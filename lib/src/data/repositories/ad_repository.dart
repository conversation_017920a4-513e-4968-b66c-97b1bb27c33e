import 'dart:async';
import 'dart:developer';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/config_param.dart';
import 'package:wordle/src/core/helpers/app_admob.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/repositories/ironsource_ad_repository.dart';

class AdRepository {
  final _localStorage = ServiceLocator.locate<LocalStorage>();
  final _ironSourceAdRepository =
      ServiceLocator.locate<IronSourceAdRepository>();

  int frequency = 3;
  int _gamesPlayedCount = 0;

  AdRepository() {
    frequency = FirebaseRemoteConfig.instance
        .getInt(ConfigParam.interstitialAdFrequency);

    if (frequency == 0) {
      frequency = 2;
    }

    _gamesPlayedCount = _localStorage.retrieveInt(LSKey.unlimitedGamesSinceAds);
  }

  static bool isSubscribed = false;
  InterstitialAd? _dailyModeAd;
  InterstitialAd? _unlimitedModeAd;

  bool get isAdAvailable {
    return _gamesPlayedCount != 0 && (_gamesPlayedCount % frequency) == 0;
  }

  bool get _adsNotRemoved =>
      (!isSubscribed && !_ironSourceAdRepository.isPlayPassAvailable);

  Future<void> loadSingleInterstialAd(AdInterstitialUnitId unitId) {
    final adCompleter = Completer<void>();

    if (_adsNotRemoved) {
      InterstitialAd.load(
        adUnitId: AppAdmob.getInterstitialAdUnitId(unitId),
        request: AdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (ad) {
            _dailyModeAd = ad;
            _dailyModeAd!.fullScreenContentCallback = FullScreenContentCallback(
              onAdDismissedFullScreenContent: (InterstitialAd ad) {
                log('onAdDismissedFullScreenContent.');
                ad.dispose();
              },
            );

            adCompleter.complete();
            log("Single ad loaded: ${ad.responseInfo?.mediationAdapterClassName}");
          },
          onAdFailedToLoad: (error) {
            log("Interstitial Ad Failed to Load: ${error.message}");
            adCompleter.completeError(Exception(error.message));
          },
        ),
      );
    } else {
      adCompleter.complete();
    }

    return adCompleter.future;
  }

  Future<void> showDailyAd(AdInterstitialUnitId unitId,
      {int delayTimeInMilliseconds = 0}) async {
    if (_adsNotRemoved) {
      if (_dailyModeAd != null) {
        Future.delayed(Duration(milliseconds: delayTimeInMilliseconds))
            .then((value) async {
          await _dailyModeAd!.show();
          _dailyModeAd = null;
        });
      } else {
        loadAndShowSingleInterstialAd(unitId);
      }
    }
  }

  Future<void> loadAndShowSingleInterstialAd(AdInterstitialUnitId unitId) {
    final adCompleter = Completer<void>();
    if (_adsNotRemoved) {
      InterstitialAd.load(
        adUnitId: AppAdmob.getInterstitialAdUnitId(unitId),
        request: AdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (ad) async {
            _dailyModeAd = ad;
            _dailyModeAd!.fullScreenContentCallback = FullScreenContentCallback(
              onAdDismissedFullScreenContent: (InterstitialAd ad) {
                log('onAdDismissedFullScreenContent.');
                adCompleter.complete();
                ad.dispose();
              },
            );

            await _dailyModeAd!.show();
            _dailyModeAd = null;
            log("Single ad loaded: ${ad.responseInfo?.mediationAdapterClassName}");
          },
          onAdFailedToLoad: (error) {
            log("Interstitial Ad Failed to Load: ${error.message}");
            adCompleter.completeError(Exception(error.message));
          },
        ),
      );
    } else {
      adCompleter.complete();
    }

    return adCompleter.future;
  }

  /// This method should be called at the start of every level.
  /// How often the ad shows will be defined by the frequency
  /// which is configurable.
  Future<void> loadFrequentInterstitialAd(AdInterstitialUnitId unitId) async {
    final adCompleter = Completer<void>();
    if (isAdAvailable && _adsNotRemoved) {
      InterstitialAd.load(
        adUnitId: AppAdmob.getInterstitialAdUnitId(unitId),
        request: AdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (InterstitialAd ad) {
            _unlimitedModeAd = ad;

            _unlimitedModeAd!.fullScreenContentCallback =
                FullScreenContentCallback(
              onAdDismissedFullScreenContent: (InterstitialAd ad) {
                log('onAdDismissedFullScreenContent.');
                ad.dispose();
              },
            );

            adCompleter.complete();
            log("Frequent ad loaded: ${ad.responseInfo?.mediationAdapterClassName}");
          },
          onAdFailedToLoad: (LoadAdError error) {
            log("Interstitial Ad Failed to Load: ${error.message}");
            adCompleter.completeError(Exception(error.message));
          },
        ),
      );
    } else {
      adCompleter.complete();
    }

    return adCompleter.future;
  }

  Future<void> showUnlimitedAd(AdInterstitialUnitId unitId,
      {int delayTimeInMilliseconds = 0}) async {
    if (_adsNotRemoved) {
      if (_unlimitedModeAd != null) {
        Future.delayed(Duration(milliseconds: delayTimeInMilliseconds))
            .then((value) async {
          await _unlimitedModeAd!.show();
          _unlimitedModeAd = null;
        });
      } else {
        loadAndShowFrequentInterstitialAd(unitId);
      }
    }
  }

  Future<void> loadAndShowFrequentInterstitialAd(
      AdInterstitialUnitId unitId) async {
    final adCompleter = Completer<void>();
    if (isAdAvailable && _adsNotRemoved) {
      InterstitialAd.load(
        adUnitId: AppAdmob.getInterstitialAdUnitId(unitId),
        request: AdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (InterstitialAd ad) {
            _unlimitedModeAd = ad;

            _unlimitedModeAd!.fullScreenContentCallback =
                FullScreenContentCallback(
              onAdDismissedFullScreenContent: (InterstitialAd ad) {
                log('onAdDismissedFullScreenContent.');
                adCompleter.complete();
                ad.dispose();
              },
            );

            _unlimitedModeAd!.show();
            _unlimitedModeAd = null;
            log("Frequent ad loaded: ${ad.responseInfo?.mediationAdapterClassName}");
          },
          onAdFailedToLoad: (LoadAdError error) {
            log("Interstitial Ad Failed to Load: ${error.message}");
            adCompleter.completeError(Exception(error.message));
          },
        ),
      );
    } else {
      adCompleter.complete();
    }

    return adCompleter.future;
  }

  /// Load and show a rewarded ad with no server-side verification
  Future<num> loadAndShowRewardedAdNoSSV(AdRewardUnitId unitId) async {
    final adCompleter = Completer<num>();
    RewardedAd.load(
      adUnitId: AppAdmob.getRewardAdUnitId(unitId),
      request: AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (RewardedAd ad) {
          ad.show(onUserEarnedReward: (adWithoutView, reward) {
            log("Reward: ${reward.amount}");
            adCompleter.complete(reward.amount);
            ad.dispose();
          });
          log("Rewarded ad loaded: ${ad.responseInfo?.mediationAdapterClassName}");
        },
        onAdFailedToLoad: (loadAdError) {
          log("Rewarded Ad Failed to Load: ${loadAdError.message}");
          adCompleter.completeError(Exception(loadAdError.message));
        },
      ),
    );

    return adCompleter.future;
  }

  Future<NativeAd> loadAndShowNativeAd(AdNativeUnitId unitId,
      {TemplateType templateType = TemplateType.medium,
      required BuildContext context}) async {
    final adCompleter = Completer<NativeAd>();

    NativeAd(
      adUnitId: AppAdmob.getNativeAdUnitId(unitId),
      request: const AdRequest(),
      listener: NativeAdListener(
        onAdLoaded: (ad) {
          NativeAd nativeAd = ad as NativeAd;
          adCompleter.complete(nativeAd);
          log("Native ad loaded: ${nativeAd.responseInfo?.mediationAdapterClassName}");
        },
        onAdFailedToLoad: (ad, error) {
          log("Native Ad Failed to Load: ${error.message}");
          adCompleter.completeError(Exception(error.message));
          ad.dispose();
        },
        onPaidEvent: (ad, valueMicros, precision, currencyCode) {
          MethodChannel platform =
              MethodChannel('com.chinloyal.wordle/channel');
          platform.invokeMethod('logRevenueEventInSingularAndFirebase', {
            'value': valueMicros / 1000000,
            'precision': precision.name,
            'currency': currencyCode,
            'adSource':
                ad.responseInfo?.loadedAdapterResponseInfo?.adSourceName ?? "",
            'adSourceInstance': ad.responseInfo?.loadedAdapterResponseInfo
                    ?.adSourceInstanceName ??
                "",
            'adUnit': "native",
          });
        },
      ),
      nativeTemplateStyle: NativeTemplateStyle(
        // Required: Choose a template.
        templateType: templateType,
        // Optional: Customize the ad's style.
        mainBackgroundColor: AppColors.of(context).colorTone7,
        cornerRadius: 10.0,
        callToActionTextStyle: NativeTemplateTextStyle(
          textColor: Colors.white,
          backgroundColor: AppColors.of(context).correct,
          style: NativeTemplateFontStyle.monospace,
          size: 16.0,
        ),
        primaryTextStyle: NativeTemplateTextStyle(
          textColor: AppColors.of(context).colorTone1,
          // backgroundColor: Colors.cyan,
          style: NativeTemplateFontStyle.bold,
          size: 16.0,
        ),
        secondaryTextStyle: NativeTemplateTextStyle(
          textColor: AppColors.of(context).colorTone1,
          // backgroundColor: Colors.black,
          style: NativeTemplateFontStyle.bold,
          size: 16.0,
        ),
        tertiaryTextStyle: NativeTemplateTextStyle(
          textColor: AppColors.of(context).colorTone1,
          backgroundColor: AppColors.of(context).colorTone7,
          style: NativeTemplateFontStyle.normal,
          size: 16.0,
        ),
      ),
    ).load();

    return adCompleter.future;
  }

  void trackGamesPlayed() {
    _gamesPlayedCount++;
    _localStorage.saveInt(LSKey.unlimitedGamesSinceAds, _gamesPlayedCount);
  }

  void resetGamesPlayed() {
    _gamesPlayedCount = 0;
    _localStorage.saveInt(LSKey.unlimitedGamesSinceAds, _gamesPlayedCount);
  }
}
