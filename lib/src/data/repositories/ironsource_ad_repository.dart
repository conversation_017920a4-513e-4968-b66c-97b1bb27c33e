import 'dart:async';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/services.dart';
import 'package:wordle/src/core/constants/config_param.dart';
import 'package:wordle/src/core/helpers/app_admob.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';

class IronSourceAdRepository {
  final _localStorage = ServiceLocator.locate<LocalStorage>();

  int frequency = 3;
  int _gamesPlayedCount = 0;

  IronSourceAdRepository() {
    frequency = FirebaseRemoteConfig.instance
        .getInt(ConfigParam.interstitialAdFrequency);

    if (frequency == 0) {
      frequency = 2;
    }

    _gamesPlayedCount = _localStorage.retrieveInt(LSKey.unlimitedGamesSinceAds);
  }

  static bool isSubscribed = false;
  static bool isPlayPass = false;
  static bool isUnlimitedModeAdLoaded = false;
  static bool isDailyModeAdLoaded = false;

  static MethodChannel platform = MethodChannel('com.chinloyal.wordle/channel');

  bool get isAdAvailable {
    return _gamesPlayedCount != 0 && (_gamesPlayedCount % frequency) == 0;
  }

  bool get isPlayPassAvailable => isPlayPass;

  bool get _adsNotRemoved => (!isSubscribed && !isPlayPass);

  Future<void> loadSingleInterstialAd(AdInterstitialUnitId unitId) {
    final adCompleter = Completer<void>();

    if (_adsNotRemoved) {
      platform.invokeMethod('loadInterstitialAd').then((value) {
        if (value == "DONE") {
          adCompleter.complete();
          isDailyModeAdLoaded = true;
        }
      }).onError((error, stackTrace) {
        adCompleter.completeError(Exception("Load InterstitialAd Failed"));
      });
    } else {
      adCompleter.complete();
    }
    return adCompleter.future;
  }

  Future<void> showDailyAd(AdInterstitialUnitId unitId,
      {int delayTimeInMilliseconds = 0}) async {
    if (_adsNotRemoved) {
      if (isDailyModeAdLoaded) {
        Future.delayed(Duration(milliseconds: delayTimeInMilliseconds))
            .then((value) async {
          platform.invokeMethod('showInterstitialAd').then((value) {
            if (value == "DONE") {}
          });
          isUnlimitedModeAdLoaded = false;
        });
      } else {
        _loadAndShowSingleInterstialAd(unitId);
      }
    }
  }

  Future<void> _loadAndShowSingleInterstialAd(AdInterstitialUnitId unitId) {
    final adCompleter = Completer<void>();

    if (_adsNotRemoved) {
      platform.invokeMethod('loadInterstitialAd').then((value) {
        if (value == "DONE") {
          platform.invokeMethod('showInterstitialAd').then((value) {
            if (value == "DONE") {
              adCompleter.complete();
            }
          }).onError((error, stackTrace) {
            adCompleter.completeError(Exception("Show InterstitialAd Failed"));
          });
        }
      }).onError((error, stackTrace) {
        adCompleter.completeError(Exception("Load InterstitialAd Failed"));
      });
    } else {
      adCompleter.complete();
    }

    return adCompleter.future;
  }

  /// This method should be called at the start of every level.
  /// How often the ad shows will be defined by the frequency
  /// which is configurable.
  Future<void> loadFrequentInterstitialAd(AdInterstitialUnitId unitId) async {
    final adCompleter = Completer<void>();
    if (isAdAvailable && _adsNotRemoved) {
      platform.invokeMethod('loadInterstitialAd').then((value) {
        if (value == "DONE") {
          adCompleter.complete();
          isUnlimitedModeAdLoaded = true;
        }
      }).onError((error, stackTrace) {
        adCompleter.completeError(Exception("Load InterstitialAd Failed"));
      });
    } else {
      adCompleter.complete();
    }

    return adCompleter.future;
  }

  Future<void> showUnlimitedAd(AdInterstitialUnitId unitId,
      {int delayTimeInMilliseconds = 0}) async {
    if (_adsNotRemoved) {
      if (isUnlimitedModeAdLoaded) {
        Future.delayed(Duration(milliseconds: delayTimeInMilliseconds))
            .then((value) async {
          platform.invokeMethod('showInterstitialAd').then((value) {
            if (value == "DONE") {}
          });
          isUnlimitedModeAdLoaded = false;
        });
      } else {
        _loadAndShowFrequentInterstitialAd(unitId);
      }
    }
  }

  Future<void> _loadAndShowFrequentInterstitialAd(
      AdInterstitialUnitId unitId) async {
    final adCompleter = Completer<void>();
    if (isAdAvailable && _adsNotRemoved) {
      platform.invokeMethod('loadInterstitialAd').then((value) {
        if (value == "DONE") {
          platform.invokeMethod('showInterstitialAd').then((value) {
            if (value == "DONE") {
              adCompleter.complete();
            }
          }).onError((error, stackTrace) {
            adCompleter.completeError(Exception("Show InterstitialAd Failed"));
          });
        }
      }).onError((error, stackTrace) {
        adCompleter.completeError(Exception("Load InterstitialAd Failed"));
      });
    } else {
      adCompleter.complete();
    }

    return adCompleter.future;
  }

  Future<num> loadAndShowRewardedAdWithAdPlacement(
      AdRewardUnitId unitId, String placement, String userId) async {
    final adCompleter = Completer<num>();
    try {
      final result = await platform.invokeMethod('showRewardAdWithPlacement',
          {'placement': placement, 'user_id': userId});
      if (result == "DONE") {
        adCompleter.complete(0);
      }
    } on PlatformException catch (e) {
      if (e.code == 'adClosed') {
        adCompleter.complete(0);
      } else {
        adCompleter.completeError(Exception(e.message));
      }
    } catch (e) {
      adCompleter.completeError(Exception(e));
    }

    return adCompleter.future;
  }

  /// Load and show a rewarded ad with no server-side verification
  Future<num> loadAndShowRewardedAdNoSSV(AdRewardUnitId unitId) async {
    final adCompleter = Completer<num>();

    platform.invokeMethod('showRewardAd').then((value) {
      if (value == "DONE") {
        adCompleter.complete(10);
      }
    }).onError((error, stackTrace) {
      adCompleter.completeError(Exception("Show RewardAd Failed"));
    });

    return adCompleter.future;
  }

  void trackGamesPlayed() {
    _gamesPlayedCount++;
    _localStorage.saveInt(LSKey.unlimitedGamesSinceAds, _gamesPlayedCount);
  }

  void resetGamesPlayed() {
    _gamesPlayedCount = 0;
    _localStorage.saveInt(LSKey.unlimitedGamesSinceAds, _gamesPlayedCount);
  }
}
