import 'package:json_annotation/json_annotation.dart';

part 'match_status.g.dart';

@JsonSerializable()
class MatchStatus {
  String state;
  @Json<PERSON>ey(name: 'last_seen')
  int lastSeen;
  @J<PERSON><PERSON>ey(name: 'last_match_id')
  String? lastMatchId;

  MatchStatus({
    required this.state,
    required this.lastSeen,
    this.lastMatchId,
  });

  factory MatchStatus.fromJson(Map<String, dynamic> json) =>
      _$MatchStatusFromJson(json);

  Map<String, dynamic> toJson() => _$MatchStatusToJson(this);
}
