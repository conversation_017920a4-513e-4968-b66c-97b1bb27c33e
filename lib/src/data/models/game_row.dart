import 'dart:convert';

import 'package:wordle/src/core/constants/evaluation.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/models/letter_node.dart';
import 'package:wordle/src/settings/settings_controller.dart';

class GameRow {
  final List<LetterNode> _list = [];
  final settingsController = ServiceLocator.locate<SettingsController>();
  int _maxLetters = 5;

  List<LetterNode> get letterList => _list;

  GameRow({
    List<LetterNode> list = const [],
    int maxLetters = 5,
  }) {
    _list.addAll(list);
    _maxLetters = maxLetters;
  }

  factory GameRow.fromJson(Map<String, dynamic> json) {
    return GameRow(
        list: (json['list'] as List<dynamic>?)
                ?.map((e) => LetterNode.fromJson(jsonDecode(jsonEncode(e))))
                .toList() ??
            const [],
        maxLetters: json['maxLetters'] as int? ?? 5);
  }

  Map<String, dynamic> toJson() => {'list': _list, 'maxLetters': _maxLetters};

  bool get isFull => _list.length == _maxLetters;

  int get length => _list.length;

  bool get isCorrect {
    if (_list.isEmpty) return false;

    for (final node in _list) {
      if (node.evaluation != Evaluation.correct) {
        return false;
      }
    }

    return true;
  }

  void setRow(List<String> values) {
    for (final value in values) {
      push(LetterNode(letter: value, evaluation: Evaluation.tbd));
    }
  }

  void push(LetterNode node) {
    if (!isFull) {
      _list.add(node);
    }
  }

  LetterNode? pop() {
    return _list.isNotEmpty ? _list.removeLast() : null;
  }

  LetterNode? letterAt(int index) {
    if (_list.length > index) {
      return _list.elementAt(index);
    } else {
      return null;
    }
  }

  String toWord() {
    String word = '';
    for (LetterNode node in _list) {
      word += node.letter;
    }
    return word;
  }

  List<LetterNode> toList() => _list;

  String toEmojis() {
    return _list
        .map((e) {
          if (e.evaluation == Evaluation.correct) {
            return '🟩';
          } else if (e.evaluation == Evaluation.present) {
            return '🟨';
          } else {
            return settingsController.isDarkMode ? '⬛' : '⬜';
          }
        })
        .toList()
        .join('');
  }

  GameRow copy() {
    return GameRow.fromJson(toJson());
  }
}
