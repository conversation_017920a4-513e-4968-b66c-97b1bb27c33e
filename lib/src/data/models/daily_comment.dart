import 'package:json_annotation/json_annotation.dart';
import 'package:wordle/src/core/helpers/date_helper.dart';

part 'daily_comment.g.dart';

@JsonSerializable()
class DailyComment {
  final String id;
  final String comment;
  final int puzzleNumber;
  final String? displayName;
  final String photoURL;
  final int createdAt;
  final List<String> flags;

  DailyComment({
    required this.id,
    required this.comment,
    required this.puzzleNumber,
    this.displayName,
    required this.photoURL,
    required this.createdAt,
    this.flags = const [],
  });

  factory DailyComment.fromJson(Map<String, dynamic> json) =>
      _$DailyCommentFromJson(json);

  Map<String, dynamic> toJson() => _$DailyCommentToJson(this);

  String get relativeCreatedAt {
    return getRelativeTime(DateTime.fromMillisecondsSinceEpoch(createdAt));
  }
}
