import '../../router/base_arguments.dart';
import 'difficulty_config.dart';

class DifficultyItemModel {
  final DifficultyConfig difficultyConfig;
  final String name;

  DifficultyItemModel({
    required this.difficultyConfig,
    required this.name,
  });
}

class UnlimitedChallengeArgs extends BaseArguments {
  final int letter;

  UnlimitedChallengeArgs({required this.letter});
}

class DailyChallengeArgs extends BaseArguments {
  final int letter;

  DailyChallengeArgs({required this.letter});
}
