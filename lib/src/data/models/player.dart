import 'package:json_annotation/json_annotation.dart';

part 'player.g.dart';

@JsonSerializable()
class Player {
  String? displayName;
  String? photoURL;
  int? timeLeft;
  String? userId;

  Player({
    this.displayName,
    this.photoURL,
    this.timeLeft,
    this.userId,
  });

  factory Player.fromJson(Map<String, dynamic> json) => _$PlayerFromJson(json);

  Map<String, dynamic> toJson() => _$PlayerToJson(this);
}
