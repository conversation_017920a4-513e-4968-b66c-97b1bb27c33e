import 'package:wordle/src/core/constants/evaluation.dart';

import 'package:json_annotation/json_annotation.dart';

part 'letter_node.g.dart';

@JsonSerializable()
class LetterNode {
  String letter;
  Evaluation? evaluation;

  @override
  bool operator ==(Object other) =>
      identical(this, other) || (other as LetterNode).letter == letter;

  LetterNode({
    required this.letter,
    this.evaluation,
  });

  factory LetterNode.fromJson(Map<String, dynamic> json) =>
      _$LetterNodeFromJson(json);

  Map<String, dynamic> toJson() => _$LetterNodeToJson(this);

  @override
  int get hashCode => letter.hashCode;
}
