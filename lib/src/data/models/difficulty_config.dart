import 'dart:ui';

import 'package:json_annotation/json_annotation.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/enums/app_enums.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/model/game_type_model.dart';

import '../../core/utils/service_locator.dart';
import '../../domain/main/stores/main_store.dart';

part 'difficulty_config.g.dart';

@JsonSerializable()
class DifficultyConfig {
  final int lettersCount;
  final int rowCount;

  DifficultyConfig({
    required this.lettersCount,
    required this.rowCount,
  });

  factory DifficultyConfig.fromJson(Map<String, dynamic> json) =>
      _$DifficultyConfigFromJson(json);

  Map<String, dynamic> toJson() => _$DifficultyConfigToJson(this);

  final mainStore = ServiceLocator.locate<MainStore>();

  GameTypeModel get unlimitedStatsType {
    GameTypeModel gameTypeModel = GameTypeModel(
      gameName: GameTypeEnum.fiveLetterUnlimited.name,
      gameType: GameTypeEnum.fiveLetterUnlimited.type,
    );
    if (lettersCount == 5) {
      gameTypeModel = GameTypeModel(
        gameName: GameTypeEnum.fiveLetterUnlimited.name,
        gameType: GameTypeEnum.fiveLetterUnlimited.type,
      );
    } else if (lettersCount == 6) {
      gameTypeModel = GameTypeModel(
        gameName: GameTypeEnum.sixLetterUnlimited.name,
        gameType: GameTypeEnum.sixLetterUnlimited.type,
      );
    } else if (lettersCount == 7) {
      gameTypeModel = GameTypeModel(
        gameName: GameTypeEnum.sevenLetterUnlimited.name,
        gameType: GameTypeEnum.sevenLetterUnlimited.type,
      );
    }

    return gameTypeModel;
  }

  GameTypeModel get dailyStatsType {
    GameTypeModel gameTypeModel = GameTypeModel(
      gameName: GameTypeEnum.fiveLetterDaily.name,
      gameType: GameTypeEnum.fiveLetterDaily.type,
    );
    if (lettersCount == 5) {
      gameTypeModel = GameTypeModel(
        gameName: GameTypeEnum.fiveLetterDaily.name,
        gameType: GameTypeEnum.fiveLetterDaily.type,
      );
    } else if (lettersCount == 6) {
      gameTypeModel = GameTypeModel(
        gameName: GameTypeEnum.sixLetterDaily.name,
        gameType: GameTypeEnum.sixLetterDaily.type,
      );
    } else if (lettersCount == 7) {
      gameTypeModel = GameTypeModel(
        gameName: GameTypeEnum.sevenLetterDaily.name,
        gameType: GameTypeEnum.sevenLetterDaily.type,
      );
    }

    return gameTypeModel;
  }

  LSKey get wordlistLastSolvedKey {
    var key = LSKey.wordlist6LastSolved;
    if (lettersCount == 6) {
      key = LSKey.wordlist6LastSolved;
    } else if (lettersCount == 7) {
      key = LSKey.wordlist7LastSolved;
    }

    return key;
  }

  LSKey get unlimitedGameSessionKey {
    var key = LSKey.unlimitedGameSession;
    if (lettersCount == 5) {
      key = mainStore.isSpeedMode
          ? LSKey.unlimitedGameSpeedSession
          : LSKey.unlimitedGameSession;
    } else if (lettersCount == 6) {
      key = mainStore.isSpeedMode
          ? LSKey.unlimitedGameSpeedSession6
          : LSKey.unlimitedGameSession6;
    } else if (lettersCount == 7) {
      key = mainStore.isSpeedMode
          ? LSKey.unlimitedGameSpeedSession7
          : LSKey.unlimitedGameSession7;
    }
    return key;
  }

  LSKey get dailyGameSessionKey {
    var key = LSKey.dailyGameSession;
    if (lettersCount == 5) {
      key = mainStore.isSpeedMode
          ? LSKey.dailyGameSpeedSession
          : LSKey.dailyGameSession;
    } else if (lettersCount == 6) {
      key = mainStore.isSpeedMode
          ? LSKey.dailyGameSpeedSession6
          : LSKey.dailyGameSession6;
    } else if (lettersCount == 7) {
      key = mainStore.isSpeedMode
          ? LSKey.dailyGameSpeedSession7
          : LSKey.dailyGameSession7;
    }
    return key;
  }

  String get difficultyTitle {
    var title = "CLASSIC";
    if (lettersCount == 5) {
      return title;
    } else if (lettersCount == 6) {
      title = "INTERMEDIATE";
    } else if (lettersCount == 7) {
      title = "HARD";
    }

    return title;
  }

  Color get difficultyColor {
    var color = AppColors.green;
    if (lettersCount == 5) {
      color = AppColors.green;
    } else if (lettersCount == 6) {
      color = AppColors.darkYellow;
    } else if (lettersCount == 7) {
      color = AppColors.red;
    }
    return color;
  }
}
