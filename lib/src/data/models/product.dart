import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:wordle/src/core/constants/product_type.dart';

part 'product.g.dart';

@JsonSerializable()
class Product {
  final String id;
  final String price;
  final ProductType type;
  final String? label;
  final String? imagePath;

  Product({
    required this.id,
    required this.price,
    required this.type,
    this.label,
    this.imagePath,
  });

  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);

  Map<String, dynamic> toJson() => _$ProductToJson(this);

  @JsonKey(includeFromJson: false)
  ProductDetails? details;
}
