import 'package:json_annotation/json_annotation.dart';

part 'daily_wordle.g.dart';

@JsonSerializable()
class DailyWordle {
  int id;
  String solution;

  @JsonKey(name: 'days_since_launch', defaultValue: 0)
  int? daysSinceLaunch;

  DailyWordle({
    required this.id,
    required this.solution,
    required this.daysSinceLaunch,
  });

  factory DailyWordle.fromJson(Map<String, dynamic> json) =>
      _$DailyWordleFromJson(json);

  Map<String, dynamic> toJson() => _$DailyWordleToJson(this);
}
