import 'package:json_annotation/json_annotation.dart';
import 'package:wordle/src/core/constants/game_result.dart';
import 'package:wordle/src/data/models/game_row.dart';
import 'package:wordle/src/data/models/letter_node.dart';

part 'daily_game_session.g.dart';

@JsonSerializable()
class DailyGameSession {
  GameRow currentGameRow;
  int currentRowNumber;
  List<GameRow> submittedRows;
  GameResult? gameResult;
  String errorMessage;
  Set<LetterNode> lettersUsed;
  bool hasGameEnded;
  int startedAt;
  String? gameId;
  Map<int, LetterNode?> hints;
  String? wordOfTheSession;

  int handHintCount;
  int handHintCountAd;
  int bulbHintCount;
  int bulbHintCountAd;
  String userId;
  bool isBoosterUsed;

  DailyGameSession({
    required this.currentGameRow,
    this.currentRowNumber = 1,
    this.submittedRows = const [],
    this.gameResult,
    this.errorMessage = '',
    this.gameId,
    this.wordOfTheSession,
    this.startedAt = 0,
    this.hints = const {},
    this.handHintCount = 0,
    this.handHintCountAd = 0,
    this.bulbHintCount = 0,
    this.bulbHintCountAd = 0,
    this.userId = "",
    this.lettersUsed = const {},
    this.hasGameEnded = false,
    this.isBoosterUsed = false,
  });

  factory DailyGameSession.fromJson(Map<String, dynamic> json) =>
      _$DailyGameSessionFromJson(json);

  Map<String, dynamic> toJson() => _$DailyGameSessionToJson(this);
}
