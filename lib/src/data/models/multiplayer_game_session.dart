import 'package:wordle/src/core/constants/game_result.dart';
import 'package:wordle/src/data/models/game_row.dart';
import 'package:wordle/src/data/models/letter_node.dart';

import 'package:json_annotation/json_annotation.dart';

part 'multiplayer_game_session.g.dart';

@JsonSerializable()
class MultiplayerGameSession {
  GameRow currentGameRow;
  int currentRowNumber;
  List<GameRow> submittedRows;
  GameResult? gameResult;
  String errorMessage;
  Set<LetterNode> lettersUsed;
  bool hasGameEnded;
  String? wordOfTheSession;

  MultiplayerGameSession({
    required this.currentGameRow,
    this.wordOfTheSession,
    this.currentRowNumber = 1,
    this.submittedRows = const [],
    this.gameResult,
    this.errorMessage = '',
    this.lettersUsed = const {},
    this.hasGameEnded = false,
  });

  factory MultiplayerGameSession.fromJson(Map<String, dynamic> json) =>
      _$MultiplayerGameSessionFromJson(json);

  Map<String, dynamic> toJson() => _$MultiplayerGameSessionToJson(this);
}
