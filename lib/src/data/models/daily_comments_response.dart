import 'package:wordle/src/data/models/daily_comment.dart';
import 'package:json_annotation/json_annotation.dart';

part 'daily_comments_response.g.dart';

@JsonSerializable(createFactory: false)
class DailyCommentsResponse {
  final List<DailyComment> items;
  final int pageCount;

  DailyCommentsResponse({
    this.items = const [],
    this.pageCount = 0,
  });

  factory DailyCommentsResponse.fromJson(Map<String, dynamic> json) =>
      DailyCommentsResponse(
        items: (json['items'] as List<dynamic>?)
                ?.map((e) => DailyComment.fromJson(Map.from(e)))
                .toList() ??
            const [],
        pageCount: json['pageCount'] as int? ?? 0,
      );

  Map<String, dynamic> toJson() => _$DailyCommentsResponseToJson(this);
}
