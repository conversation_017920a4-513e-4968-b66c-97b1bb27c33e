import 'package:json_annotation/json_annotation.dart';

part 'user_score.g.dart';

@JsonSerializable()
class UserScore {
  String? playerName;
  String? avatarUrl;
  int? gamesDrawed;
  int? gamesLost;
  int? gamesPlayed;
  int? gamesWon;
  int? points;
  String? userId;

  UserScore({
    this.playerName,
    this.avatarUrl,
    this.gamesDrawed,
    this.gamesLost,
    this.gamesPlayed,
    this.gamesWon,
    this.points,
    this.userId,
  });

  factory UserScore.fromJson(Map<String, dynamic> json) =>
      _$UserScoreFromJson(json);

  Map<String, dynamic> toJson() => _$UserScoreToJson(this);
}
