import 'package:json_annotation/json_annotation.dart';
import 'package:wordle/src/core/constants/game_result.dart';
import 'package:wordle/src/core/enums/app_enums.dart';
import 'package:wordle/src/data/models/game_row.dart';
import 'package:wordle/src/data/models/letter_node.dart';

part 'unlimited_game_session.g.dart';

@JsonSerializable()
class UnlimitedGameSession {
  GameRow currentGameRow;
  int currentRowNumber;
  List<GameRow> submittedRows;
  GameResult? gameResult;

  String errorMessage;
  Set<LetterNode> lettersUsed;
  bool isUsedExtraRow;
  bool hasGameEnded;
  int? startedAt;
  String? gameId;
  int? wordOfTheSessionIndex;
  Map<int, LetterNode?> hints;
  String? wordOfTheSession;

  int handHintCount;
  int handHintCountAd;
  int bulbHintCount;
  int bulbHintCountAd;
  String userId;
  KeepPlayingPopupType keepPlayingPopupType;
  bool isBoosterUsed;

  UnlimitedGameSession({
    required this.currentGameRow,
    this.wordOfTheSession,
    this.wordOfTheSessionIndex,
    this.gameId,
    this.startedAt,
    this.currentRowNumber = 1,
    this.submittedRows = const [],
    this.gameResult,
    this.errorMessage = '',
    this.lettersUsed = const {},
    this.hasGameEnded = false,
    this.isUsedExtraRow = false,
    this.hints = const {},
    this.handHintCount = 0,
    this.handHintCountAd = 0,
    this.bulbHintCount = 0,
    this.bulbHintCountAd = 0,
    this.userId = "",
    this.keepPlayingPopupType = KeepPlayingPopupType.none,
    this.isBoosterUsed = false,
  });

  factory UnlimitedGameSession.fromJson(Map<String, dynamic> json) =>
      _$UnlimitedGameSessionFromJson(json);

  Map<String, dynamic> toJson() => _$UnlimitedGameSessionToJson(this);
}
