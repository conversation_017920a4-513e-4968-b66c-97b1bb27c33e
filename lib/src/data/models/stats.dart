import 'package:json_annotation/json_annotation.dart';

import '../../model/get_speed_game_details_model.dart';
import '../../model/get_speed_game_summary_model.dart';

part 'stats.g.dart';

@JsonSerializable()
class Stats {
  int gamesPlayed;
  int gamesWon;
  num winPercentage;
  int maxStreak;
  int currentStreak;
  Map<int, int> guessDistribution;
  Map<int, double> gameSummary;

  GetSpeedGameDetailsModel? getSpeedGameDetailsModel;
  GetSpeedGameSummaryModel? getSpeedGameSummaryModel;

  int bestPoints;
  int averagePoints;
  int bestUsedSeconds;
  int averageUsedSeconds;

  Stats({
    this.gamesPlayed = 0,
    this.gamesWon = 0,
    this.winPercentage = 0,
    this.maxStreak = 0,
    this.currentStreak = 0,
    this.guessDistribution = const {1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0},
    this.gameSummary = const {1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0, 7: 0},
    this.getSpeedGameDetailsModel,
    this.getSpeedGameSummaryModel,
    this.bestPoints = 0,
    this.averagePoints = 0,
    this.bestUsedSeconds = 0,
    this.averageUsedSeconds = 0,
  });

  factory Stats.fromJson(Map<String, dynamic> json) => _$StatsFromJson(json);

  Map<String, dynamic> toJson() => _$StatsToJson(this);

  String get bestUsedTimeFormatted {
    final minutes = (bestUsedSeconds ~/ 60).toString().padLeft(2, '0');
    final seconds = (bestUsedSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  String get averageUsedTimeFormatted {
    final minutes = (averageUsedSeconds ~/ 60).toString().padLeft(2, '0');
    final seconds = (averageUsedSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }
}
