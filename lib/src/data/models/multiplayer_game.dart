import 'package:json_annotation/json_annotation.dart';
import 'package:wordle/src/data/models/player.dart';

part 'multiplayer_game.g.dart';

@JsonSerializable()
class MultiplayerGame {
  Player? player1;
  Player? player2;
  int? playerCount;
  String? turn;
  List<String>? guesses;
  String? solution;
  String? winner;

  MultiplayerGame({
    this.player1,
    this.player2,
    this.playerCount,
    this.turn,
    this.guesses = const [],
    this.solution,
    this.winner,
  });

  factory MultiplayerGame.fromJson(Map<String, dynamic> json) =>
      _$MultiplayerGameFromJson(json);

  Map<String, dynamic> toJson() => _$MultiplayerGameToJson(this);
}
