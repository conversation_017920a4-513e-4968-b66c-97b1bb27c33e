// Package imports:
import 'package:shared_preferences/shared_preferences.dart';

class LocalStorage {
  static late SharedPreferences _prefs;

  static Future<LocalStorage> create() async {
    LocalStorage._prefs = await SharedPreferences.getInstance();
    return LocalStorage();
  }

  /// Use this retrieve method to retrieve data from storage,
  /// be sure to add your key to LSKey enum
  String retrieve(LSKey key) {
    return LocalStorage._prefs.getString(key.toString()) ?? '';
  }

  /// Use this new method to save data to storage,
  /// be sure to add your key to LSKey enum
  Future<void> save(LSKey key, String data) async {
    await LocalStorage._prefs.setString(key.toString(), data);
  }

  bool? retrieveBool(LSKey key) {
    return LocalStorage._prefs.getBool(key.toString());
  }

  Future<void> saveBool(LSKey key, bool data) async {
    await LocalStorage._prefs.setBool(key.toString(), data);
  }

  int retrieveInt(<PERSON><PERSON><PERSON><PERSON> key) {
    return LocalStorage._prefs.getInt(key.toString()) ?? 0;
  }

  Future<void> saveInt(LSKey key, int data) async {
    await LocalStorage._prefs.setInt(key.toString(), data);
  }

  double retrieveDouble(LSKey key) {
    return LocalStorage._prefs.getDouble(key.toString()) ?? 0.0;
  }

  Future<void> saveDouble(LSKey key, double data) async {
    await LocalStorage._prefs.setDouble(key.toString(), data);
  }

  void remove(LSKey key) {
    LocalStorage._prefs.remove(key.toString());
  }

  void clearAll({List<LSKey>? exclude}) {
    if (exclude == null) {
      LocalStorage._prefs.clear();
      return;
    }

    for (final key in LSKey.values) {
      if (!exclude.contains(key)) {
        remove(key);
      }
    }
  }

  bool containsKey(LSKey key) {
    return LocalStorage._prefs.containsKey(key.toString());
  }
}

/// Define all keys used in local storage under this enum, this prevents passing
/// around strings as keys which can easily cause errors that are difficult to
/// debug if strings are mistyped. (LocalStorageKey)
enum LSKey {
  accessToken,
  refreshToken,
  isFirstLaunch,
  // daily stats, DO NOT RENAME, USERS WOULD LOSE DAILY STATS
  stats,
  unlimitedStats,
  unlimitedStats6,
  unlimitedStats7,
  dailyGameSession,
  dailyGameSession6,
  dailyGameSession7,
  dailyGameSpeedSession,
  dailyGameSpeedSession6,
  dailyGameSpeedSession7,
  // Represents default difficulty for unlimited (5 letters)
  unlimitedGameSession,
  unlimitedGameSession6,
  unlimitedGameSession7,
  unlimitedGameSpeedSession,
  unlimitedGameSpeedSession6,
  unlimitedGameSpeedSession7,
  themeMode,
  lastPuzzlePlayed,
  reviewRequested,
  isEnterOnLeft,
  keyboardLayout,
  isHighContrast,
  isFullScreen,
  // First time opening multiplayer
  isFirstMultiplayerLaunch,
  isTermsAccepted,
  // Is user signed-in on facebook, google or twitter
  hasSignedInOnSocial,
  unlimitedGamesSinceAds,
  wordlist6LastSolved,
  wordlist7LastSolved,
  wordlist6Completed,
  wordlist7Completed,
  difficultyConfig,
  // The new bubble on entrance screen
  newFeatureUnlimitedDifficultyOnEntrance,
  // The persistent badge on unlimited screen
  newFeatureUnlimitedDifficultyOnUnlimited,
  isFirstLaunchPlayPass,
  oldUserId,
  //notificationPopup
  lastNotificationPopupShownKey,
  shouldShowTransferIapPopup,
}
