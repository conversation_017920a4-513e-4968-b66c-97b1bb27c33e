import 'package:get_it/get_it.dart';
import 'package:wordle/src/core/http/api_registry.dart';
import 'package:wordle/src/core/repository_registry.dart';
import 'package:wordle/src/core/store/store_registry.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/word_list.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/services/billing/billing_service.dart';
import 'package:wordle/src/services/events/event_logger.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';
import 'package:wordle/src/services/fcm/fcm_service.dart';
import 'package:wordle/src/services/permission/permission_service.dart';
import 'package:wordle/src/services/permission/permission_service_contract.dart';
import 'package:wordle/src/services/remote_config/remote_config_service.dart';
import 'package:wordle/src/services/remote_config/remote_config_service_contract.dart';
import 'package:wordle/src/services/stats_service.dart';
import 'package:wordle/src/settings/settings_controller.dart';
import 'package:wordle/src/settings/settings_service.dart';

class ServiceLocator {
  static GetIt locate = GetIt.instance;

  static Future<void> register() async {
    final localStorage = await LocalStorage.create();
    locate.registerLazySingleton<LocalStorage>(() => localStorage);
    locate.registerLazySingleton<EventLoggerContract>(
      () => EventLogger(),
    );
    locate.registerLazySingleton<FcmService>(
      () => FcmService(),
    );
    locate.registerLazySingleton<RemoteConfigServiceContract>(
      () => RemoteConfigService(),
    );

    locate.registerLazySingleton<BillingService>(() => BillingService());
    locate.registerLazySingleton<PermissionServiceContract>(
      () => PermissionService(),
    );
    locate.registerLazySingleton<WordList>(() => WordList());
    locate.registerLazySingleton<StatsService>(() => StatsService());
    locate.registerLazySingleton<SettingsController>(
      () => SettingsController(SettingsService()),
    );

    locate.registerLazySingleton<AuthService>(() => AuthService());

    ApiRegistry();
    RepositoryRegistry();
    StoreRegistry();
  }
}
