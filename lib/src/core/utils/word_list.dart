import 'dart:convert';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:wordle/src/core/constants/config_param.dart';
import 'package:wordle/src/core/constants/evaluation.dart';
import 'package:wordle/src/core/helpers/random.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/models/difficulty_config.dart';
import 'package:wordle/src/data/models/letter_node.dart';
import 'package:wordle/src/services/remote_config/remote_config_service_contract.dart';

List<String> _parseJsonInBackground(String json) {
  return List.from(jsonDecode(json));
}

class WordList {
  List<String> wordsDaily = [];
  List<String> wordsUnlimited = [];
  List<String> wordList6 = [];
  List<String> wordList7 = [];
  List<String> dictionary = [];
  List<String> dictionary6 = [];
  List<String> dictionary7 = [];
  final configService = ServiceLocator.locate<RemoteConfigServiceContract>();
  final _localStorage = ServiceLocator.locate<LocalStorage>();

  static const dailyWordListPath = 'assets/json/en_word_list.json';
  static const wordList6Path = 'assets/json/en_word_list_6.json';
  static const wordList7Path = 'assets/json/en_word_list_7.json';
  static const unlimitedWordListPath = 'assets/json/unlimited_word_list.json';
  static const dictionaryPath = 'assets/json/en_dictionary.json';
  static const dictionary6Path = 'assets/json/en_dictionary_6.json';
  static const dictionary7Path = 'assets/json/en_dictionary_7.json';

  Future<void> loadWords() async {
    try {
      final unlimitedWordListJson =
          await rootBundle.loadString(unlimitedWordListPath);
      final wordList6Json = await rootBundle.loadString(wordList6Path);
      final wordList7Json = await rootBundle.loadString(wordList7Path);
      final dictionaryJson = await rootBundle.loadString(dictionaryPath);
      final dictionary6Json = await rootBundle.loadString(dictionary6Path);
      final dictionary7Json = await rootBundle.loadString(dictionary7Path);

      final results = await Future.wait([
        compute(
            _parseJsonInBackground,
            configService.getJson(
              ConfigParam.enWordlist,
            )),
        compute(_parseJsonInBackground, unlimitedWordListJson),
        compute(_parseJsonInBackground, wordList6Json),
        compute(_parseJsonInBackground, wordList7Json),
        compute(_parseJsonInBackground, dictionaryJson),
        compute(_parseJsonInBackground, dictionary6Json),
        compute(_parseJsonInBackground, dictionary7Json),
      ]);

      wordsDaily = results[0];
      wordsUnlimited = results[1];
      wordList6 = results[2];
      wordList7 = results[3];
      dictionary = results[4];
      dictionary6 = results[5];
      dictionary7 = results[6];
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  bool wordExists(String word) {
    if (word.length == 5) {
      return dictionary.contains(word) ||
          wordsDaily.contains(word) ||
          wordsUnlimited.contains(word);
    } else if (word.length == 6) {
      return dictionary6.contains(word);
    } else if (word.length == 7) {
      return dictionary7.contains(word);
    } else {
      return false;
    }
  }

  UnlimitedWordModel get _newWord {
    try {
      final milliSeconds = DateTime.now().millisecondsSinceEpoch;
      final rand = Random(milliSeconds);
      final tempUnlimited = wordsUnlimited;

      // Take words already used from daily and add to unlimited, except the word of the day
      tempUnlimited.addAll(wordsDaily.take(puzzleNumber - 1));

      final seed = rand.nextInt(tempUnlimited.length % milliSeconds);
      return UnlimitedWordModel(
          word: tempUnlimited.elementAt(seed), index: seed);
    } catch (e) {
      rethrow;
    }
  }

  /// Choose a word from the bottom of the list and continue to the top,
  UnlimitedWordModel newWordReverseAlgorithm(
    List<String> wordlist, {
    required LSKey lastSolvedKey,
    required LSKey wordlistCompletedKey,
  }) {
    final lastWordSolved = _localStorage.retrieve(lastSolvedKey);
    final wordListCompleted = _localStorage.retrieveBool(wordlistCompletedKey);

    if (wordListCompleted == true) {
      return newWordRandomAlgorithm(wordlist);
    }

    if (lastWordSolved.isEmpty) {
      return UnlimitedWordModel(
          word: wordlist.last, index: wordlist.length - 1);
    } else {
      final index = wordlist.indexOf(lastWordSolved);

      if (index == 0) {
        _localStorage.saveBool(wordlistCompletedKey, true);
        return newWordRandomAlgorithm(wordlist);
      }
      return UnlimitedWordModel(word: wordlist[index - 1], index: index - 1);
    }
  }

  UnlimitedWordModel newWordRandomAlgorithm(List<String> wordlist) {
    final milliSeconds = DateTime.now().millisecondsSinceEpoch;
    final rand = Random(milliSeconds);
    final seed = rand.nextInt(wordlist.length % milliSeconds);
    return UnlimitedWordModel(word: wordlist.elementAt(seed), index: seed);
  }

  UnlimitedWordModel getNewWord(DifficultyConfig config) {
    try {
      if (config.lettersCount == 5) return _newWord;

      if (config.lettersCount == 6) {
        return newWordReverseAlgorithm(
          wordList6,
          lastSolvedKey: LSKey.wordlist6LastSolved,
          wordlistCompletedKey: LSKey.wordlist6Completed,
        );
      }
      if (config.lettersCount == 7) {
        return newWordReverseAlgorithm(
          wordList7,
          lastSolvedKey: LSKey.wordlist7LastSolved,
          wordlistCompletedKey: LSKey.wordlist7Completed,
        );
      }

      return _newWord;
    } catch (e) {
      rethrow;
    }
  }

  /// Will provide a random word that the user may use for guessing, it will attempt
  /// to only give words where the used letters aren't absent. If that's not possible,
  /// it will provide a random word.
  String getHelperWord(String solution, Set<LetterNode> letterUsed) {
    List<String> selection = [];
    final usedLetters = letterUsed.toList();

    if (solution.length == 5) {
      final tempUnlimited = wordsUnlimited;

      // Take words already used from daily and add to unlimited, except the word of the day
      tempUnlimited.addAll(wordsDaily.take(puzzleNumber - 1));
      selection = tempUnlimited.where((word) => word != solution).toList();
    } else if (solution.length == 6) {
      selection = wordList6.where((word) => word != solution).toList();
    } else if (solution.length == 7) {
      selection = wordList7.where((word) => word != solution).toList();
    }

    final initialCandidateWord = randomElement<String>(selection)!;

    usedLetters.removeWhere((node) => node.evaluation != Evaluation.absent);

    for (var node in usedLetters) {
      selection.removeWhere((word) => word.contains(node.letter));
    }

    if (selection.isEmpty) {
      return initialCandidateWord;
    } else {
      return randomElement<String>(selection)!;
    }
  }

  int get puzzleNumber {
    final past = DateTime(2021, 6, 19);
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    final seed = today.difference(past);
    return seed.inDays;
  }
}

class UnlimitedWordModel {
  final String word;
  final int index;

  UnlimitedWordModel({required this.word, required this.index});
}
