import 'dart:developer';

import 'package:uni_links/uni_links.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/router/routes.dart';

class DeepLinkHandler {
  static Future<void> setupDeepLinkHandler() async {
    getInitialLink().then((link) {
      handleDeepLink(link);
    });

    linkStream.listen((link) {
      handleDeepLink(link);
    });
  }
}

Future<void> handleDeepLink(String? link) async {
  const base = "https://worde.potudo.com";

  if (link != null) {
    final validRoutes = routes.keys.toList();
    final path = link.split(base).last;

    if (validRoutes.contains(path)) AppRouter.push(path);
  }

  log("Deeplink: $link");
}
