/// Following effective dart style guide:
/// https://dart.dev/guides/language/effective-dart/design#avoid-defining-a-class-that-contains-only-static-members
/// Avoid defining a class that contains ONLY static members.
/// Dar<PERSON> has top-level functions, variables, and constants, so you don’t need
/// a class just to define something, unlike in Java or C#.

// ignore_for_file: constant_identifier_names

import 'dart:developer';

import 'package:flutter/foundation.dart'
    show TargetPlatform, defaultTargetPlatform, kDebugMode, kIsWeb;
import 'package:flutter_dotenv/flutter_dotenv.dart';

enum EnvKey {
  BASE_URL,
  PLATFORM_APP_NAME,
  PLAYSTORE_TITLE,
  PLAYSTORE_LINK,
  TWITTER_APIKEY,
  TWITTER_APISECRET,
  CALLBACK_URI,
  FACEBOOK_APP_ID,
}

String env(EnvKey key) {
  return _getValue(key);
}

Future loadEnv({String type = ''}) async {
  log('Loading $type.env');
  if (kIsWeb && !kDebugMode) {
    return await dotenv.load(fileName: 'config/prod.env');
  }

  return await dotenv.load(fileName: 'config/$type.env');
}

String _getValue(EnvKey key) {
  var stringKey = key.toString().split('.')[1];
  const platform = 'PLATFORM_';

  if (stringKey.startsWith(platform)) {
    if (defaultTargetPlatform == TargetPlatform.iOS) {
      return dotenv.env['IOS_' + stringKey.substring(platform.length)] ?? '';
    } else {
      return dotenv.env['ANDROID_' + stringKey.substring(platform.length)] ??
          '';
    }
  }

  return dotenv.env[key.toString().split('.')[1]] ?? '';
}
