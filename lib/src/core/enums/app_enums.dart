enum KeepPlayingPopupType {
  none,
  outOfTries,
  keepStreaks,
}

enum GameTypeEnum {
  fiveLetterDaily(name: "5Letter", type: 'daily'),
  sixLetterDaily(name: "6Letter", type: 'daily'),
  sevenLetterDaily(name: "7Letter", type: 'daily'),
  fiveLetterUnlimited(name: "5Letter", type: 'unlimited'),
  sixLetterUnlimited(name: "6Letter", type: 'unlimited'),
  sevenLetterUnlimited(name: "7Letter", type: 'unlimited');

  final String name;
  final String type;

  const GameTypeEnum({required this.name, required this.type});
}
