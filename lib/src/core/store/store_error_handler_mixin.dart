import 'package:dio/dio.dart';
// import 'package:wordle/src/core/helpers/translation.dart';

mixin StoreErrorHandlerMixin {
  /// To use this method ensure you have a field called [errorMessage]
  /// or this won't work
  void handleError(dynamic store, dynamic error) {
    // final tr = translateWithoutContext()!;
    if (error is DioError) {
      store.errorMessage =
          error.response?.data['message'] ?? 'An unexpected error occured.';
    } else {
      store.errorMessage = 'An unexpected error occured.';
    }
  }
}
