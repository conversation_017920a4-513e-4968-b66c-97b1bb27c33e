import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/domain/auth/store/auth_store.dart';
import 'package:wordle/src/domain/daily_challenge/stores/daily_challenge_store.dart';
import 'package:wordle/src/domain/daily_challenge/stores/daily_comment_store.dart';
import 'package:wordle/src/domain/daily_challenge/stores/timer_store.dart';
import 'package:wordle/src/domain/main/stores/connection_store.dart';
import 'package:wordle/src/domain/main/stores/main_store.dart';
import 'package:wordle/src/domain/multiplayer/stores/match_making_store.dart';
import 'package:wordle/src/domain/multiplayer/stores/multiplayer_leaderboard_store.dart';
import 'package:wordle/src/domain/multiplayer/stores/multiplayer_store.dart';
import 'package:wordle/src/domain/shop/store/ads_store.dart';
import 'package:wordle/src/domain/shop/store/shop_store.dart';
import 'package:wordle/src/domain/unlimited_challenge/stores/unlimited_challenge_store.dart';

class StoreRegistry {
  StoreRegistry() {
    ServiceLocator.locate.registerSingleton<TimerStore>(TimerStore());
    ServiceLocator.locate.registerSingleton<MainStore>(MainStore());
    ServiceLocator.locate
        .registerSingleton<MatchMakingStore>(MatchMakingStore());
    ServiceLocator.locate
        .registerSingleton<MultiplayerStore>(MultiplayerStore());
    ServiceLocator.locate.registerSingleton<ShopStore>(ShopStore());
    ServiceLocator.locate.registerSingleton<AuthStore>(AuthStore());
    ServiceLocator.locate.registerSingleton<ConnectionStore>(ConnectionStore());
    ServiceLocator.locate
        .registerSingleton<DailyChallengeStore>(DailyChallengeStore());
    ServiceLocator.locate
        .registerSingleton<UnlimitedChallengeStore>(UnlimitedChallengeStore());

    ServiceLocator.locate.registerSingleton<AdsStore>(AdsStore());
    ServiceLocator.locate.registerSingleton<MultiplayerLeaderboardStore>(
      MultiplayerLeaderboardStore(),
    );
    ServiceLocator.locate.registerSingleton<DailyCommentStore>(
      DailyCommentStore(),
    );
  }
}
