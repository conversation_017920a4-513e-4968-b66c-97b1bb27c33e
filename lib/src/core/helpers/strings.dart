/// Following effective dart style guide:
/// https://dart.dev/guides/language/effective-dart/design#avoid-defining-a-class-that-contains-only-static-members
/// Avoid defining a class that contains ONLY static members.
/// Dart has top-level functions, variables, and constants, so you don’t need
/// a class just to define something, unlike in Java or C#.
///

/// Checks if a string is empty or null and returns true, if not false.
bool isNullOrEmpty(String? value) {
  return value == null || value.isEmpty;
}
