import 'package:flutter/foundation.dart'
    show TargetPlatform, defaultTargetPlatform, kDebugMode;

class AppAdmob {
  // This class is not meant to be instantiated or extended; this constructor
  // prevents instantiation and extension.
  AppAdmob._();
  static const _testRewardId = "ca-app-pub-3577719288227636/7411492015";
  static final _testInterstitialAdId =
      (defaultTargetPlatform == TargetPlatform.android)
          ? 'ca-app-pub-3577719288227636/7411492015'
          : 'ca-app-pub-3577719288227636/7411492015';
  static final _testNativeAdId =
      (defaultTargetPlatform == TargetPlatform.android)
          ? 'ca-app-pub-3577719288227636/7411492015'
          : 'ca-app-pub-3577719288227636/7411492015';

  // production reward ids
  static final _rewardAdUnitIds = <AdRewardUnitId, String>{
    AdRewardUnitId.free10Coins: "ca-app-pub-3577719288227636/7411492015",
    AdRewardUnitId.skipUnlimitedLevel: "ca-app-pub-3577719288227636/7411492015",
  };

  static final _interstitialAdUnitIds = <AdInterstitialUnitId, String>{
    AdInterstitialUnitId.daily: 'ca-app-pub-3577719288227636/7411492015',
    AdInterstitialUnitId.unlimited: 'ca-app-pub-3577719288227636/7411492015',
  };

  static final _nativeAdUnitIds = <AdNativeUnitId, String>{
    AdNativeUnitId.dailyCommentNative: 'ca-app-pub-3577719288227636/7411492015',
  };

  static String getRewardAdUnitId(AdRewardUnitId key) {
    if (kDebugMode) {
      return _testRewardId;
    } else {
      return _rewardAdUnitIds[key]!;
    }
  }

  static String getInterstitialAdUnitId(AdInterstitialUnitId key) {
    if (kDebugMode) {
      return _testInterstitialAdId;
    } else {
      return _interstitialAdUnitIds[key]!;
    }
  }

  static String getNativeAdUnitId(AdNativeUnitId key) {
    if (kDebugMode) {
      return _testNativeAdId;
    } else {
      return _nativeAdUnitIds[key]!;
    }
  }
}

enum AdRewardUnitId { free10Coins, skipUnlimitedLevel }

enum AdInterstitialUnitId { daily, unlimited }

enum AdNativeUnitId { dailyCommentNative }
