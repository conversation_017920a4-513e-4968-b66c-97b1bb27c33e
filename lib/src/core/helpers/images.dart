import 'dart:io';

import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';

/// Following effective dart style guide:
/// https://dart.dev/guides/language/effective-dart/design#avoid-defining-a-class-that-contains-only-static-members
/// Avoid defining a class that contains ONLY static members.
/// Dar<PERSON> has top-level functions, variables, and constants, so you don’t need
/// a class just to define something, unlike in Java or C#.
///

Future<String> createShareableImage(String imagePath) async {
  final ByteData bytes = await rootBundle.load(imagePath);
  final list = bytes.buffer.asUint8List();
  final directory = await getTemporaryDirectory();
  File imgFile = await File('${directory.path}/image.jpg').create();
  imgFile.writeAsBytesSync(list);

  return '${directory.path}/image.jpg';
}
