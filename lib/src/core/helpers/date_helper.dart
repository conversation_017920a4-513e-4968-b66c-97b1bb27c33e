String getRelativeTime(DateTime date) {
  final now = DateTime.now();
  final difference = now.difference(date);

  if (difference.inSeconds < 60) {
    return "${difference.inSeconds}s";
  } else if (difference.inMinutes < 60) {
    return "${difference.inMinutes}m";
  } else if (difference.inHours < 24) {
    return "${difference.inHours}h";
  } else if (difference.inDays < 30) {
    return "${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago";
  } else if (difference.inDays < 365) {
    final months = difference.inDays ~/ 30;
    return "$months ${months == 1 ? 'month' : 'months'} ago";
  } else {
    final years = difference.inDays ~/ 365;
    return "$years ${years == 1 ? 'year' : 'years'} ago";
  }
}
