import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:oktoast/oktoast.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/core/constants/font_family.dart';

void toast({
  required BuildContext context,
  required String msg,
  Color? backgroundColor,
  double? radius,
  ToastPosition? position,
  EdgeInsetsGeometry? textPadding,
}) {
  showToast(
    msg,
    textStyle: TextStyle(color: AppColors.of(context).colorTone7, fontSize: 16),
    backgroundColor: backgroundColor ?? AppColors.of(context).colorTone1,
    position: position ?? ToastPosition.top,
    radius: radius,
    textPadding: textPadding ?? const EdgeInsets.all(10),
  );
}

void showGameToast({
  required BuildContext context,
  required String msg,
  required bool isSuccess,
}) {
  showToastWidget(
    Center(
      child: Container(
        height: 56,
        decoration: BoxDecoration(
          color: isSuccess ? AppColors.brightYellow : AppColors.errorRed,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withOpacity(0.35),
              offset: Offset(0, 4),
              blurRadius: 4,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              width: 20,
            ),
            Container(
              height: 32,
              width: 32,
              decoration: BoxDecoration(
                  color: isSuccess ? AppColors.black : AppColors.white,
                  shape: BoxShape.circle),
              padding: EdgeInsets.all(isSuccess ? 7 : 9),
              child: SvgPicture.asset(
                isSuccess ? AppImages.checkIcon : AppImages.crossIcon,
              ),
            ),
            SizedBox(
              width: 16,
            ),
            AutoSizeText(
              msg,
              maxLines: 1,
              style: TextStyle(
                color: isSuccess ? Colors.black : Colors.white,
                fontWeight: FontWeight.w700,
                fontSize: 20,
                fontFamily: FontFamily.urbanist,
              ),
            ),
            SizedBox(
              width: 25,
            )
          ],
        ),
      ),
    ),
    position: ToastPosition.center,
  );
}
