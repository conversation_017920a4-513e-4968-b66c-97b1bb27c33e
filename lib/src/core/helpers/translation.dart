/// Following effective dart style guide:
/// https://dart.dev/guides/language/effective-dart/design#avoid-defining-a-class-that-contains-only-static-members
/// Avoid defining a class that contains ONLY static members.
/// Dart has top-level functions, variables, and constants, so you don’t need
/// a class just to define something, unlike in Java or C#.

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:intl/intl.dart';

/// This method is meant to be used in pages and widgets. Essentially UI
/// elements that use context
AppLocalizations? t(BuildContext context) {
  return AppLocalizations.of(context);
}

/// This is meant to be used outside widgets and pages, for example
/// in a model, a store or even API.
AppLocalizations? translateWithoutContext() {
  return lookupAppLocalizations(
    Locale(
      Intl.shortLocale(
        Intl.getCurrentLocale(),
      ),
    ),
  );
}
