import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/repositories/ad_repository.dart';
import 'package:wordle/src/data/repositories/ironsource_ad_repository.dart';

class RepositoryRegistry {
  RepositoryRegistry() {
    ServiceLocator.locate
        .registerSingleton<IronSourceAdRepository>(IronSourceAdRepository());
    ServiceLocator.locate.registerSingleton<AdRepository>(AdRepository());
  }
}
