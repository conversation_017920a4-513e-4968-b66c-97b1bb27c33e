class AppStrings {
  //SplashScreen
  static const String solveNewPuzzleMessage =
      'Solve a new puzzle every day.\nChallenge yourself!';

  //MatchMakingView
  static const String leaveMatchText = "Leave Match";
  static const String leaveMatchMessage = '''
Are you sure you want to leave this match?

If you leave before any moves are played the match will be drawn, and you will receive your coins back.

If you leave when a move has been played, you will not receive your coins back and your opponent will be declared the winner.''';
  static const String multiplayerText = "Multiplayer";
  static const String youText = "You";

  //MultiplayerLandingView
  static const String howToPlayText = 'How To Play';
  static const String signedInAsText = 'Signed in as:';
  static const String classicText = 'CLASSIC';
  static const String twoMinPerGuessText = '2 minutes per guess';
  static const String errorText = 'Error';
  static const String notEnoughCoinsMessage =
      'Not enough coins to join a match. You need at least 10 coins to join a match.';
  static const String vsFriendText = 'VS FRIENDS';
  static const String noTimeLimitMessage = 'No time limit per guess';
  static const String comingSoonText = 'COMING SOON';

  //MultiplayerLeaderboardView
  static const String leaderboardText = 'Leaderboard';

  //unlimitedChallengeView
  static const String congratulationsText = "Congratulations!";
  static const String thanksForPlayingText = "Thanks for playing!";
  static const String helpingHandText = 'Helping Hand:';
  static const String adErrorMessage = "There was an error loading the ad.";
  static const String unlimitedText = "Unlimited";
  static const String statsText = "Stats";
  static const String newWordeText = 'New worde';

  //unlimitedChallengeStore
  static const String notEnoughLettersText = 'Not enough letters';
  static const String notInWordListText = 'Not in word list';

  //confirmStreakDialog
  static const String areYourSureText = "Are you sure?";
  static const String giveUpAndNextText = 'Give up & Next';
  static const String keepItText = 'Keep it';
  static const String gameWinStreakText = 'game win streak.';
  static const String youWillLoseYourText = "you will lose your";

  //keepPlayingDialog
  static const String outOfTriesText = "Out of Tries!";
  static const String keepStreakMessage = "Keep your Streak?";
  static const String getOneMoreTryText = "Get one more try!";
  static const String giveUpText = "Give Up";
  static const String nextText = 'NEXT';
  static const String unlockText = "Unlock";
  static const String oneMoreGuessText = "One more guess";
  static const String keepYourText = "Keep Your";
  static const String winStreakText = "Win Streak";
}
