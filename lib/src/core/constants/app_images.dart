class AppImages {
  // This class is not meant to be instantiated or extended; this constructor
  // prevents instantiation and extension.
  AppImages._();

  static const rootPath = 'assets/images';
  static const iconPath = 'assets/icons';
  static const wordleLogo = '$rootPath/wordle_logo.png';
  static const featureImage = '$rootPath/feature.jpg';
  static const helpIcon = '$iconPath/question_mark.svg';
  static const statsIcon = '$iconPath/stats.svg';
  static const settingsIcon = '$iconPath/gear.svg';
  static const deleteIcon = '$iconPath/delete.svg';
  static const shareIcon = '$iconPath/share.svg';
  static const infinityIcon = '$iconPath/infinity.svg';
  static const bulbIcon = '$iconPath/bulb.png';
  static const helpingHandIcon = '$iconPath/helping_hand.png';
  static const skipIcon = '$iconPath/skip.png';
  static const crownImage = '$rootPath/crown.png';
  static const timerImage = '$rootPath/timer_image.svg';
  static const coin200 = '$rootPath/coin_200.png';
  static const coin500 = '$rootPath/coin_500.png';
  static const coin1200 = '$rootPath/coin_1200.png';
  static const coin2500 = '$rootPath/coin_2500.png';
  static const coin5200 = '$rootPath/coin_5200.png';
  static const coin11000 = '$rootPath/coin_11000.png';
  static const coinSingle = '$rootPath/coin_single.png';
  static const worldConnected = '$rootPath/world_connected.png';
  static const friendsOnline = '$rootPath/friends_online.png';
  static const appLogo = '$rootPath/app_logo.png';
  static const calendarGlossy = '$rootPath/calendar-glossy.png';
  static const flagGlossy = '$rootPath/flag-glossy.png';
  static const gameGlossy = '$rootPath/game-glossy.png';
  static const drawerIcon = '$iconPath/drawer.svg';
  static const dailyModeIcon = '$iconPath/daily_mode.svg';
  static const multiPlayerModeIcon = '$iconPath/multiplayer_mode_icon.svg';
  static const unlimitedModeIcon = '$iconPath/unlimited_mode_icon.svg';
  static const statCircularIcon = '$iconPath/stat_circular_icon.svg';
  static const trophyCircularIcon = '$iconPath/trophy_circular_icon.svg';
  static const helpCircularIcon = '$iconPath/help_circular_icon.svg';
  static const logo = '$iconPath/LOGO.svg';
  static const logoGridThin = '$iconPath/logo_grid_thin.svg';
  static const logoGrid = '$rootPath/new_logo_grid.png';
  static const helpFilledIcon = '$iconPath/help_filled_icon.svg';
  static const statsFilledIcon = '$iconPath/stats_filled_icon.svg';
  static const helpIconNew = '$iconPath/help_icon_new.svg';
  static const hintIconNew = '$iconPath/hint_icon_new.svg';
  static const skipAdIconNew = '$iconPath/skip_ad_icon_new.svg';
  static const storeIcon = '$iconPath/store_icon.svg';
  static const starIcon = '$iconPath/star_icon.png';
  static const winnerIcon = '$iconPath/winner_icon.svg';
  static const multiplayerTitleIcon = '$iconPath/multiplayer_title_icon.svg';
  static const singleCoinNormalIcon = '$iconPath/single_coin_normal_icon.svg';
  static const crossIcon = '$iconPath/cross_icon.svg';
  static const checkIcon = '$iconPath/check_icon.svg';
  static const giveUpIcon = '$iconPath/give_up_icon.svg';
  static const oneMoreGuessIcon = '$iconPath/one_more_guess_icon.svg';
  static const unlockAdIcon = '$iconPath/unlock_ad_icon.svg';
  static const keepSteakIcon = '$iconPath/keep_streak_icon.svg';
  static const keepSteakBlackThemeIcon =
      '$iconPath/keep_streak_black_theme_icon.svg';
  static const oneMoreGuessBlackThemeIcon =
      '$iconPath/one_more_guess_black_theme_icon.svg';
  static const giveUpBlackThemeIcon = '$iconPath/give_up_black_theme_icon.svg';
  static const downMoreIcon = '$iconPath/down_more_icon.svg';
  static const watchIcon = '$iconPath/watch_icon.svg';
  static const watchBlueGreyIcon = '$iconPath/watch_blue_grey_icon.svg';
  static const watchSpeedIcon = '$iconPath/watch_speed_icon.svg';
  static const speedModeIcon = '$iconPath/speed_mode_icon.svg';
  static const notificationIcon = '$iconPath/notification_icon.svg';
  static const notificationBlackThemeIcon =
      '$iconPath/notification_black_theme_icon.svg';
}
