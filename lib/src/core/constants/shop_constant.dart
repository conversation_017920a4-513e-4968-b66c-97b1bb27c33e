import 'app_images.dart';

const String playPassProductId = "playpass_no_ads";

const Set<String> productIds = {
  'coins_200',
  'coins_500',
  'coins_1200',
  'coins_2500',
  'coins_5200',
  'coins_11000',
};

const Set<String> subscriptionIds = {'premium_month', 'premium_year'};

List<String> productImages = [
  AppImages.coin200,
  AppImages.coin500,
  AppImages.coin1200,
  AppImages.coin2500,
  AppImages.coin5200,
  AppImages.coin11000,
];

Map<String, dynamic> iapProductMap = {
  "coins_200": {
    "productId": "coins_200",
    "type": "NON_SUBSCRIPTION",
    "coins": 200,
    "price": 1.42,
  },
  "coins_500": {
    "productId": "coins_500",
    "type": "NON_SUBSCRIPTION",
    "coins": 500,
    "price": 2.84,
  },
  "coins_1200": {
    "productId": "coins_1200",
    "type": "NON_SUBSCRIPTION",
    "coins": 1200,
    "price": 5.68,
  },
  "coins_2500": {
    "productId": "coins_2500",
    "type": "NON_SUBSCRIPTION",
    "coins": 2500,
    "price": 11.36,
  },
  "coins_5200": {
    "productId": "coins_5200",
    "type": "NON_SUBSCRIPTION",
    "coins": 5200,
    "price": 22.72,
  },
  "coins_11000": {
    "productId": "coins_11000",
    "type": "NON_SUBSCRIPTION",
    "coins": 11000,
    "price": 45.44,
  },
  "premium_month": {
    "productId": "premium_month",
    "type": "SUBSCRIPTION",
    "coins": 1000,
    'price': 1.50,
  },
  "premium_year": {
    "productId": "premium_year",
    "type": "SUBSCRIPTION",
    "coins": 6000,
    "price": 10.49,
  },
  "playpass_no_ads": {
    "productId": "playpass_no_ads",
    "type": "NON_SUBSCRIPTION",
    "coins": 0,
    "price": 0.74,
  },
};
