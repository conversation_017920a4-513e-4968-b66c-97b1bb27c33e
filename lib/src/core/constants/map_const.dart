import '../../data/models/difficulty_config.dart';
import '../../data/models/difficulty_item_model.dart';

Map<int, DifficultyItemModel> difficultyItemsMap = {
  5: DifficultyItemModel(
      difficultyConfig: DifficultyConfig(lettersCount: 5, rowCount: 6),
      name: '5 Letter'),
  6: DifficultyItemModel(
      difficultyConfig: DifficultyConfig(lettersCount: 6, rowCount: 6),
      name: '6 Letter'),
  7: DifficultyItemModel(
      difficultyConfig: DifficultyConfig(lettersCount: 7, rowCount: 7),
      name: '7 Letter'),
};
