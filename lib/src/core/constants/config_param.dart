class ConfigParam {
  static const enWordlist = 'en_wordlist';
  static const enableUnlimitedMode = 'enable_unlimited_mode';
  static const enableDailyMode = 'enable_daily_mode';
  static const enableMultiplayerMode = 'enable_multiplayer_mode';
  static const enableDailyModeComments = 'enable_daily_mode_comments';
  static const shareableResultsTemplate = 'shareable_results_template';
  static const forceUpdate = 'force_update';
  static const forceUpdateMessage = 'force_update_message';
  static const forceUpdateLink = 'force_update_link';
  static const leaderboardLimit = 'leaderboard_limit';
  static const interstitialAdFrequency = 'interstitial_ad_frequency';
  static const isPreviousSinglePlayerStatsRequired =
      'is_previous_single_player_stats_required';

  // Used to determine where in the comments list-view ads will be placed.
  static const dailyCommentAdPlacement = 'daily_comment_ad_placement';
  static const playPassRevenueUsd = 'playpass_revenue_usd';

  //hint_counts_normal_user
  static const hints5Hand = 'hints_5_hand';
  static const hints5LightBulb = 'hints_5_lightbulb';
  static const hints6Hand = 'hints_6_hand';
  static const hints6LightBulb = 'hints_6_lightbulb';
  static const hints7Hand = 'hints_7_hand';
  static const hints7LightBulb = 'hints_7_lightbulb';

  //hint_counts_play_pass
  static const hints5HandPlayPass = 'hints_5_hand_play_pass';
  static const hints5LightBulbPlayPass = 'hints_5_lightbulb_play_pass';
  static const hints6HandPlayPass = 'hints_6_hand_play_pass';
  static const hints6LightBulbPlayPass = 'hints_6_lightbulb_play_pass';
  static const hints7HandPlayPass = 'hints_7_hand_play_pass';
  static const hints7LightBulbPlayPass = 'hints_7_lightbulb_play_pass';

  //hint_counts_sub
  static const hints5HandSub = 'hints_5_hand_Sub';
  static const hints5LightBulbSub = 'hints_5_lightbulb_Sub';
  static const hints6HandSub = 'hints_6_hand_Sub';
  static const hints6LightBulbSub = 'hints_6_lightbulb_Sub';
  static const hints7HandSub = 'hints_7_hand_Sub';
  static const hints7LightBulbSub = 'hints_7_lightbulb_Sub';

  //hint_count_ads
  static const hints5HandAds = 'hints_5_hand_ads';
  static const hints5LightBulbAds = 'hints_5_lightbulb_ads';
  static const hints6HandAds = 'hints_6_hand_ads';
  static const hints6LightBulbAds = 'hints_6_lightbulb_ads';
  static const hints7HandAds = 'hints_7_hand_ads';
  static const hints7LightBulbAds = 'hints_7_lightbulb_ads';

  //speed-mode
  static const speed5LetterDuration = 'speed_5_letter_duration';
  static const speed6LetterDuration = 'speed_6_letter_duration';
  static const speed7LetterDuration = 'speed_7_letter_duration';
}

class ConfigDefaults {
  //hint_counts_normal_user
  static const int hints5HandCount = 0;
  static const int hints5LightBulbCount = 0;
  static const int hints6HandCount = 1;
  static const int hints6LightBulbCount = 1;
  static const int hints7HandCount = 1;
  static const int hints7LightBulbCount = 1;

  //hint_counts_play_pass
  static const int hints5HandPlayPassCount = 1;
  static const int hints5LightBulbPlayPassCount = 1;
  static const int hints6HandPlayPassCount = 2;
  static const int hints6LightBulbPlayPassCount = 2;
  static const int hints7HandPlayPassCount = 2;
  static const int hints7LightBulbPlayPassCount = 3;

  //hint_counts_sub
  static const int hints5HandSubCount = 1;
  static const int hints5LightBulbSubCount = 1;
  static const int hints6HandSubCount = 2;
  static const int hints6LightBulbSubCount = 2;
  static const int hints7HandSubCount = 2;
  static const int hints7LightBulbSubCount = 3;

  //hint_count_ads
  static const int hints5HandAdsCount = 1;
  static const int hints5LightBulbAdsCount = 1;
  static const int hints6HandAdsCount = 2;
  static const int hints6LightBulbAdsCount = 2;
  static const int hints7HandAdsCount = 2;
  static const int hints7LightBulbAdsCount = 2;

  //speed-mode
  static const int speed5LetterDuration = 300;
  static const int speed6LetterDuration = 360;
  static const int speed7LetterDuration = 420;
}
