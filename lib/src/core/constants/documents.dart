import 'package:flutter/foundation.dart';

class Documents {
  // This class is not meant to be instantiated or extended; this constructor
  // prevents instantiation and extension.
  Documents._();

  static const rootPath = 'assets/docs';
  static String get privacyPolicy {
    if (defaultTargetPlatform == TargetPlatform.iOS) {
      return '$rootPath/ios-privacy-policy.md';
    } else {
      return '$rootPath/privacy-policy.md';
    }
  }

  static String get terms {
    if (defaultTargetPlatform == TargetPlatform.iOS) {
      return '$rootPath/ios-terms-and-conditions.md';
    } else {
      return '$rootPath/terms-and-conditions.md';
    }
  }

  static String get termsAcceptMessage {
    return '$rootPath/terms-message.md';
  }
}
