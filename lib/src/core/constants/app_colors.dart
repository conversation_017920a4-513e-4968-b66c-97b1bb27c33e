import 'package:flutter/material.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/settings/settings_controller.dart';

class AppColors {
  // This class is not meant to be instantiated or extended; this constructor
  // prevents instantiation and extension.
  AppColors._();

  final settingsController = ServiceLocator.locate<SettingsController>();
  static Brightness? brightness;

  static const Color primaryColor = Color(0xFF1F2933);
  static const Color secondaryColor = Color(0xFFEDC179);
  static const Color mutedGrey = Color(0xFF7E7E7E);
  static const Color disabled = Color(0xFF606060);
  static const Color lightGrey = Color(0xFFC4C4C4);
  static const Color midBlack = Color(0xFF464646);
  static const Color goodGreen = Color(0xFF00DF7D);
  static const Color saleGreen1 = Color(0xff77AE4C);
  static const Color nonActiveGrey = Color(0xFFD9D9D9);
  static const Color purple = Color(0xFF6620BF);
  static const Color multiPlayerSearchingGreyColor = Color(0xff757575);
  static const Color darkGrey = Color(0xff323233);
  static const Color blueGrey = Color(0xffC1D4D8);
  static const Color filledGrey = Color(0xffDFDFDF);
  static const Color greyB8 = Color(0xffB8B8B8);

  static const Color green = Color(0xFF77AE4C);
  static const Color darkYellow = Color(0xFFD0A400);
  static const Color brightYellow = Color(0xFFFADF00);
  static const Color black = Color(0xFF000000);
  static const Color white = Color(0xFFFFFFFF);
  static const Color red = Color(0xFFFF3D00);
  static const Color errorRed = Color(0xFFFD2C2E);
  static const Color turquoise = Color(0xFF1E7D83);
  static const Color lightBlue = Color(0xFF85C0F9);
  static const Color smBlue = Color(0xFF009E94);
  static const Color orange = Color(0xFFF3793C);
  static const Color realOrange = Color(0xFFFF5623);
  static const Color warmGray = Color(0xFF797C6C);
  static const Color gray8E = Color(0xFF8E8E8E);
  static const Color grayChartLine = Color(0xFFBABABA);
  static const Color boardBoxBorderColor = Color(0xFFD3D6DB);
  static const Color dailyGameTypeBorderColor = Color(0xFF2E2E2E);

  static AppColors of(BuildContext context) {
    brightness = Theme.of(context)
        .brightness; //MediaQuery.platformBrightnessOf(context);
    return AppColors._();
  }

  /// Light mode
  Color get colorTone1 {
    return (brightness == Brightness.light)
        ? const Color(0xff1a1a1b)
        : const Color(0xffd7dadc);
  }

  Color get colorTone2 {
    return (brightness == Brightness.light)
        ? const Color(0xff606060)
        : const Color(0xff818384);
  }

  Color get colorTone3 {
    return (brightness == Brightness.light)
        ? const Color(0xff787c7e)
        : const Color(0xff565758);
  }

  Color get colorTone4 {
    return (brightness == Brightness.light)
        ? const Color(0xffd3d6da)
        : const Color(0xff3A3A3C);
  }

  Color get getAlertBackgroundColor {
    return (brightness == Brightness.light) ? Colors.white : AppColors.darkGrey;
  }

  Color get getAlertBorderColor {
    return (brightness == Brightness.light) ? Colors.white : AppColors.warmGray;
  }

  Color get colorTone7 {
    return (brightness == Brightness.light)
        ? Colors.white
        : const Color(0xff121213);
  }

  Color get keyBg {
    return (brightness == Brightness.light) ? colorTone4 : colorTone2;
  }

  Color get correct {
    if (settingsController.isHighContrast) {
      return Color(0xfff5793a);
    } else {
      return (brightness == Brightness.light) ? green : green;
    }
  }

  Color get present {
    if (settingsController.isHighContrast) {
      return Color(0xff85c0f9);
    } else {
      return (brightness == Brightness.light) ? darkYellow : darkYellow;
    }
  }

  Color get absent {
    return (brightness == Brightness.light) ? colorTone2 : colorTone4;
  }

  Color get infoBlue {
    return (brightness == Brightness.light)
        ? Color(0xFF69b1fa)
        : Color(0xFF0095ff);
  }

  Color get badRed {
    return (brightness == Brightness.light) ? Colors.redAccent : Colors.red;
  }

  Color get saleGreen {
    return Color(0xff77AE4C);
  }

  Color get deepBlue => Color(0xff007adf);

  Color get deepOrange => Color(0xFFDF5200);

  Color get octoberPurple => Color(0xffb721ff);

  static Color leaderboardTopThreeBorderColor = Color(0xff77AE4C);
  static Color leaderboardTopThreePlaceBgColor = Color(0xffF3793C);
  static Color leaderboardTopThreePlaceStandBgColor = Color(0xffC6C8B0);
  static Color leaderboardIndexBgColor = Color(0xffA1A1A1);
  static Color leaderboardIndexBgSelfColor = Color(0xff85C0F9);
}
