import 'package:flutter/material.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/settings/settings_controller.dart';

/// Find gradient to use here: https://webgradients.com/
class AppGradients {
  // This class is not meant to be instantiated or extended; this constructor
  // prevents instantiation and extension.
  AppGradients._();
  final settingsController = ServiceLocator.locate<SettingsController>();
  static Brightness? brightness;

  static AppGradients of(BuildContext context) {
    brightness = Theme.of(context)
        .brightness; //MediaQuery.platformBrightnessOf(context);
    return AppGradients._();
  }

  List<Color> get entranceBg {
    return (brightness == Brightness.light)
        ? [
            Colors.white,
            Color(0xffF3FFFD),
          ]
        : [
            Color(0xff414141),
            Colors.black,
          ];
  }

  List<Color> get octoberSilence => [
        Color(0xff007adf),
        Color(0xffb721ff),
      ];

  List<Color> get grownEarly => [
        Color(0xff3cba92),
        Color(0xff0ba360),
      ];

  List<Color> get happyMemories => [
        Color(0xFFFF5858),
        Color(0xFFF09819),
      ];
  List<Color> get silverWithOpacity => [
        Color(0xffF5F7FA),
        Color(0xffB8C6DB).withOpacity(0.5),
      ];
  List<Color> get goldWithOpacity => [
        Color(0xffFABC3C),
        Color(0xffFACC6B).withOpacity(0.5),
      ];
  List<Color> get bronzeWithOpacity => [
        Color(0xffCD7F32),
        Color(0xffBE7023).withOpacity(0.5),
      ];
  List<Color> get silver => [
        Color(0xffF5F7FA),
        Color(0xffB8C6DB),
      ];
  List<Color> get gold => [
        Color(0xffFABC3C),
        Color(0xffFACC6B),
      ];
  List<Color> get bronze => [
        Color(0xffCD7F32),
        Color(0xffBE7023),
      ];
}
