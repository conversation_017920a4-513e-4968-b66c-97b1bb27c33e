import 'package:wordle/src/core/constants/keyboard_keys.dart';
import 'package:wordle/src/core/constants/keyboard_layout.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/settings/settings_controller.dart';

class KeyboardSettings {
  static final settingsController = ServiceLocator.locate<SettingsController>();
  static List<List<String>> get qwerty => [
        ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
        ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
        [
          settingsController.isEnterOnLeft
              ? KeyboardKeys.enter
              : KeyboardKeys.delete,
          'z',
          'x',
          'c',
          'v',
          'b',
          'n',
          'm',
          settingsController.isEnterOnLeft
              ? KeyboardKeys.delete
              : KeyboardKeys.enter
        ],
      ];

  static List<List<String>> get qwertz => [
        ['q', 'w', 'e', 'r', 't', 'z', 'u', 'i', 'o', 'p'],
        ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
        [
          settingsController.isEnterOnLeft
              ? KeyboardKeys.enter
              : KeyboardKeys.delete,
          'y',
          'x',
          'c',
          'v',
          'b',
          'n',
          'm',
          settingsController.isEnterOnLeft
              ? KeyboardKeys.delete
              : KeyboardKeys.enter
        ],
      ];

  static List<List<String>> get azerty => [
        ['a', 'z', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
        ['q', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm'],
        [
          settingsController.isEnterOnLeft
              ? KeyboardKeys.enter
              : KeyboardKeys.delete,
          'w',
          'x',
          'c',
          'v',
          'b',
          'n',
          settingsController.isEnterOnLeft
              ? KeyboardKeys.delete
              : KeyboardKeys.enter
        ],
      ];

  static List<List<String>> getLayout(KeyboardLayout layout) {
    switch (layout) {
      case KeyboardLayout.qwerty:
        return qwerty;
      case KeyboardLayout.qwertz:
        return qwertz;
      case KeyboardLayout.azerty:
        return azerty;
      default:
        return qwerty;
    }
  }
}
