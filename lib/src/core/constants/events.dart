import '../../../main.dart';
import '../../domain/auth/store/auth_store.dart';
import '../utils/service_locator.dart';

class Events {
  // This class is not meant to be instantiated or extended; this constructor
  // prevents instantiation and extension.
  Events._();

  static const appLaunched = 'app_launched';
  static const appOpened = 'app_opened';
  static const splashVisited = 'splash_visited';
  static const entranceVisited = 'entrance_visited';
  static const dailyChallengeVisited = 'daily_challenge_visited';
  static const unlimitedChallengeVisited = 'unlimited_challenge_visited';
  static const settingsVisited = 'settings_visited';
  static const privacyVisited = 'privacy_visited';

  // Terms & Conditions visited
  static const tocVisited = 'toc_visited';
  static const statsClicked = 'stats_clicked';
  static const helpClicked = 'help_clicked';

  // This share is for daily challenge results
  static const shareClicked = 'share_clicked';
  static const dailyGameFinished = 'daily_game_finished';
  static const dailyGameStarted = 'daily_game_started';
  static const unlimitedGameFinished = 'unlimited_game_finished';
  static const unlimitedGameStarted = 'unlimited_game_started';
  static const unlimitedDifficultyClicked = 'unlimited_difficulty_clicked';

  // speed mode events
  static const shareSpeedClicked = 'share_speed_clicked';
  static const dailySpeedGameFinished = 'daily_speed_game_finished';
  static const dailySpeedGameStarted = 'daily_speed_game_started';
  static const unlimitedSpeedGameFinished = 'unlimited_speed_game_finished';
  static const unlimitedSpeedGameStarted = 'unlimited_speed_game_started';
  static const unlimitedSpeedDifficultyClicked =
      'unlimited_speed_difficulty_clicked';

  // This share is for when users share the app
  static const shareAppClicked = 'share_app_clicked';
  static const onlineMpGameFinished = 'online_mp_game_finished';
  static const onlineMpGameStarted = 'online_mp_game_started';
  static const onlineMpChallengeVisited = 'online_mp_challenge_visited';
  static const shopClicked = 'shop_clicked';
  static const shopVisited = 'shop_visited';
  static const dailyDiscussionsClicked = 'daily_discussions_clicked';
  static const dailyDiscussionsVisited = 'daily_discussions_visited';
  static const addDailyCommentClicked = 'add_daily_comment_clicked';
  static const dailyModeSyncFailed = 'daily_mode_sync_failed';
  static const playPassRevenueUsd = 'playpass_revenue_usd';
  static const playPassFirstOpen = 'playpass_first_open';

  //boosters
  static const didUseLetterHint = 'did_use_letter_hint';
  static const didUseWordHint = 'did_use_word_hint';
  static const didUseLetterHintAd = 'did_use_letter_hint_ad';
  static const didUseWordHintAd = 'did_use_word_hint_ad';
  static const didUseSkipLevelAd = 'did_use_skip_level_ad';
  static const didUseExtraGuess = 'did_use_extra_guess';
  static const didUseSaveStreak = 'did_use_save_streak';

  //speed_boosters
  static const didUseLetterSpeedHint = 'did_use_letter_speed_hint';
  static const didUseWordSpeedHint = 'did_use_word_speed_hint';
  static const didUseLetterSpeedHintAd = 'did_use_letter_speed_hint_ad';
  static const didUseWordSpeedHintAd = 'did_use_word_speed_hint_ad';
  static const didUseSkipLevelSpeedAd = 'did_use_skip_level_speed_ad';
  static const didUseSaveSpeedStreak = 'did_use_save_speed_streak';

  //debug_events
  static const remoteConfigCalled = 'remote_config_called';
  static const deepLinkCalled = 'deep_link_called';
  static const callAdsCalled = 'call_ads_called';
  static const isSettingPlayPassCompleted = 'is_setting_play_pass_completed';
  static const setUpConfigDefaultsCalled = 'setup_config_defaults_called';
  static const updatePurchaseLoopCalled = 'update_purchase_loop_called';
  static const callFirebaseAuthCalled = 'call_firebase_auth_called';
  static const callSubscribeToTopicCalled = 'call_subscribe_to_topic_called';
  static const fetchActivateRemoteConfigCalled =
      'fetch_activate_remote_config_called';
}

class EventParam {
  // This class is not meant to be instantiated or extended; this constructor
  // prevents instantiation and extension.
  EventParam._();

  static const dailyChallengeResult = 'daily_challenge_result';
  static const unlimitedChallengeResult = 'unlimited_challenge_result';
  static const unlimitedChallengeDifficulty = 'unlimited_challenge_difficulty';
  static const value = 'value';
  static const currency = 'currency';
  static const endTime = 'end_time';
  static const userId = 'user_id';
}

class AppStartupParam {
  final _authStore = ServiceLocator.locate<AuthStore>();
  late Map<String, Object>? param;

  AppStartupParam() {
    param = {
      EventParam.endTime: stopwatch.elapsedMilliseconds,
      EventParam.userId: _authStore.user?.uid ?? "",
    };
  }
}
