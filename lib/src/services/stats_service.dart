import 'dart:convert';
import 'dart:developer';

import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/config_param.dart';
import 'package:wordle/src/core/constants/game_result.dart';
import 'package:wordle/src/core/enums/app_enums.dart';
import 'package:wordle/src/core/utils/env.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/core/utils/word_list.dart';
import 'package:wordle/src/data/models/daily_game_session.dart';
import 'package:wordle/src/data/models/stats.dart';
import 'package:wordle/src/services/remote_config/remote_config_service_contract.dart';

import '../domain/main/stores/main_store.dart';
import '../model/get_speed_game_details_model.dart';
import '../model/get_speed_game_summary_model.dart';
import 'auth/auth_service.dart';

class StatsService {
  Stats stats = Stats();

  final _localStorage = ServiceLocator.locate<LocalStorage>();
  final mainStore = ServiceLocator.locate<MainStore>();
  final _wordlist = ServiceLocator.locate<WordList>();
  final configService = ServiceLocator.locate<RemoteConfigServiceContract>();
  final _functions = FirebaseFunctions.instance;
  final _authService = ServiceLocator.locate<AuthService>();

  Future<void> loadStats({
    required BuildContext parentContext,
    required String gameType,
    required String gameName,
  }) async {
    stats = Stats();
    String json = await getSinglePlayerGameStatsNetworkCall(
          context: parentContext,
          gameType: gameType,
          gameName: gameName,
        ) ??
        "";
    if (json.isNotEmpty) {
      stats = Stats.fromJson(
        jsonDecode(json),
      );
    }
    if (gameName == GameTypeEnum.sevenLetterDaily.name &&
        stats.guessDistribution.length == 6) {
      Map<int, int> map = Map.of(stats.guessDistribution);
      map.addAll({7: 0});
      stats.guessDistribution = map;
    }
  }

  Future<String?> getSinglePlayerGameStatsNetworkCall({
    required BuildContext context,
    required String gameType,
    required String gameName,
  }) async {
    final String? userId = _authService.user?.uid;

    var body = <String, dynamic>{
      'userId': userId,
      'gameType': gameType,
      'gameName': gameName,
      'isSpeedMode': mainStore.isSpeedMode,
    };

    try {
      HttpsCallable callable =
          FirebaseFunctions.instance.httpsCallable('getSinglePlayerGameStats');
      final HttpsCallableResult result = await callable.call(body);

      if (result.data != null) {
        debugPrint(
            'Success getSinglePlayerGameStats: userId=$userId, gameType=$gameType, gameName=$gameName\n${result.data}');
        return jsonEncode(result.data);
      } else {
        debugPrint(
            'Error getSinglePlayerGameStats: userId=$userId, gameType=$gameType, gameName=$gameName\n${result.data}');
      }
    } catch (error, stackTrace) {
      debugPrint(
          'Error getSinglePlayerGameStats: userId=$userId, gameType=$gameType, gameName=$gameName\n${error.toString()} \n$stackTrace');
    }
    return null;
  }

  Future<void> getSpeedGameDetailsNetworkCall({
    required BuildContext context,
    required String gameId,
  }) async {
    var body = <String, dynamic>{
      'gameId': gameId,
    };
    try {
      HttpsCallable callable =
          FirebaseFunctions.instance.httpsCallable('getSpeedGameDetails');
      final HttpsCallableResult res = await callable.call(body);
      if (res.data != null && res.data is Map<String, dynamic>) {
        stats.getSpeedGameDetailsModel =
            GetSpeedGameDetailsModel.fromJson(res.data);
        debugPrint('Success getSpeedGameDetails:$body  \n${res.data}');
      } else {
        debugPrint('Error getSpeedGameDetails:$body \n${res.data}');
      }
    } catch (error, stackTrace) {
      debugPrint('Error getSpeedGameDetails: game id=$gameId,\n$stackTrace');
    }
  }

  Future<void> getGameSummaryNetworkCall({
    required BuildContext context,
    required String gameType,
    required String gameName,
  }) async {
    var body = <String, dynamic>{
      'gameType': gameType,
      'gameName': gameName,
    };

    try {
      HttpsCallable callable = FirebaseFunctions.instance
          .httpsCallable('getSinglePlayerGameSummary');
      final HttpsCallableResult result = await callable.call(body);

      if (result.data != null) {
        var res = result.data;
        Map<int, double> gameSummary = {};
        (res as Map<dynamic, dynamic>).forEach((key, value) {
          int intKey = int.parse(key.toString());
          double doubleValue = double.parse(value.toString());
          gameSummary[intKey] = doubleValue;
        });

        stats.gameSummary = gameSummary;
        debugPrint('Success getSinglePlayerGameSummary:$body  \n$gameSummary');
      } else {
        debugPrint('Error getSinglePlayerGameSummary:$body \n${result.data}');
      }
    } catch (error, stackTrace) {
      debugPrint(
          'Error getSinglePlayerGameSummary: gameType=$gameType, gameName=$gameName\n${error.toString()} \n$stackTrace');
    }
  }

  Future<void> getSpeedGameSummaryNetworkCall({
    required BuildContext context,
    required String gameType,
    required String gameName,
  }) async {
    var body = <String, dynamic>{
      'gameType': gameType,
      'gameName': gameName,
    };

    try {
      HttpsCallable callable =
          FirebaseFunctions.instance.httpsCallable('getSpeedGameSummary');
      final HttpsCallableResult res = await callable.call(body);
      if (res.data != null && res.data is Map<String, dynamic>) {
        stats.getSpeedGameSummaryModel =
            GetSpeedGameSummaryModel.fromJson(res.data);

        debugPrint(
            'test getSinglePlayerGameSummary:$body  \n${stats.getSpeedGameSummaryModel?.summary?.length ?? "null"}');
      } else {
        debugPrint('Error getSpeedGameSummary:$body \n${res.data}');
      }
    } catch (error, stackTrace) {
      debugPrint('Error getSpeedGameSummary: $body,\n$stackTrace');
    }
  }

  void loadLocalStats(LSKey key) {
    final json = _localStorage.retrieve(key);
    if (json.isEmpty) {
      stats = Stats();
    } else {
      stats = Stats.fromJson(jsonDecode(json));
    }
    if (key == LSKey.unlimitedStats7 && stats.guessDistribution.length == 6) {
      Map<int, int> map = Map.of(stats.guessDistribution);
      map.addAll({7: 0});
      stats.guessDistribution = map;
    }
  }

  Future<void> resetStats({
    required BuildContext parentContext,
    required String gameType,
    required String gameName,
  }) async {
    await resetStatsNetworkCall(gameType: gameType, gameName: gameName);
    await loadStats(
      parentContext: parentContext,
      gameType: gameType,
      gameName: gameName,
    );
  }

  String getShareableResults() {
    final json = _localStorage.retrieve(LSKey.dailyGameSession);
    String results = '';
    String guesses = '';
    if (json.isNotEmpty) {
      final session = DailyGameSession.fromJson(jsonDecode(json));
      for (var element in session.submittedRows) {
        results += '${element.toEmojis()}\n';
      }

      if (session.gameResult == GameResult.won) {
        guesses = '${session.submittedRows.length}/6';
      } else {
        guesses = 'X/6';
      }
    }

    String template =
        configService.getString(ConfigParam.shareableResultsTemplate);
    return template
        .replaceAll('{title}', env(EnvKey.PLATFORM_APP_NAME))
        .replaceAll('{puzzleNumber}', _wordlist.puzzleNumber.toString())
        .replaceAll('{guesses}', guesses)
        .replaceAll('{results}', results);
  }

  Future<void> resetStatsNetworkCall({
    required String gameType,
    required String gameName,
  }) async {
    Map<String, dynamic> body = {
      "gameName": gameName,
      "gameType": gameType,
      "isSpeedMode": mainStore.isSpeedMode,
    };
    final resetSinglePlayerGameStats =
        _functions.httpsCallable('resetSinglePlayerGameStats');
    await resetSinglePlayerGameStats(body).then((response) {
      log('Success :  ResetSinglePlayerGameStats $body');
    }).catchError((error, stackTrace) {
      log('Error :  ResetSinglePlayerGameStats $body\n${error.toString()} $stackTrace');
    });
  }

  String get gamesPlayed {
    return stats.gamesPlayed.toString();
  }

  String get winPercentage {
    return "${stats.winPercentage.toStringAsFixed(0)}%";
  }

  String get currentStreak {
    return stats.currentStreak.toString();
  }

  String get maxStreak {
    return stats.maxStreak.toString();
  }

  Map<int, int> get guessDistribution {
    return stats.guessDistribution;
  }
}
