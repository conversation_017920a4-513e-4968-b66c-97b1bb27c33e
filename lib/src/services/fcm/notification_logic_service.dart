import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../core/utils/local_storage.dart';
import '../../core/utils/service_locator.dart';
import '../../ui/widgets/dialogs/custom_notification_dialog.dart';
import '../auth/auth_service.dart';

class NotificationLogicService {
  static final _localStorage = ServiceLocator.locate<LocalStorage>();
  static final _authService = ServiceLocator.locate<AuthService>();

  static Future<void> showNotificationPopupIfNeeded(
      BuildContext context) async {
    if (await _shouldShowNotification()) {
      await _showCustomNotification(context);
    }
  }

  static Future<bool> _shouldShowNotification() async {
    final user = _authService.user;

    DateTime creationTime = user?.metadata.creationTime ?? DateTime.now();
    DateTime currentDate = DateTime.now();
    int daysSinceCreation = currentDate.difference(creationTime).inDays;

    List<int> notificationDays = [0, 1, 2, 4, 7];
    bool shouldShow = false;
    PermissionStatus status = await Permission.notification.status;

    if (status.isDenied || status.isPermanentlyDenied) {
      String? lastShownDate =
          _localStorage.retrieve(LSKey.lastNotificationPopupShownKey);

      String today = DateTime.now().toIso8601String().split('T')[0];

      if (lastShownDate != today) {
        if (daysSinceCreation <= 10) {
          shouldShow = notificationDays.contains(daysSinceCreation);
        } else {
          shouldShow = (daysSinceCreation % 10 == 0);
        }
      }
    }

    return shouldShow;
  }

  static Future<void> _showCustomNotification(BuildContext context) async {
    await showDialog(
      barrierDismissible: false,
      context: context,
      builder: (_) => CustomNotificationDialog(),
    );

    String today = DateTime.now().toIso8601String().split('T')[0];
    await _localStorage.save(LSKey.lastNotificationPopupShownKey, today);
  }
}
