import 'dart:developer' as dev;

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';

class EventLogger implements EventLoggerContract {
  final _analytics = FirebaseAnalytics.instance;
  final coinMap = {
    "coins_200": AnalyticsEventItem(
      itemId: "coins_200",
      itemName: "200 Coins",
      itemCategory: "coins",
      itemVariant: "200",
      itemBrand: "Google",
      price: 1.42,
    ),
    "coins_500": AnalyticsEventItem(
      itemId: "coins_500",
      itemName: "500 Coins",
      itemCategory: "coins",
      itemVariant: "500",
      itemBrand: "Google",
      price: 2.84,
    ),
    "coins_1200": AnalyticsEventItem(
      itemId: "coins_1200",
      itemName: "1200 Coins",
      itemCategory: "coins",
      itemVariant: "1200",
      itemBrand: "Google",
      price: 5.68,
    ),
    "coins_2500": AnalyticsEventItem(
      itemId: "coins_2500",
      itemName: "2500 Coins",
      itemCategory: "coins",
      itemVariant: "2500",
      itemBrand: "Google",
      price: 11.36,
    ),
    "coins_5200": AnalyticsEventItem(
      itemId: "coins_5200",
      itemName: "5200 Coins",
      itemCategory: "coins",
      itemVariant: "5200",
      itemBrand: "Google",
      price: 22.72,
    ),
    "coins11000": AnalyticsEventItem(
      itemId: "coins_11000",
      itemName: "11000 Coins",
      itemCategory: "coins",
      itemVariant: "11000",
      itemBrand: "Google",
      price: 45.44,
    ),
    "premium_month": AnalyticsEventItem(
      itemId: "premium_month",
      itemName: "Premium Membership",
      itemCategory: "subsription",
      itemVariant: "monthly",
      itemBrand: "Google",
      price: 3.99,
    ),
    "premium_year": AnalyticsEventItem(
      itemId: "premium_year",
      itemName: "Yearly Premium Membership",
      itemCategory: "subsription",
      itemVariant: "yearly",
      itemBrand: "Google",
      price: 39.99,
    ),
  };

  @override
  void log(String eventName, {Map<String, Object>? params = const {}}) {
    dev.log('eventName: $eventName, params: $params');
    _analytics.logEvent(name: eventName, parameters: params);
  }

  @override
  void logPurchase({
    String? transactionId,
    String? source,
    double? price,
    List<String>? productIds,
  }) {
    _analytics.logPurchase(
      transactionId: transactionId,
      affiliation: source,
      value: price,
      items: productIds?.map((e) => coinMap[e]!).toList(),
    );
  }
}
