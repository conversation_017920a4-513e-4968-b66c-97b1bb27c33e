import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:wordle/src/services/remote_config/remote_config_service_contract.dart';

class RemoteConfigService implements RemoteConfigServiceContract {
  final _firebaseRemoteConfig = FirebaseRemoteConfig.instance;

  @override
  Future<void> setDefaults(Map<String, dynamic> defaults) {
    return _firebaseRemoteConfig.setDefaults(defaults);
  }

  @override
  Future<void> setConfigSetting({
    required Duration fetchTimeout,
    required Duration minimumFetchInterval,
  }) {
    return _firebaseRemoteConfig.setConfigSettings(RemoteConfigSettings(
      fetchTimeout: fetchTimeout,
      minimumFetchInterval: minimumFetchInterval,
    ));
  }

  @override
  String getJson(String key) {
    return _firebaseRemoteConfig.getValue(key).asString();
  }

  @override
  String getString(String key) {
    return _firebaseRemoteConfig.getString(key);
  }

  @override
  Future<bool> fetchAndActivate() {
    return _firebaseRemoteConfig.fetchAndActivate();
  }

  @override
  bool getBool(String key) {
    return _firebaseRemoteConfig.getBool(key);
  }

  @override
  int getInt(String key) {
    return _firebaseRemoteConfig.getInt(key);
  }

  @override
  double getDouble(String key) {
    return _firebaseRemoteConfig.getDouble(key);
  }
}
