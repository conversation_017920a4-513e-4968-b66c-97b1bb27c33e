abstract class RemoteConfigServiceContract {
  Future<void> setDefaults(Map<String, dynamic> defaults);
  Future<void> setConfigSetting({
    required Duration fetchTimeout,
    required Duration minimumFetchInterval,
  });
  String getJson(String key);
  String getString(String key);
  bool getBool(String key);
  int getInt(String key);
  double getDouble(String key);
  Future<bool> fetchAndActivate();
}
