import 'package:wordle/src/services/permission/permission_service_contract.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionService implements PermissionServiceContract {
  bool _checkPermissionStatus(PermissionStatus status) {
    return status == PermissionStatus.granted ||
        status == PermissionStatus.limited;
  }

  @override
  Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();

    return _checkPermissionStatus(status);
  }

  @override
  Future<bool> requestLocationPermission() async {
    final status = await Permission.location.request();

    return _checkPermissionStatus(status);
  }
}
