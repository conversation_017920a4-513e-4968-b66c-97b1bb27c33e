import 'dart:async';
import 'dart:developer';

import 'package:cloud_functions/cloud_functions.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/services/auth/auth_service.dart';

part 'match_making_store.g.dart';

class MatchMakingStore = _MatchMakingStore with _$MatchMakingStore;

abstract class _MatchMakingStore with Store {
  final _functions = FirebaseFunctions.instance;
  final _authService = ServiceLocator.locate<AuthService>();
  @observable
  Status? status;
  @observable
  String? currentMatchId;
  @observable
  bool showUnlimitedButton = false;

  Timer? timer;

  @action
  void findMatch() {
    status = Status.pending;
    startTimer();
    final makeMatch = _functions.httpsCallable('makeMatch');
    makeMatch({
      'displayName': _authService.user?.getDisplayName(),
    }).then((result) {
      Map<String, String> data = Map.from(result.data);
      currentMatchId = data['matchId'];
      status = Status.done;
      log('Success Response from makeMatch function');
    }).catchError((error) {
      status = Status.error;
      log('Error Response from makeMatch function');
      log(error.toString());
    });
  }

  @action
  void changeShowUnlimitedButton({required bool value}) {
    showUnlimitedButton = value;
  }

  void cancelMatch() {
    if (currentMatchId != null) {
      timer?.cancel();
      final cancelMatchMaking = _functions.httpsCallable('cancelMatch');
      cancelMatchMaking({'matchId': currentMatchId}).then((_) {
        log('Success Response from cancelMatch function');
      }).catchError((error) {
        log('Error Response from cancelMatch function');
        log(error.toString());
      });
    }
  }

  void startTimer() {
    const oneSec = Duration(seconds: 1);
    timer = Timer.periodic(
      oneSec,
      (Timer t) {
        if (t.tick == 10) {
          changeShowUnlimitedButton(value: true);
          timer?.cancel();
        }
      },
    );
  }
}
