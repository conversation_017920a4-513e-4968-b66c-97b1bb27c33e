import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/animation_constants.dart';
import 'package:wordle/src/core/constants/evaluation.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/constants/game_result.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/core/utils/word_list.dart';
import 'package:wordle/src/data/models/difficulty_config.dart';
import 'package:wordle/src/data/models/game_row.dart';
import 'package:wordle/src/data/models/letter_node.dart';
import 'package:wordle/src/data/models/match_status.dart';
import 'package:wordle/src/data/models/multiplayer_game.dart';
import 'package:wordle/src/data/models/player.dart';
import 'package:wordle/src/domain/daily_challenge/stores/timer_store.dart';
import 'package:wordle/src/domain/multiplayer/stores/match_making_store.dart';
import 'package:wordle/src/model/multi_player_stats_model.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';
import 'package:wordle/src/services/stats_service.dart';

part 'multiplayer_store.g.dart';

class MultiplayerStore = _MultiplayerStore with _$MultiplayerStore;

abstract class _MultiplayerStore with Store {
  final wordList = ServiceLocator.locate<WordList>();
  final statsService = ServiceLocator.locate<StatsService>();
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final _authService = ServiceLocator.locate<AuthService>();
  final _functions = FirebaseFunctions.instance;
  final _matchMakingStore = ServiceLocator.locate<MatchMakingStore>();
  final _timerStore = ServiceLocator.locate<TimerStore>();
  final pointsMap = <int, int>{
    1: 500,
    2: 350,
    3: 225,
    4: 100,
    5: 50,
    6: 25,
  };
  StreamSubscription<DatabaseEvent>? opponentStatusStream;

  @observable
  GameRow currentGameRow = GameRow();
  @observable
  int currentRowNumber = 1;
  @observable
  ObservableList<GameRow> submittedRows = ObservableList.of([]);
  @observable
  Status? status;
  @observable
  Status? serverStatus;
  @observable
  bool isLoading = true;
  @observable
  String errorMessage = '';
  @observable
  Set<LetterNode> lettersUsed = {};

  // To help with animation
  @observable
  Set<LetterNode> oldLettersUsed = {};
  @observable
  bool hasGameEnded = false;
  @observable
  String? wordOfTheSession;
  @observable
  GameResult? gameResult;
  @observable
  MultiplayerGame? currentMatch;

  // Who's turn is it
  @observable
  String? turn;
  @observable
  String? winner;
  @observable
  bool isOpponentOnline = true;
  @observable
  int pointsWon = 0;
  @observable
  MultiPlayerStatsModel multiPlayerStatsModel =
      MultiPlayerStatsModel(points: 0);

  @observable
  bool hasErrorInStats = false;

  final difficultyConfig = DifficultyConfig(
    lettersCount: 5,
    rowCount: 6,
  );

  @action
  Future<void> loadStats() async {
    isLoading = true;
    final getUserStats = _functions.httpsCallable('getUserStats');
    try {
      HttpsCallableResult<dynamic> res = await getUserStats({});
      log('Response from callable function ${res.data.runtimeType} : ${res.data}');

      if (res.data != null && res.data is Map<String, dynamic>) {
        hasErrorInStats = false;
        multiPlayerStatsModel = MultiPlayerStatsModel.fromJson(res.data);
      } else {
        throw FormatException('Response data is not in expected format.');
      }
    } catch (error) {
      multiPlayerStatsModel = MultiPlayerStatsModel.fromJson({});
      hasErrorInStats = true;
      log('Error Response from callable function: $error');
    } finally {
      isLoading = false;
    }
  }

  @action
  void addLetter(String letter) {
    currentGameRow.push(LetterNode(
      letter: letter,
      evaluation: Evaluation.tbd,
    ));

    currentGameRow = currentGameRow;
  }

  @action
  void removeLastLetter() {
    currentGameRow.pop();
    currentGameRow = currentGameRow;
  }

  @action
  void submitGuess({bool remotely = true}) {
    status = Status.pending;
    if (!isBoardCompleted && !hasGameEnded) {
      if (validateGuess()) {
        oldLettersUsed = Set.from(lettersUsed);
        if (remotely) {
          _timerStore.resetMultiplayerTimerTo(
            currentMatch?.player1?.timeLeft ?? 120,
          );
          turn = opponent.userId;
          _submitGuessToServer();
        }
        _markCharacterEvaluation();
        _evaluateUsedLetters();

        submittedRows.add(currentGameRow.copy());
        increaseRowNumber();

        if (submittedRows.length == 1) {
          _eventLogger.log(Events.onlineMpGameStarted);
        }

        if (currentGameRow.isCorrect) {
          gameResult = GameResult.won;
          endGame();
        } else if (submittedRows.length < difficultyConfig.rowCount) {
          status = Status.done;
        } else if (!currentGameRow.isCorrect) {
          gameResult = GameResult.lost;
          endGame();
        }

        currentGameRow = GameRow();
      } else {
        status = Status.error;
      }
    } else {
      status = Status.done;
    }
  }

  @action
  void endGame() {
    hasGameEnded = true;
    _eventLogger.log(Events.onlineMpGameFinished);
  }

  @action
  Future<void> syncGuesses(List<String> serverGuesses) async {
    if (serverGuesses.length > submittedRows.length) {
      final guessLetters = serverGuesses.last.split('');
      for (final letter in guessLetters) {
        addLetter(letter);
        await Future.delayed(Duration(milliseconds: 100));
      }

      if (!hasGameEnded) {
        submitGuess(remotely: false);
      }
    }
  }

  Future<void> _submitGuessToServer() async {
    serverStatus = Status.pending;
    final makeGuess = _functions.httpsCallable('makeGuess');

    try {
      await makeGuess({
        'matchId': _matchMakingStore.currentMatchId,
        'guess': currentGameRow.toWord(),
      });
      serverStatus = Status.done;
      log('Guess submitted to server successfully.');
    } catch (error) {
      serverStatus = Status.error;
      log('Error Response from callable function');
      log(error.toString());
    }
  }

  void _markCharacterEvaluation() {
    List<String> wordOfTheDayChars = wordOfTheSession!.split('');

    int index = 0;
    // mark the ones that are correct
    currentGameRow.toList().forEach((element) {
      if (wordOfTheDayChars.elementAt(index) == element.letter) {
        element.evaluation = Evaluation.correct;
        wordOfTheDayChars[index] = '';
        // Add to letter used set
        lettersUsed.add(element);
      }

      index++;
    });

    index = 0;
    // mark the ones that are present
    currentGameRow.toList().forEach((element) {
      if (element.evaluation == Evaluation.tbd &&
          wordOfTheDayChars.contains(element.letter)) {
        element.evaluation = Evaluation.present;
        wordOfTheDayChars[wordOfTheDayChars.indexOf(element.letter)] = '';
        // Add to letter used set
        lettersUsed.add(element);
      }

      index++;
    });

    index = 0;
    // mark the ones that are absent
    currentGameRow.toList().forEach((element) {
      if (element.evaluation == Evaluation.tbd) {
        element.evaluation = Evaluation.absent;
      }

      // Add to letter used set
      lettersUsed.add(element);
      index++;
    });
  }

  void _evaluateUsedLetters() {
    currentGameRow.toList().forEach((guess) {
      try {
        final used = lettersUsed.firstWhere((used) => used == guess);

        if (used.evaluation == Evaluation.present &&
            guess.evaluation == Evaluation.correct) {
          lettersUsed.remove(used);
          lettersUsed.add(guess);
        }

        if (guess.evaluation == Evaluation.present &&
            used.evaluation == Evaluation.absent) {
          lettersUsed.remove(used);
          lettersUsed.add(guess);
        }
      } on StateError {
        return;
      }
    });
  }

  bool validateGuess() {
    if (!currentGameRow.isFull) {
      errorMessage = 'Not enough letters';
      return false;
    } else if (!wordList.wordExists(currentGameRow.toWord())) {
      errorMessage = 'Not in word list';
      return false;
    }
    return true;
  }

  @action
  increaseRowNumber() {
    if (currentRowNumber <= difficultyConfig.rowCount) {
      currentRowNumber++;
    }
  }

  @action
  void reset() {
    currentGameRow = GameRow();
    currentRowNumber = 1;
    submittedRows = ObservableList.of([]);
    status = null;
    serverStatus = null;
    errorMessage = '';
    lettersUsed = {};
    oldLettersUsed = {};
    hasGameEnded = false;
    wordOfTheSession = null;
    gameResult = null;
    currentMatch = null;
    turn = null;
  }

  @action
  void checkOpponentStatus() {
    isOpponentOnline = true;
    final userId = opponent.userId;
    if (userId == null) isOpponentOnline = false;
    final userStatusRef = FirebaseDatabase.instance.ref('/status/$userId');

    opponentStatusStream = userStatusRef.onValue.listen((event) {
      final data = event.snapshot.value;
      if (data != null) {
        final matchStatus = MatchStatus.fromJson(jsonDecode(jsonEncode(data)));

        isOpponentOnline = (matchStatus.state == 'online' &&
            matchStatus.lastMatchId == _matchMakingStore.currentMatchId);
      }
    });
  }

  void closeOpponentStatusStream() {
    opponentStatusStream?.cancel();
  }

  @action
  void calculatePoints() {
    switch (matchResult) {
      case GameResult.won:
        final rowWon = submittedRows.length;
        pointsWon = pointsMap[rowWon] ?? 0;
        break;
      case GameResult.lost:
        pointsWon = 0;
        break;
      case GameResult.draw:
        pointsWon = 3;
    }
  }

  @computed
  int get emptyRows {
    int empty = difficultyConfig.rowCount - (submittedRows.length + 1);
    return submittedRows.length == difficultyConfig.rowCount ? 0 : empty;
  }

  @computed
  bool get isBoardCompleted =>
      submittedRows.length == difficultyConfig.rowCount;

  @computed
  Player get opponent {
    final player1 = currentMatch!.player1!;
    final player2 = currentMatch!.player2!;

    if (player1.userId == _authService.user?.uid) {
      return player2;
    } else {
      return player1;
    }
  }

  @computed
  bool get isMyTurn {
    return turn == _authService.user?.uid;
  }

  @computed
  GameResult get matchResult {
    if (winner == _authService.user?.uid) {
      return GameResult.won;
    } else if (winner == opponent.userId) {
      return GameResult.lost;
    } else {
      return GameResult.draw;
    }
  }

  // Animation delay time
  int get timeToWait =>
      AnimationConstants.delayTileTimeInMilliseconds *
      difficultyConfig.lettersCount;
}
