import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/cupertino.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/firestore_keys.dart';
import 'package:wordle/src/core/constants/leaderboard.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/models/user_score.dart';
import 'package:wordle/src/model/get_leader_board_model.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/services/remote_config/remote_config_service_contract.dart';

import '../../../ui/widgets/show_loading.dart';

part 'multiplayer_leaderboard_store.g.dart';

class MultiplayerLeaderboardStore = _MultiplayerLeaderboardStore
    with _$MultiplayerLeaderboardStore;

abstract class _MultiplayerLeaderboardStore with Store {
  @observable
  Status? scoresStatus;
  @observable
  Status? myScoreStatus;
  @observable
  String errorMessage = '';
  @observable
  String myScoreErrorMessage = '';
  @observable
  ObservableList<UserScore> scores = ObservableList.of([]);
  @observable
  ObservableList<UserScore> top3Scores = ObservableList.of([]);
  @observable
  UserScore? myScore;
  @observable
  String myRank = '#';
  @observable
  bool isLoading = true;
  @observable
  ObservableList<Leaderboard> leadBoardScores = ObservableList.of([]);
  @observable
  String selectedFilter = leaderboardFilters.keys.first;

  final _functions = FirebaseFunctions.instance;
  GetLeaderBoardModel getLeaderBoardModel = GetLeaderBoardModel();

  final configService = ServiceLocator.locate<RemoteConfigServiceContract>();
  final _authService = ServiceLocator.locate<AuthService>();

  @action
  Future<void> onDropDownChanged(
      {required String? value, required BuildContext context}) async {
    selectedFilter = value ?? leaderboardFilters.keys.first;

    await loadLeaderBoard(context);
  }

  Future<void> loadLeaderBoard(BuildContext context) async {
    scoresStatus = Status.pending;
    showLoading(context);

    String selectedFilterHere = leaderboardFilters[selectedFilter]!;

    final getUserStats = _functions.httpsCallable('getLeaderBoards');
    try {
      HttpsCallableResult<dynamic> res =
          await getUserStats({"filter": selectedFilterHere});
      log('Response from callable function $selectedFilterHere ${res.data.runtimeType}  : ${res.data}');
      if (res.data != null && res.data is Map<String, dynamic>) {
        getLeaderBoardModel = GetLeaderBoardModel.fromJson(res.data);
        leadBoardScores =
            ObservableList.of(getLeaderBoardModel.leaderboard ?? []);
        log("debug score length is : $leadBoardScores");
      } else {
        throw FormatException('Response data is not in expected format.');
      }
    } catch (error) {
      leadBoardScores = ObservableList.of([]);
      errorMessage = 'Unable to retrieve scores.';
      log('Error Response from callable function: $error');
    } finally {
      hideLoading(context);
      scoresStatus = Status.done;
    }
  }

  Future<void> loadStats() async {
    myScoreStatus = Status.pending;
    final uid = _authService.user?.uid;
    try {
      final scoreSnapshot = await FirebaseFirestore.instance
          .collection(FirestoreKeys.scores)
          .doc(uid)
          .get();

      if (scoreSnapshot.exists) {
        myScore = UserScore.fromJson(scoreSnapshot.data() ?? {});
        myScore?.userId = scoreSnapshot.id;
        myScoreStatus = Status.done;
      } else {
        myScoreStatus = Status.noData;
      }
    } on Exception catch (_) {
      myScoreErrorMessage = 'Unable to retrieve your score.';
      myScoreStatus = Status.error;
    }
  }
}
