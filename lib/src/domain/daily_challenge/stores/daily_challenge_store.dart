import 'dart:convert';
import 'dart:developer';

import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_overlay_loader/flutter_overlay_loader.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/animation_constants.dart';
import 'package:wordle/src/core/constants/evaluation.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/constants/game_result.dart';
import 'package:wordle/src/core/helpers/app_admob.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/core/utils/word_list.dart';
import 'package:wordle/src/data/models/daily_game_session.dart';
import 'package:wordle/src/data/models/daily_wordle.dart';
import 'package:wordle/src/data/models/difficulty_config.dart';
import 'package:wordle/src/data/models/game_row.dart';
import 'package:wordle/src/data/models/letter_node.dart';
import 'package:wordle/src/data/repositories/ironsource_ad_repository.dart';
import 'package:wordle/src/domain/daily_challenge/stores/timer_store.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';
import 'package:wordle/src/services/stats_service.dart';
import 'package:wordle/src/ui/widgets/show_loading.dart';

import '../../../core/helpers/random.dart';
import '../../../core/helpers/toast.dart';
import '../../../core/strings/app_string.dart';
import '../../../ui/widgets/reusable_timer_bar.dart';
import '../../auth/store/auth_store.dart';
import '../../main/stores/main_store.dart';
import '../../unlimited_challenge/stores/hint_manager.dart';

part 'daily_challenge_store.g.dart';

class DailyChallengeStore = _DailyChallengeStore with _$DailyChallengeStore;

abstract class _DailyChallengeStore with Store {
  final wordList = ServiceLocator.locate<WordList>();
  final _localStorage = ServiceLocator.locate<LocalStorage>();
  final statsService = ServiceLocator.locate<StatsService>();
  final timerStore = ServiceLocator.locate<TimerStore>();
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final _adRepository = ServiceLocator.locate<IronSourceAdRepository>();
  final _functions = FirebaseFunctions.instance;
  final mainStore = ServiceLocator.locate<MainStore>();
  final _authStore = ServiceLocator.locate<AuthStore>();
  TimerController? timerController;

  @observable
  bool isStatsVisibleToUser = false;
  @observable
  bool? isGameEndedBeforePlayed;
  @observable
  GameRow currentGameRow = GameRow();
  @observable
  int currentRowNumber = 1;
  @observable
  ObservableList<GameRow> submittedRows = ObservableList.of([]);
  @observable
  Status? status;
  @observable
  Status? gamePostingStatus;
  @observable
  int startedAt = 0;
  @observable
  String? gameId;
  @observable
  String errorMessage = '';
  @observable
  Set<LetterNode> lettersUsed = {};

  // To help with animation
  @observable
  Set<LetterNode> oldLettersUsed = {};
  @observable
  bool hasGameEnded = false;
  @observable
  GameResult? gameResult;
  @observable
  Status? syncStatus;
  @observable
  Status? ongoingGameStatus;
  @observable
  DifficultyConfig difficultyConfig = DifficultyConfig(
    lettersCount: 5,
    rowCount: 6,
  );
  @observable
  GameStatus gameStatus5 = GameStatus.notStarted;
  @observable
  GameStatus gameStatus6 = GameStatus.notStarted;
  @observable
  GameStatus gameStatus7 = GameStatus.notStarted;

  // Animation delay time
  @observable
  int timeToWait = 0;
  @observable
  int puzzle = 0;
  @observable
  HintManager? hintManager;
  @observable
  String? wordOfTheSession;
  @observable
  ObservableMap<int, LetterNode?> hints = ObservableMap.of({});
  @observable
  String? helpingHandWord;
  @observable
  bool isBoosterUsed = false;

  @action
  Future<void> initialise(
      {required DifficultyConfig difficultyConfig,
      required bool shouldSyncDailyWord}) async {
    this.difficultyConfig = difficultyConfig;
    validateLastTimePlayed();
    final gameSessionJson = _localStorage.retrieve(
      difficultyConfig.dailyGameSessionKey,
    );

    hintManager = HintManager(
      lettersCount: this.difficultyConfig.lettersCount,
      isPlayPass: mainStore.isPlayPass,
      isSubscriber: mainStore.isSubscribed,
    );

    if (gameSessionJson.isNotEmpty) {
      final session = DailyGameSession.fromJson(jsonDecode(gameSessionJson));
      currentGameRow = session.currentGameRow;
      currentRowNumber = session.currentRowNumber;
      submittedRows = ObservableList.of(session.submittedRows);
      gameResult = session.gameResult;
      errorMessage = session.errorMessage;
      lettersUsed = session.lettersUsed;
      hasGameEnded = session.hasGameEnded;
      wordOfTheSession = session.wordOfTheSession;
      hints = ObservableMap.of(session.hints);
      startedAt = session.startedAt;
      gameId = session.gameId;
      isBoosterUsed = session.isBoosterUsed;

      if (_authStore.user?.uid == session.userId) {
        hintManager?.updateCounts(
          handHintCount: session.handHintCount,
          handHintCountAd: session.handHintCountAd,
          bulbHintCount: session.bulbHintCount,
          bulbHintCountAd: session.bulbHintCountAd,
        );
      } else {
        reset(hardReset: true);
      }
    } else {
      if (shouldSyncDailyWord ||
          wordOfTheSession == null ||
          wordOfTheSession == "") {
        await syncWordOfTheDay();
      }
      isGameEndedBeforePlayed = hasGameEnded;
      currentGameRow = GameRow(maxLetters: this.difficultyConfig.lettersCount);
      startedAt = DateTime.timestamp().millisecondsSinceEpoch;
    }

    await _setGameState();
    helpingHandWord = null;
    timeToWait = AnimationConstants.delayTileTimeInMilliseconds *
        this.difficultyConfig.lettersCount;

    _localStorage.saveInt(LSKey.lastPuzzlePlayed, wordList.puzzleNumber);
    timerStore.startDailyTimer();
  }

  @action
  Future<void> _setGameState() async {
    switch (difficultyConfig.lettersCount) {
      case 5:
        gameStatus5 = await _getGameStateStatus();
        break;
      case 6:
        gameStatus6 = await _getGameStateStatus();
        break;
      default:
        gameStatus7 = await _getGameStateStatus();
        break;
    }
  }

  Future<GameStatus> _getGameStateStatus() async {
    GameStatus _gameStats = GameStatus.notStarted;
    if (hasGameEnded) {
      _gameStats = GameStatus.completed;
    } else if (submittedRows.isNotEmpty) {
      _gameStats = GameStatus.inProgress;
    }
    return _gameStats;
  }

  Future<void> reportSession() async {
    final session = DailyGameSession(
      currentGameRow: currentGameRow,
      currentRowNumber: currentRowNumber,
      errorMessage: errorMessage,
      hasGameEnded: hasGameEnded,
      lettersUsed: lettersUsed,
      gameResult: gameResult,
      submittedRows: submittedRows,
      wordOfTheSession: wordOfTheSession,
      handHintCount: hintManager?.handHintCount ?? 0,
      bulbHintCount: hintManager?.bulbHintCount ?? 0,
      handHintCountAd: hintManager?.handHintCountAd ?? 0,
      bulbHintCountAd: hintManager?.bulbHintCountAd ?? 0,
      isBoosterUsed: isBoosterUsed,
      gameId: gameId,
      startedAt: startedAt,
      hints: hints,
      userId: _authStore.user?.uid ?? "",
    );
    await _localStorage.save(
      difficultyConfig.dailyGameSessionKey,
      jsonEncode(session),
    );
  }

  @action
  Future<void> syncWordOfTheDay() async {
    // If no guesses have been submitted sync daily word
    if (submittedRows.isEmpty) {
      syncStatus = Status.pending;

      var body = {
        'name': "${difficultyConfig.lettersCount}Letter",
        'isoDate': DateTime.now().toIso8601String(),
        'isSpeedMode': mainStore.isSpeedMode,
      };

      final getDailyWord = _functions.httpsCallable('getDailyWordFor');
      await getDailyWord(body).then((response) {
        final dailyWordle = DailyWordle.fromJson(response.data);
        puzzle = dailyWordle.id;
        wordOfTheSession ??= dailyWordle.solution;
        syncStatus = Status.done;
        log('Daily word synced $body ${response.data}');
      }).catchError((error) {
        syncStatus = Status.error;
        log('Error Response from getDailyWord function $body');
        log(error.toString());
      });
    }
  }

  @action
  Future<void> onHintClicked({
    required DifficultyConfig? selectedDifficultyConfig,
    required BuildContext context,
  }) async {
    if (hintManager?.isHintAdFree(isHelpingHand: false) == true) {
      await useHint();
      _eventLogger.log(mainStore.isSpeedMode
          ? Events.didUseLetterSpeedHint
          : Events.didUseLetterHint);
    } else {
      timerController?.pause();
      Loader.show(
        context,
        progressIndicator: const CircularProgressIndicator.adaptive(),
      );
      await _adRepository
          .loadAndShowRewardedAdNoSSV(AdRewardUnitId.skipUnlimitedLevel)
          .then((value) async {
        Loader.hide();
        await useHint();
        timerController?.resume();
        _eventLogger.log(mainStore.isSpeedMode
            ? Events.didUseLetterSpeedHintAd
            : Events.didUseLetterHintAd);
      }).catchError((error) {
        Loader.hide();
        timerController?.resume();
        toast(context: context, msg: AppStrings.adErrorMessage);
      });
    }
  }

  @action
  Future<void> onHelpingHandClick({
    required DifficultyConfig? selectedDifficultyConfig,
    required BuildContext context,
  }) async {
    if (hintManager?.isHintAdFree(isHelpingHand: true) == true) {
      await useHelpingHand();
      _eventLogger.log(mainStore.isSpeedMode
          ? Events.didUseWordSpeedHint
          : Events.didUseWordHint);
    } else {
      Loader.show(
        context,
        progressIndicator: const CircularProgressIndicator.adaptive(),
      );
      timerController?.pause();
      await _adRepository
          .loadAndShowRewardedAdNoSSV(AdRewardUnitId.skipUnlimitedLevel)
          .then((value) async {
        Loader.hide();
        await useHelpingHand();
        timerController?.resume();
        _eventLogger.log(mainStore.isSpeedMode
            ? Events.didUseWordSpeedHintAd
            : Events.didUseWordHintAd);
      }).catchError((error) {
        Loader.hide();
        timerController?.resume();
        toast(context: context, msg: AppStrings.adErrorMessage);
      });
    }
  }

  @action
  Future<void> useHelpingHand() async {
    helpingHandWord = wordList.getHelperWord(wordOfTheSession!, lettersUsed);
    isBoosterUsed = true;
    hintManager?.useHint(
      isHelpingHand: true,
    );
    await reportSession();
  }

  @action
  Future<void> useHint() async {
    isBoosterUsed = true;
    final hint = _determineHint();
    if (hint != null && !hints.containsKey(hint.key)) {
      hints[hint.key] = hint.value;
      hints = hints; // updates the state
      hintManager?.useHint(
        isHelpingHand: false,
      );
      await reportSession();
    }
  }

  MapEntry<int, LetterNode>? _determineHint() {
    final range = {for (var i = 0; i < difficultyConfig.lettersCount; i++) i};
    List<String> wordOfTheSessionChars = wordOfTheSession!.split('');

    for (var submittedRow in submittedRows) {
      submittedRow.toList().asMap().forEach((index, element) {
        if (element.evaluation == Evaluation.correct) {
          range.removeWhere((rangeIndex) => rangeIndex == index);
        }
      });
    }

    hints.keys.toList().forEach((hintIndex) {
      range.removeWhere((rangeIndex) => rangeIndex == hintIndex);
    });

    final rangeIndex = randomElement<int>(range);
    if (rangeIndex != null) {
      final letter = wordOfTheSessionChars.elementAt(rangeIndex);

      return MapEntry(rangeIndex, LetterNode(letter: letter));
    } else {
      return null;
    }
  }

  @action
  void setTimerController(TimerController? newTimerController) {
    timerController = newTimerController;
  }

  void postGameScore() {
    if (mainStore.isSpeedMode) {
      postSpeedGameScore();
    } else {
      postNormalGameScore();
    }
  }

  @action
  Future<void> getOngoingSpeedGame({
    required BuildContext context,
  }) async {
    if (mainStore.isSpeedMode) {
      showLoading(context);
      ongoingGameStatus = Status.pending;
      final post = _functions.httpsCallable('getOngoingSpeedGame');
      String name = "${difficultyConfig.lettersCount}Letter";
      String type = "daily";

      final Map<String, dynamic> data = {
        "name": name,
        "type": type,
      };

      try {
        final HttpsCallableResult<dynamic> response = await post(data);
        bool isGameAlreadyExist = response.data["isGameAlreadyExist"] ?? false;

        if (!isGameAlreadyExist) {
          if (gameId == null) {
            _startSpeedGame(context: context);
          } else {
            if (!hasGameEnded) {
              gameResult = GameResult.lost;
              endGame();
              showGameToast(
                  context: context, msg: "Time’s up!", isSuccess: false);
            }
          }
        } else {
          int totalSeconds = response.data["totalSeconds"];
          int usedSeconds = response.data["usedSeconds"];
          wordOfTheSession = response.data["solution"];
          timerController?.updateDuration(
            Duration(
              seconds: totalSeconds,
            ),
          );
          timerController?.updateUsedSeconds(usedSeconds);
          bool isFirstLaunch =
              _localStorage.retrieveBool(LSKey.isFirstLaunch) ?? true;
          if (!isFirstLaunch) {
            timerController?.resume();
          }
          reportSession();
        }
        log('Success getOngoingSpeedGame:$data\n${response.data} ');
      } catch (error, stackTrace) {
        log('Error getOngoingSpeedGame:$data\n${error.toString()} $stackTrace');
      } finally {
        ongoingGameStatus = Status.done;
        hideLoading(context);
      }
    }
  }

  @action
  Future<void> _startSpeedGame({
    required BuildContext context,
  }) async {
    if (mainStore.isSpeedMode) {
      showLoading(context);
      gamePostingStatus = Status.pending;
      final post = _functions.httpsCallable('startSpeedGame');
      String name = "${difficultyConfig.lettersCount}Letter";
      String type = "daily";

      final Map<String, dynamic> data = {
        "solution": wordOfTheSession,
        "startedAt": startedAt,
        "name": name,
        "puzzle": puzzle,
        "type": type,
      };

      try {
        final HttpsCallableResult<dynamic> response = await post(data);
        gameId = response.data["gameId"];
        int totalSeconds = response.data["totalSeconds"];
        timerController?.pause();
        timerController?.updateDuration(Duration(
          seconds: totalSeconds,
        ));
        timerController?.updateUsedSeconds(0);
        bool isFirstLaunch =
            _localStorage.retrieveBool(LSKey.isFirstLaunch) ?? true;
        if (!isFirstLaunch) {
          timerController?.resume();
        }
        reportSession();
        log('Success startSpeedGame:$data\n${response.data} ');
      } catch (error, stackTrace) {
        log('Error startSpeedGame:$data\n${error.toString()} $stackTrace');
      } finally {
        gamePostingStatus = Status.done;
        hideLoading(context);
      }
    }
  }

  Future<void> startSpeedGame({
    required BuildContext context,
    required DifficultyConfig selectedDifficultyConfig,
  }) async {
    reset(hardReset: true);
    await initialise(
      difficultyConfig: selectedDifficultyConfig,
      shouldSyncDailyWord: true,
    );
    await _startSpeedGame(context: context);
  }

  @action
  Future<void> postSpeedGameScore() async {
    if (gameId == "" || gameId == null) {
      gamePostingStatus = Status.pending;
    }
    final post = _functions.httpsCallable('postSpeedGameScore');

    List<String> guesses = getUsedWordArray();

    String? guess =
        (timerController?.remainingTime.inSeconds != 0 && guesses.isNotEmpty)
            ? guesses.last
            : null;

    final Map<String, dynamic> data = {
      "gameId": gameId,
      "guess": guess,
      "usedSeconds": timerController?.usedTime.inSeconds,
      "isBoosterUsed": isBoosterUsed,
    };

    try {
      final HttpsCallableResult<dynamic> response = await post(data);
      log('success posting game score:$data\n${response.data} ');
    } catch (error, stackTrace) {
      log('Error posting game score:$data\n${error.toString()} $stackTrace');
    } finally {
      gamePostingStatus = Status.done;
    }
  }

  @action
  Future<void> postNormalGameScore() async {
    if (gameId == "" || gameId == null) {
      gamePostingStatus = Status.pending;
    }
    final post = _functions.httpsCallable('postSingleGameScore');
    List<String> guesses = getUsedWordArray();
    String name = "${difficultyConfig.lettersCount}Letter";
    String type = "daily";

    // Create a data map for clarity and maintainability
    final Map<String, dynamic> data = {
      "gameId": gameId,
      "guesses": guesses,
      "solution": wordOfTheSession,
      "startedAt": startedAt,
      "name": name,
      "puzzle": puzzle,
      "type": type,
    };

    try {
      final HttpsCallableResult<dynamic> response = await post(data);
      gameId = response.data;
      reportSession();
      log('success posting game score:$data\n${response.data} ');
    } catch (error, stackTrace) {
      log('Error posting game score:$data\n${error.toString()} $stackTrace');
    } finally {
      gamePostingStatus = Status.done;
    }
  }

  @action
  void validateLastTimePlayed() {
    final lastPuzzlePlayed = _localStorage.retrieveInt(LSKey.lastPuzzlePlayed);
    if (lastPuzzlePlayed > 0 && wordList.puzzleNumber > lastPuzzlePlayed) {
      reset(hardReset: true);
    }
  }

  @action
  void addLetter(String letter) {
    currentGameRow.push(LetterNode(
      letter: letter,
      evaluation: Evaluation.tbd,
    ));

    currentGameRow = currentGameRow;
  }

  @action
  void removeLastLetter() {
    currentGameRow.pop();
    currentGameRow = currentGameRow;
  }

  @action
  Future<void> submitGuess() async {
    if (gamePostingStatus != Status.pending) {
      gamePostingStatus = Status.pending;
      status = Status.pending;
      if (!isBoardCompleted && !hasGameEnded) {
        if (validateGuess()) {
          oldLettersUsed = Set.from(lettersUsed);
          _markCharacterEvaluation();
          _evaluateUsedLetters();
          submittedRows.add(currentGameRow.copy());
          increaseRowNumber();
          reportSession();
          postGameScore();
          if (submittedRows.length == 1) {
            _eventLogger.log(mainStore.isSpeedMode
                ? Events.dailySpeedGameStarted
                : Events.dailyGameStarted);
            _adRepository.loadSingleInterstialAd(AdInterstitialUnitId.daily);
          }

          if (currentGameRow.isCorrect) {
            gameResult = GameResult.won;
            endGame();
          } else if (submittedRows.length < difficultyConfig.rowCount) {
            status = Status.done;
          } else if (!currentGameRow.isCorrect) {
            gameResult = GameResult.lost;
            endGame();
          }
          currentGameRow = GameRow(maxLetters: difficultyConfig.lettersCount);
          validateHint();
          reportSession();
        } else {
          status = Status.error;
          gamePostingStatus = Status.done;
        }
      } else {
        gamePostingStatus = Status.done;
        status = Status.done;
      }
      _setGameState();
    }
  }

  @action
  void endGameDueToTimeout() {
    if (!hasGameEnded) {
      postGameScore();
      gameResult = GameResult.lost;
      endGame();
      errorMessage = 'Time\'s up!';
      //  _eventLogger.log(Events.dailyGameTimeoutReached);
    }
  }

  @action
  void endGame() {
    ongoingGameStatus = Status.done;
    isGameEndedBeforePlayed = false;
    hasGameEnded = true;
    reportSession();
    _setGameState();

    _eventLogger.log(
        mainStore.isSpeedMode
            ? Events.dailySpeedGameFinished
            : Events.dailyGameFinished,
        params: {
          EventParam.dailyChallengeResult: gameResult.toString().split('.').last
        });
    _adRepository.showDailyAd(
      AdInterstitialUnitId.daily,
      delayTimeInMilliseconds: timeToWait + 2000,
    );
  }

  void _markCharacterEvaluation() {
    List<String> wordOfTheDayChars = wordOfTheSession?.split('') ?? [];
    int index = 0;
    // mark the ones that are correct
    currentGameRow.toList().forEach((element) {
      if (wordOfTheDayChars.elementAt(index) == element.letter) {
        element.evaluation = Evaluation.correct;
        wordOfTheDayChars[index] = '';
        // Add to letter used set
        lettersUsed.add(element);
      }

      index++;
    });

    index = 0;
    // mark the ones that are present
    currentGameRow.toList().forEach((element) {
      if (element.evaluation == Evaluation.tbd &&
          wordOfTheDayChars.contains(element.letter)) {
        element.evaluation = Evaluation.present;
        wordOfTheDayChars[wordOfTheDayChars.indexOf(element.letter)] = '';
        // Add to letter used set
        lettersUsed.add(element);
      }

      index++;
    });

    index = 0;
    // mark the ones that are absent
    currentGameRow.toList().forEach((element) {
      if (element.evaluation == Evaluation.tbd) {
        element.evaluation = Evaluation.absent;
      }

      // Add to letter used set
      lettersUsed.add(element);
      index++;
    });
  }

  void _evaluateUsedLetters() {
    currentGameRow.toList().forEach((guess) {
      try {
        final used = lettersUsed.firstWhere((used) => used == guess);

        if (used.evaluation == Evaluation.present &&
            guess.evaluation == Evaluation.correct) {
          lettersUsed.remove(used);
          lettersUsed.add(guess);
        }

        if (guess.evaluation == Evaluation.present &&
            used.evaluation == Evaluation.absent) {
          lettersUsed.remove(used);
          lettersUsed.add(guess);
        }
      } on StateError {
        return;
      }
    });
  }

  bool validateGuess() {
    if (!currentGameRow.isFull) {
      errorMessage = 'Not enough letters';
      return false;
    } else if (!wordList.wordExists(currentGameRow.toWord())) {
      errorMessage = 'Not in word list';
      return false;
    }
    return true;
  }

  @action
  void validateHint() {
    final range = {for (var i = 0; i < difficultyConfig.lettersCount; i++) i};

    for (var submittedRow in submittedRows) {
      submittedRow.toList().asMap().forEach((index, element) {
        if (element.evaluation == Evaluation.correct) {
          range.removeWhere((rangeIndex) => rangeIndex == index);
        }
      });
    }

    hints.removeWhere((hintIndex, _) => !range.contains(hintIndex));
  }

  @action
  increaseRowNumber() {
    if (currentRowNumber <= difficultyConfig.rowCount) {
      currentRowNumber++;
    }
  }

  @action
  List<String> getUsedWordArray() {
    List<String> words = [];
    for (var row in submittedRows) {
      String word = "";
      for (var element in row.letterList) {
        word = word + element.letter;
      }

      words.add(word);
      word = "";
    }
    return words;
  }

  @action
  void reset({bool hardReset = false}) {
    currentGameRow = GameRow(maxLetters: difficultyConfig.lettersCount);
    currentRowNumber = 1;
    submittedRows = ObservableList.of([]);
    status = null;
    ongoingGameStatus = null;
    errorMessage = '';
    isGameEndedBeforePlayed = null;
    lettersUsed = {};
    oldLettersUsed = {};
    startedAt = 0;
    hasGameEnded = false;
    gameResult = null;
    isBoosterUsed = false;
    gameId = null;
    wordOfTheSession = null;
    hints = ObservableMap.of({});

    if (hardReset) {
      _localStorage.remove(difficultyConfig.dailyGameSessionKey);
      _setGameState();
    }
  }

  @action
  void checkAndResetGameIfNeeded() {
    if (startedAt == 0) {
      reset();
      return;
    }

    DateTime storedDate = DateTime.fromMillisecondsSinceEpoch(startedAt);
    DateTime now = DateTime.now();
    DateTime today = DateTime(now.year, now.month, now.day);

    if (storedDate.year != today.year ||
        storedDate.month != today.month ||
        storedDate.day != today.day) {
      reset(
        hardReset: true,
      );
    } else {
      reset();
    }
  }

  @computed
  bool get canShowAd => !mainStore.isPlayPass && !mainStore.isSubscribed;

  @computed
  bool get isBoardCompleted =>
      submittedRows.length == difficultyConfig.rowCount;

  int speedModeDuration(DifficultyConfig selectedDifficultyConfig) {
    return (selectedDifficultyConfig.lettersCount == 5
            ? mainStore.speed5LetterDuration
            : selectedDifficultyConfig.lettersCount == 6
                ? mainStore.speed6LetterDuration
                : mainStore.speed7LetterDuration) ??
        0;
  }
}
