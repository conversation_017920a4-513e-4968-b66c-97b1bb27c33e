import 'dart:async';

import 'package:flutter/material.dart';
import 'package:mobx/mobx.dart';

part 'timer_store.g.dart';

class TimerStore = _TimerStore with _$TimerStore;

abstract class _TimerStore with Store {
  @observable
  String timeToNextWord = '';
  @observable
  String myTimer = '';
  @observable
  String opponentTimer = '';
  @observable
  Timer? multiplayerTimer;

  @action
  void startDailyTimer() {
    Timer.periodic(const Duration(seconds: 1), (_) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final diff = today.difference(now);
      final seconds = (diff.inSeconds % 60).floor();
      var minutes = ((diff.inSeconds / 60) % 60).floor();
      var hours = ((diff.inMilliseconds / (1000 * 60 * 60)) % 24).floor();

      timeToNextWord =
          '${hours < 10 ? "0" + hours.toString() : hours}:${minutes < 10 ? "0" + minutes.toString() : minutes}:${seconds < 10 ? "0" + seconds.toString() : seconds}';
    });
  }

  @action
  void initMultiplayerTimer(int seconds) {
    final minutes = ((seconds / 60) % 60).floor();
    final timerSeconds = (seconds % 60).floor();
    final timerString =
        '${minutes < 10 ? "0" + minutes.toString() : minutes}:${timerSeconds < 10 ? "0" + timerSeconds.toString() : timerSeconds}';
    myTimer = timerString;
    opponentTimer = timerString;
    multiplayerTimer?.cancel();
    multiplayerTimer = null;
  }

  @action
  void startMultiplayerTimer(
    int seconds, {
    bool isMyTurn = true,
    VoidCallback? onEnd,
  }) {
    multiplayerTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      var minutes = ((seconds / 60) % 60).floor();
      var timerSeconds = (seconds % 60).floor();
      var timerString =
          '${minutes < 10 ? "0" + minutes.toString() : minutes}:${timerSeconds < 10 ? "0" + timerSeconds.toString() : timerSeconds}';
      if (isMyTurn) {
        myTimer = timerString;
        if (seconds == 0) {
          onEnd?.call();
          multiplayerTimer?.cancel();
        }
      } else {
        opponentTimer = timerString;
        if (seconds == 0) {
          onEnd?.call();
          multiplayerTimer?.cancel();
        }
      }

      seconds--;
    });
  }

  @action
  void resetMultiplayerTimerTo(int seconds) {
    multiplayerTimer?.cancel();
    final minutes = ((seconds / 60) % 60).floor();
    final timerSeconds = (seconds % 60).floor();
    final timerString =
        '${minutes < 10 ? "0" + minutes.toString() : minutes}:${timerSeconds < 10 ? "0" + timerSeconds.toString() : timerSeconds}';

    myTimer = timerString;
    opponentTimer = timerString;
  }
}
