import 'dart:convert';
import 'dart:developer';

import 'package:cloud_functions/cloud_functions.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/config_param.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/models/daily_comment.dart';
import 'package:wordle/src/data/models/daily_comments_response.dart';
import 'package:wordle/src/domain/main/stores/main_store.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/services/remote_config/remote_config_service_contract.dart';

part 'daily_comment_store.g.dart';

class DailyCommentStore = _DailyCommentStore with _$DailyCommentStore;

abstract class _DailyCommentStore with Store {
  final _functions = FirebaseFunctions.instance;
  final _authService = ServiceLocator.locate<AuthService>();
  final _configService = ServiceLocator.locate<RemoteConfigServiceContract>();
  final _mainStore = ServiceLocator.locate<MainStore>();

  @observable
  Status? commentsStatus;
  @observable
  Status? loadMoreStatus;
  @observable
  Status? submissionStatus;
  @observable
  ObservableList<DailyComment> dailyComments = ObservableList.of([]);
  @observable
  bool isBannedComments = false;

  /// This number keeps track of the total comments received from the API.
  /// It allows us to know whether there are more results to be paginated
  @observable
  int retrievedCount = 0;
  @observable
  int pageCount = 0;

  List<int> nativeAdIndexes = [];

  void _setNativeAdIndexes() {
    List<int> defaultIndexes = [2, 11];

    final json = _configService.getJson(ConfigParam.dailyCommentAdPlacement);

    if (json.isNotEmpty) {
      try {
        final List<int> indexes = List.from(jsonDecode(json));
        nativeAdIndexes = indexes;
      } on Exception catch (_) {
        nativeAdIndexes = defaultIndexes;
      }
    } else {
      nativeAdIndexes = defaultIndexes;
    }
  }

  void _removeFlaggedComments() {
    final userId = _authService.user!.uid;
    dailyComments.removeWhere((comment) => comment.flags.contains(userId));
  }

  @action
  Future<void> getComments(int puzzleNumber) async {
    commentsStatus = Status.pending;
    final getDailyComments = _functions.httpsCallable('getDailyComments');

    try {
      final result = await getDailyComments({
        'puzzleNumber': puzzleNumber,
        'lastCommentId': null,
      });
      final response = DailyCommentsResponse.fromJson(Map.of(result.data));
      retrievedCount = response.items.length;
      pageCount = response.pageCount;

      dailyComments = ObservableList.of(response.items);
      _removeFlaggedComments();
      _insertAds();

      if (response.items.isEmpty) {
        commentsStatus = Status.noData;
      } else {
        commentsStatus = Status.done;
      }

      log('Comments fetched successfully (length after inserting ads: ${dailyComments.length})');
    } on Exception catch (error) {
      commentsStatus = Status.error;
      log('There was an error fetching comments');
      log(error.toString());
    }
  }

  @action
  Future<void> loadMoreComments(int puzzleNumber, String lastCommentId) async {
    loadMoreStatus = Status.pending;

    final getDailyComments = _functions.httpsCallable('getDailyComments');

    try {
      final result = await getDailyComments({
        'puzzleNumber': puzzleNumber,
        'lastCommentId': lastCommentId,
      });

      final response = DailyCommentsResponse.fromJson(Map.of(result.data));
      retrievedCount = response.items.length;
      pageCount = response.pageCount;

      dailyComments.addAll(response.items);
      _removeFlaggedComments();
      _insertAds();

      if (response.items.isEmpty) {
        loadMoreStatus = Status.noData;
      } else {
        loadMoreStatus = Status.done;
      }

      log('More comments fetched successfully (length after inserting ads: ${dailyComments.length})');
    } on Exception catch (error) {
      loadMoreStatus = Status.error;
      log('There was an error fetching more comments');
      log(error.toString());
    }
  }

  /// This method is used to insert dummy comments with an id of "ad". This
  /// tells the UI where to show ads in the comments list. [nativeAdIndexes]
  /// tells you where to start inserting ads with the first number, the second
  /// number tells you have far apart the next ad should be. Minus the first
  /// number from the second to get the offset of how far the ads should be
  /// apart. For example if [nativeAdIndexes] is [2, 11], then the offset would
  /// be 9.
  void _insertAds() {
    if (!_mainStore.isSubscribed && !_mainStore.isPlayPass) {
      _setNativeAdIndexes();
      final offset = (nativeAdIndexes[1] - nativeAdIndexes[0]);
      for (int i = nativeAdIndexes[0]; i <= dailyComments.length; i += offset) {
        if (dailyComments.length == i || dailyComments[i].id != 'ad') {
          dailyComments.insert(
            i,
            DailyComment(
              id: 'ad',
              comment: '',
              puzzleNumber: 0,
              photoURL: '',
              createdAt: 0,
            ),
          );
        }
      }
    }
  }

  @action
  Future<void> submitComment({
    required int puzzleNumber,
    required String comment,
  }) async {
    dailyComments.insert(
      0,
      DailyComment(
        id: '',
        comment: comment,
        puzzleNumber: puzzleNumber,
        photoURL: _authService.user?.getPhotoURL() ?? '',
        displayName: _authService.user?.getDisplayName(),
        createdAt: DateTime.now().millisecondsSinceEpoch,
      ),
    );

    if (commentsStatus == Status.noData) {
      commentsStatus = Status.done;
    }

    submissionStatus = Status.pending;

    final makeDailyComment = _functions.httpsCallable('makeDailyComment');

    try {
      await makeDailyComment({
        'puzzleNumber': puzzleNumber,
        'comment': comment,
      });
      await Future.delayed(Duration(seconds: 2));

      submissionStatus = Status.done;

      log('Comment submitted successfully');
    } on Exception catch (error) {
      dailyComments.removeAt(0);
      submissionStatus = Status.error;

      if (dailyComments.isEmpty) {
        commentsStatus = Status.noData;
      }

      log('There was an error submmitting comments');
      log(error.toString());
    }
  }

  Future<void> flagComment(String commentId) async {
    final flagComment = _functions.httpsCallable('flagComment');

    try {
      await flagComment({
        'commentId': commentId,
      });
      log('Comment flagged successfully');
    } on Exception catch (error) {
      log('There was an error flagging comment');
      log(error.toString());
    }
  }
}
