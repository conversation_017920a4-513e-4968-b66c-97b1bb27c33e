import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/config_param.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/services/remote_config/remote_config_service_contract.dart';

part 'hint_manager.g.dart';

class HintManager = _HintManager with _$HintManager;

abstract class _HintManager with Store {
  final RemoteConfigServiceContract _configService =
      ServiceLocator.locate<RemoteConfigServiceContract>();

  @observable
  int handHintCount = 0;
  @observable
  int handHintCountAd = 0;
  @observable
  int bulbHintCount = 0;
  @observable
  int bulbHintCountAd = 0;

  final bool isPlayPass;
  final bool isSubscriber;

  _HintManager(
      {required int lettersCount,
      required this.isPlayPass,
      required this.isSubscriber}) {
    fetchAndSetCountFromConfig(lettersCount, isPlayPass);
  }

  Future<void> fetchAndSetCountFromConfig(
      int lettersCount, bool isPlayPass) async {
    if (isPlayPass) {
      if (lettersCount == 5 || lettersCount == 0) {
        handHintCount = _configService.getInt(ConfigParam.hints5HandPlayPass);
        bulbHintCount =
            _configService.getInt(ConfigParam.hints5LightBulbPlayPass);
      } else if (lettersCount == 6) {
        handHintCount = _configService.getInt(ConfigParam.hints6HandPlayPass);
        bulbHintCount =
            _configService.getInt(ConfigParam.hints6LightBulbPlayPass);
      } else {
        handHintCount = _configService.getInt(ConfigParam.hints7HandPlayPass);
        bulbHintCount =
            _configService.getInt(ConfigParam.hints7LightBulbPlayPass);
      }
    } else if (isSubscriber) {
      if (lettersCount == 5 || lettersCount == 0) {
        handHintCount = _configService.getInt(ConfigParam.hints5HandSub);
        bulbHintCount = _configService.getInt(ConfigParam.hints5LightBulbSub);
      } else if (lettersCount == 6) {
        handHintCount = _configService.getInt(ConfigParam.hints6HandSub);
        bulbHintCount = _configService.getInt(ConfigParam.hints6LightBulbSub);
      } else {
        handHintCount = _configService.getInt(ConfigParam.hints7HandSub);
        bulbHintCount = _configService.getInt(ConfigParam.hints7LightBulbSub);
      }
    } else {
      if (lettersCount == 5 || lettersCount == 0) {
        handHintCount = _configService.getInt(ConfigParam.hints5Hand);
        handHintCountAd = _configService.getInt(ConfigParam.hints5HandAds);
        bulbHintCount = _configService.getInt(ConfigParam.hints5LightBulb);
        bulbHintCountAd = _configService.getInt(ConfigParam.hints5LightBulbAds);
      } else if (lettersCount == 6) {
        handHintCount = _configService.getInt(ConfigParam.hints6Hand);
        handHintCountAd = _configService.getInt(ConfigParam.hints6HandAds);
        bulbHintCount = _configService.getInt(ConfigParam.hints6LightBulb);
        bulbHintCountAd = _configService.getInt(ConfigParam.hints6LightBulbAds);
      } else {
        handHintCount = _configService.getInt(ConfigParam.hints7Hand);
        handHintCountAd = _configService.getInt(ConfigParam.hints7HandAds);
        bulbHintCount = _configService.getInt(ConfigParam.hints7LightBulb);
        bulbHintCountAd = _configService.getInt(ConfigParam.hints7LightBulbAds);
      }
    }
  }

  @action
  int getCount({
    required bool isHelpingHand,
  }) {
    bool isAdFree = isHintAdFree(isHelpingHand: isHelpingHand);

    int count = 0;

    if (isHelpingHand) {
      count = isAdFree ? handHintCount : handHintCountAd;
    } else {
      count = isAdFree ? bulbHintCount : bulbHintCountAd;
    }
    return count;
  }

  @action
  void updateCounts({
    required int handHintCount,
    required int handHintCountAd,
    required int bulbHintCount,
    required int bulbHintCountAd,
  }) {
    this.handHintCount = handHintCount;
    this.handHintCountAd = handHintCountAd;
    this.bulbHintCount = bulbHintCount;
    this.bulbHintCountAd = bulbHintCountAd;
  }

  @action
  bool isHintAvailable({
    required bool isHelpingHand,
  }) {
    bool isAvailable = true;
    bool isAdFree = isHintAdFree(isHelpingHand: isHelpingHand);

    if (isAdFree) {
      if (isHelpingHand) {
        isAvailable = handHintCount > 0;
      } else {
        isAvailable = bulbHintCount > 0;
      }
    } else {
      if (isHelpingHand) {
        isAvailable = handHintCountAd > 0;
      } else {
        isAvailable = bulbHintCountAd > 0;
      }
    }

    return isAvailable;
  }

  @action
  void useHint({required bool isHelpingHand}) {
    if (isHelpingHand) {
      if (handHintCount > 0) {
        handHintCount--;
      } else if (handHintCountAd > 0) {
        handHintCountAd--;
      }
    } else {
      if (bulbHintCount > 0) {
        bulbHintCount--;
      } else if (bulbHintCountAd > 0) {
        bulbHintCountAd--;
      }
    }
  }

  @action
  bool isHintAdFree({required bool isHelpingHand}) {
    bool isHintAdFree = true;

    if (isPlayPass) {
      isHintAdFree = true;
    } else if (isHelpingHand) {
      isHintAdFree = handHintCount > 0;
    } else {
      isHintAdFree = bulbHintCount > 0;
    }

    return isHintAdFree;
  }
}
