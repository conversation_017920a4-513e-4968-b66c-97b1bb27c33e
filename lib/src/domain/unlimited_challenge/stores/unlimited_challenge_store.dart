import 'dart:convert';
import 'dart:developer';

import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_overlay_loader/flutter_overlay_loader.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/animation_constants.dart';
import 'package:wordle/src/core/constants/evaluation.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/constants/game_result.dart';
import 'package:wordle/src/core/helpers/app_admob.dart';
import 'package:wordle/src/core/helpers/random.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/strings/app_string.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/core/utils/word_list.dart';
import 'package:wordle/src/data/models/difficulty_config.dart';
import 'package:wordle/src/data/models/game_row.dart';
import 'package:wordle/src/data/models/letter_node.dart';
import 'package:wordle/src/data/models/unlimited_game_session.dart';
import 'package:wordle/src/data/repositories/ironsource_ad_repository.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';
import 'package:wordle/src/services/stats_service.dart';
import 'package:wordle/src/ui/widgets/show_loading.dart';

import '../../../core/enums/app_enums.dart';
import '../../../core/helpers/toast.dart';
import '../../../ui/widgets/confirm_streak_dialog.dart';
import '../../../ui/widgets/reusable_timer_bar.dart';
import '../../auth/store/auth_store.dart';
import '../../main/stores/main_store.dart';
import 'hint_manager.dart';

part 'unlimited_challenge_store.g.dart';

class UnlimitedChallengeStore = _UnlimitedChallengeStore
    with _$UnlimitedChallengeStore;

abstract class _UnlimitedChallengeStore with Store {
  final wordList = ServiceLocator.locate<WordList>();
  final _localStorage = ServiceLocator.locate<LocalStorage>();
  final statsService = ServiceLocator.locate<StatsService>();
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final _adRepository = ServiceLocator.locate<IronSourceAdRepository>();
  final mainStore = ServiceLocator.locate<MainStore>();
  final _authStore = ServiceLocator.locate<AuthStore>();
  final _functions = FirebaseFunctions.instance;

  @observable
  TimerController? timerController;
  @observable
  bool isStatsVisibleToUser = false;
  @observable
  bool? isGameEndedBeforePlayed;
  @observable
  GameRow currentGameRow = GameRow();
  @observable
  int currentRowNumber = 1;
  @observable
  ObservableList<GameRow> submittedRows = ObservableList.of([]);
  @observable
  Status? status;
  @observable
  Status? gamePostingStatus;
  @observable
  Status? ongoingGameStatus;
  @observable
  String errorMessage = '';
  @observable
  Set<LetterNode> lettersUsed = {};
  @observable
  Set<LetterNode> oldLettersUsed = {};
  @observable
  String currentStreak = "0";
  @observable
  bool hasGameEnded = false;
  @observable
  bool isUsedExtraRow = false;
  @observable
  KeepPlayingPopupType keepPlayingPopupType = KeepPlayingPopupType.none;
  @observable
  String? wordOfTheSession;
  @observable
  int? wordOfTheSessionIndex;
  @observable
  int? startedAt;
  @observable
  String? gameId;
  @observable
  GameResult? gameResult;
  @observable
  DifficultyConfig difficultyConfig = DifficultyConfig(
    lettersCount: 5,
    rowCount: 6,
  );
  @observable
  int timeToWait = 0;
  @observable
  String? helpingHandWord;
  @observable
  ObservableMap<int, LetterNode?> hints = ObservableMap.of({});
  @observable
  bool enableBoosters = true;
  @observable
  Status? skipStatus;
  @observable
  HintManager? hintManager;
  @observable
  bool isBoosterUsed = false;

  @action
  Future<void> initialise({
    required DifficultyConfig difficultyConfig,
    required BuildContext context,
  }) async {
    this.difficultyConfig = difficultyConfig;
    _localStorage.save(
      LSKey.difficultyConfig,
      jsonEncode(difficultyConfig),
    );

    hintManager = HintManager(
      lettersCount: this.difficultyConfig.lettersCount,
      isPlayPass: mainStore.isPlayPass,
      isSubscriber: mainStore.isSubscribed,
    );

    final gameSessionJson =
        _localStorage.retrieve(difficultyConfig.unlimitedGameSessionKey);

    if (gameSessionJson.isNotEmpty) {
      final session =
          UnlimitedGameSession.fromJson(jsonDecode(gameSessionJson));
      currentGameRow = session.currentGameRow;
      currentRowNumber = session.currentRowNumber;
      submittedRows = ObservableList.of(session.submittedRows);
      gameResult = session.gameResult;
      errorMessage = session.errorMessage;
      lettersUsed = session.lettersUsed;
      hasGameEnded = session.hasGameEnded;
      wordOfTheSession = session.wordOfTheSession;
      startedAt = session.startedAt;
      gameId = session.gameId;
      isBoosterUsed = session.isBoosterUsed;
      wordOfTheSessionIndex = session.wordOfTheSessionIndex;
      hints = ObservableMap.of(session.hints);
      keepPlayingPopupType = session.keepPlayingPopupType;
      isUsedExtraRow = session.isUsedExtraRow;
      if (_authStore.user?.uid == session.userId) {
        hintManager?.updateCounts(
          handHintCount: session.handHintCount,
          handHintCountAd: session.handHintCountAd,
          bulbHintCount: session.bulbHintCount,
          bulbHintCountAd: session.bulbHintCountAd,
        );
      } else {
        reset(hardReset: true);
      }
    } else {
      currentGameRow = GameRow(maxLetters: this.difficultyConfig.lettersCount);
      startedAt = DateTime.timestamp().millisecondsSinceEpoch;
    }
    if (isUsedExtraRow) {
      _addExtraRow(shouldLogEvent: !isUsedExtraRow);
    }
    isGameEndedBeforePlayed = hasGameEnded;
    helpingHandWord = null;
    timeToWait = AnimationConstants.delayTileTimeInMilliseconds *
        this.difficultyConfig.lettersCount;
    wordOfTheSession ??= wordList.getNewWord(difficultyConfig).word;
    wordOfTheSessionIndex ??= wordList.getNewWord(difficultyConfig).index;
    statsService
        .loadStats(
      parentContext: context,
      gameType: difficultyConfig.unlimitedStatsType.gameType,
      gameName: difficultyConfig.unlimitedStatsType.gameName,
    )
        .then((value) {
      currentStreak = statsService.currentStreak;
    });
  }

  @action
  Future<void> reportSession() async {
    final session = UnlimitedGameSession(
      currentGameRow: currentGameRow,
      currentRowNumber: currentRowNumber,
      errorMessage: errorMessage,
      hasGameEnded: hasGameEnded,
      keepPlayingPopupType: keepPlayingPopupType,
      lettersUsed: lettersUsed,
      gameResult: gameResult,
      startedAt: startedAt,
      isUsedExtraRow: isUsedExtraRow,
      gameId: gameId,
      isBoosterUsed: isBoosterUsed,
      submittedRows: submittedRows,
      wordOfTheSession: wordOfTheSession,
      wordOfTheSessionIndex: wordOfTheSessionIndex,
      hints: hints,
      handHintCount: hintManager?.handHintCount ?? 0,
      bulbHintCount: hintManager?.bulbHintCount ?? 0,
      handHintCountAd: hintManager?.handHintCountAd ?? 0,
      bulbHintCountAd: hintManager?.bulbHintCountAd ?? 0,
      userId: _authStore.user?.uid ?? "",
    );

    await _localStorage.save(
      difficultyConfig.unlimitedGameSessionKey,
      jsonEncode(session),
    );
  }

  @action
  void setTimerController(TimerController? newTimerController) {
    timerController = newTimerController;
  }

  @action
  Future<void> onHintClicked({
    required DifficultyConfig? selectedDifficultyConfig,
    required BuildContext context,
  }) async {
    if (hintManager?.isHintAdFree(isHelpingHand: false) == true) {
      await useHint();
      _eventLogger.log(mainStore.isSpeedMode
          ? Events.didUseLetterSpeedHint
          : Events.didUseLetterHint);
    } else {
      timerController?.pause();
      Loader.show(
        context,
        progressIndicator: const CircularProgressIndicator.adaptive(),
      );
      await _adRepository
          .loadAndShowRewardedAdNoSSV(AdRewardUnitId.skipUnlimitedLevel)
          .then((value) async {
        Loader.hide();

        await useHint();
        timerController?.resume();
        _eventLogger.log(mainStore.isSpeedMode
            ? Events.didUseLetterSpeedHintAd
            : Events.didUseLetterHintAd);
      }).catchError((error) {
        Loader.hide();
        timerController?.resume();
        toast(context: context, msg: AppStrings.adErrorMessage);
      });
    }
  }

  @action
  Future<void> onHelpingHandClick({
    required DifficultyConfig? selectedDifficultyConfig,
    required BuildContext context,
  }) async {
    if (hintManager?.isHintAdFree(isHelpingHand: true) == true) {
      await useHelpingHand();
      _eventLogger.log(mainStore.isSpeedMode
          ? Events.didUseWordSpeedHint
          : Events.didUseWordHint);
    } else {
      timerController?.pause();
      Loader.show(
        context,
        progressIndicator: const CircularProgressIndicator.adaptive(),
      );
      await _adRepository
          .loadAndShowRewardedAdNoSSV(AdRewardUnitId.skipUnlimitedLevel)
          .then((value) async {
        Loader.hide();
        await useHelpingHand();
        timerController?.resume();
        _eventLogger.log(mainStore.isSpeedMode
            ? Events.didUseWordSpeedHintAd
            : Events.didUseWordHintAd);
      }).catchError((error) {
        Loader.hide();
        timerController?.resume();
        toast(context: context, msg: AppStrings.adErrorMessage);
      });
    }
  }

  @action
  onGiveUpClick({required BuildContext parentContext}) {
    int currentStreak = int.parse(statsService.currentStreak);

    if (currentStreak > 0) {
      showDialog(
        context: parentContext,
        builder: (_) => ConfirmStreakDialog(
          streakCount: currentStreak,
          parentContext: parentContext,
        ),
      );
    } else {
      endGame(
        finalGameResult: gameResult = GameResult.lost,
        canPop: true,
      );
    }
  }

  @action
  onUnlockClick({required BuildContext context}) {
    _handleAdClick(
      context: context,
      onSuccess: _addExtraRow,
      adId: AdRewardUnitId.skipUnlimitedLevel,
    );
  }

  @action
  onKeepItStreakClick({required BuildContext context}) {
    _handleAdClick(
      context: context,
      onSuccess: () {
        reportSession();
        _keepStreakAndLoseGame(context);
        _eventLogger.log(mainStore.isSpeedMode
            ? Events.didUseSkipLevelSpeedAd
            : Events.didUseSkipLevelAd);
      },
      adId: AdRewardUnitId.skipUnlimitedLevel,
    );
  }

  @action
  void _addExtraRow({bool shouldLogEvent = true}) {
    isUsedExtraRow = true;
    int oldRowCount = difficultyConfig.rowCount;
    int oldLetterCount = difficultyConfig.lettersCount;
    difficultyConfig = DifficultyConfig(
      lettersCount: oldLetterCount,
      rowCount: oldRowCount + 1,
    );
    reportSession();
    if (shouldLogEvent) {
      _eventLogger.log(Events.didUseExtraGuess);
      AppRouter.pop();
    }
  }

  @action
  Future<void> _keepStreakAndLoseGame(BuildContext context) async {
    showLoading(context);
    await markSingleGameAsSkippedNetworkCall(context);
    hideLoading(context);
  }

  Future<void> _handleAdClick({
    required BuildContext context,
    required Function onSuccess,
    required AdRewardUnitId adId,
  }) async {
    Loader.show(
      context,
      progressIndicator: const CircularProgressIndicator.adaptive(),
    );
    if (canShowAd) {
      _adRepository.loadAndShowRewardedAdNoSSV(adId).then((value) async {
        await onSuccess();
        Loader.hide();
      }).catchError((error) {
        Loader.hide();
        toast(context: context, msg: AppStrings.adErrorMessage);
      });
    } else {
      await onSuccess();
      Loader.hide();
    }
  }

  @action
  Future<void> markSingleGameAsSkippedNetworkCall(BuildContext context) async {
    final markSingleGameAsSkipped =
        _functions.httpsCallable('markSingleGameAsSkipped');

    final Map<String, dynamic> data = {
      "gameId": gameId,
      "isSpeedMode": mainStore.isSpeedMode,
    };

    try {
      final HttpsCallableResult<dynamic> response =
          await markSingleGameAsSkipped(data);
      _eventLogger.log(mainStore.isSpeedMode
          ? Events.didUseSaveSpeedStreak
          : Events.didUseSaveStreak);
      endGame(
        finalGameResult: GameResult.lost,
        canResetStreak: false,
        showAd: false,
        canPop: true,
      );

      log('success markSingleGameAsSkipped: ${response.data}  $data');
    } catch (error) {
      log('Error markSingleGameAsSkipped: ${error.toString()}  $data');
    }
  }

  @action
  void addLetter(String letter) {
    currentGameRow.push(LetterNode(
      letter: letter,
      evaluation: Evaluation.tbd,
    ));

    currentGameRow = currentGameRow;
  }

  @action
  void removeLastLetter() {
    currentGameRow.pop();
    currentGameRow = currentGameRow;
  }

  @action
  Future<void> submitGuess() async {
    if (gamePostingStatus != Status.pending) {
      gamePostingStatus = Status.pending;
      status = Status.pending;
      enableBoosters = false;
      if (!isBoardCompleted && !hasGameEnded) {
        if (validateGuess()) {
          oldLettersUsed = Set.from(lettersUsed);
          _markCharacterEvaluation();
          _evaluateUsedLetters();
          submittedRows.add(currentGameRow.copy());
          increaseRowNumber();
          reportSession();
          postGameScore();
          if (submittedRows.length == 1) {
            _eventLogger.log(
                mainStore.isSpeedMode
                    ? Events.unlimitedSpeedGameStarted
                    : Events.unlimitedGameStarted,
                params: {
                  EventParam.unlimitedChallengeDifficulty:
                      difficultyConfig.lettersCount,
                });
            _adRepository.trackGamesPlayed();
            if (_adRepository.isAdAvailable) {
              _adRepository
                  .loadFrequentInterstitialAd(AdInterstitialUnitId.unlimited);
            }
          }

          if (currentGameRow.isCorrect) {
            endGame(finalGameResult: GameResult.won);
          } else if (submittedRows.length < difficultyConfig.rowCount) {
            status = Status.done;
          } else if (!currentGameRow.isCorrect) {
            if (mainStore.isSpeedMode && int.parse(currentStreak) == 0) {
              endGame(
                finalGameResult: GameResult.lost,
              );
            } else if (keepPlayingPopupType == KeepPlayingPopupType.none) {
              if (mainStore.isSpeedMode) {
                keepPlayingPopupType = KeepPlayingPopupType.keepStreaks;
              } else {
                keepPlayingPopupType = KeepPlayingPopupType.outOfTries;
              }
              status = Status.done;
            } else if (int.parse(currentStreak) != 0) {
              keepPlayingPopupType = KeepPlayingPopupType.keepStreaks;
            } else {
              endGame(
                finalGameResult: GameResult.lost,
              );
            }
          }

          currentGameRow = GameRow(maxLetters: difficultyConfig.lettersCount);
          validateHint();
          reportSession();

          Future.delayed(Duration(milliseconds: timeToWait)).then((value) {
            enableBoosters = true;
          });
        } else {
          enableBoosters = true;
          gamePostingStatus = Status.done;
          status = Status.error;
        }
      } else {
        gamePostingStatus = Status.done;
        status = Status.done;
      }
    }
  }

  @action
  void endGameDueToTimeout({required BuildContext context}) {
    if (!hasGameEnded) {
      postGameScore();
      showGameToast(context: context, msg: "Time’s up!", isSuccess: false);
      endGame(finalGameResult: GameResult.lost);
      errorMessage = 'Time\'s up!';
      //  _eventLogger.log(Events.dailyGameTimeoutReached);
    }
  }

  @action
  void endGame({
    required GameResult finalGameResult,
    bool canResetStreak = true,
    bool showAd = true,
    canPop = false,
  }) {
    gameResult = finalGameResult;
    ongoingGameStatus = Status.done;
    isGameEndedBeforePlayed = false;
    hasGameEnded = true;

    keepPlayingPopupType = KeepPlayingPopupType.none;

    if (canPop) {
      AppRouter.pop();
    }

    if (difficultyConfig.lettersCount != 5) {
      _localStorage.save(
        difficultyConfig.wordlistLastSolvedKey,
        wordOfTheSession!,
      );
    }

    reportSession();
    _eventLogger.log(
        mainStore.isSpeedMode
            ? Events.unlimitedSpeedGameFinished
            : Events.unlimitedGameFinished,
        params: {
          EventParam.unlimitedChallengeResult:
              gameResult.toString().split('.').last,
          EventParam.unlimitedChallengeDifficulty:
              difficultyConfig.lettersCount,
        });

    if (_adRepository.isAdAvailable && showAd) {
      _adRepository.showUnlimitedAd(
        AdInterstitialUnitId.unlimited,
        delayTimeInMilliseconds: timeToWait + 2000,
      );
    }
  }

  @action
  bool validateGuess() {
    if (!currentGameRow.isFull) {
      errorMessage = AppStrings.notEnoughLettersText;
      return false;
    } else if (!wordList.wordExists(currentGameRow.toWord())) {
      errorMessage = AppStrings.notInWordListText;
      return false;
    }
    return true;
  }

  @action
  void increaseRowNumber() {
    if (currentRowNumber <= difficultyConfig.rowCount) {
      currentRowNumber++;
    }
  }

  @action
  Future<void> useHelpingHand() async {
    isBoosterUsed = true;
    helpingHandWord = wordList.getHelperWord(wordOfTheSession!, lettersUsed);
    hintManager?.useHint(
      isHelpingHand: true,
    );
    await reportSession();
  }

  @action
  Future<void> useHint() async {
    isBoosterUsed = true;
    final hint = _determineHint();
    if (hint != null && !hints.containsKey(hint.key)) {
      hints[hint.key] = hint.value;
      hints = hints;
      hintManager?.useHint(
        isHelpingHand: false,
      );
      await reportSession();
    }
  }

  void postGameScore() {
    if (mainStore.isSpeedMode) {
      _postSpeedGameScoreNetworkCall();
    } else {
      _postGameScoreNetworkCall();
    }
  }

  @action
  Future<void> getOngoingSpeedGame({
    required BuildContext context,
    required DifficultyConfig selectedDifficultyConfig,
  }) async {
    if (mainStore.isSpeedMode) {
      ongoingGameStatus = Status.pending;
      showLoading(context);
      final post = _functions.httpsCallable('getOngoingSpeedGame');
      String name = "${difficultyConfig.lettersCount}Letter";
      String type = "unlimited";

      final Map<String, dynamic> data = {
        "name": name,
        "type": type,
      };

      try {
        final HttpsCallableResult<dynamic> response = await post(data);

        bool isGameAlreadyExist = response.data["isGameAlreadyExist"] ?? false;
        if (!isGameAlreadyExist) {
          if (gameId != null) {
            if (!hasGameEnded) {
              endGame(finalGameResult: GameResult.lost);
            }
          } else {
            await _startSpeedGame(context: context);
          }
        } else {
          int totalSeconds = response.data["totalSeconds"];
          int usedSeconds = response.data["usedSeconds"];
          wordOfTheSession = response.data["solution"];
          timerController?.updateDuration(
            Duration(
              seconds: totalSeconds,
            ),
          );
          timerController?.updateUsedSeconds(usedSeconds);
          bool isFirstLaunch =
              _localStorage.retrieveBool(LSKey.isFirstLaunch) ?? true;
          if (!isFirstLaunch) {
            timerController?.resume();
          }
          reportSession();
        }
        log('Success getOngoingSpeedGame:$data\n${response.data} ');
      } catch (error, stackTrace) {
        log('Error getOngoingSpeedGame:$data\n${error.toString()} $stackTrace');
      } finally {
        hideLoading(context);
        ongoingGameStatus = Status.done;
      }
    }
  }

  Future<void> startSpeedGame({
    required BuildContext context,
    required DifficultyConfig selectedDifficultyConfig,
  }) async {
    reset(hardReset: true);
    await initialise(
      difficultyConfig: selectedDifficultyConfig,
      context: context,
    );
    await _startSpeedGame(context: context);
  }

  @action
  Future<void> _startSpeedGame({
    required BuildContext context,
  }) async {
    if (mainStore.isSpeedMode) {
      showLoading(context);
      gamePostingStatus = Status.pending;
      final post = _functions.httpsCallable('startSpeedGame');
      String name = "${difficultyConfig.lettersCount}Letter";
      String type = "unlimited";

      final Map<String, dynamic> data = {
        "solution": wordOfTheSession,
        "startedAt": startedAt,
        "name": name,
        "puzzle": wordOfTheSessionIndex,
        "type": type,
      };

      try {
        final HttpsCallableResult<dynamic> response = await post(data);
        gameId = response.data["gameId"];
        int totalSeconds = response.data["totalSeconds"];
        wordOfTheSession = response.data["solution"];
        timerController?.pause();
        timerController?.updateDuration(Duration(
          seconds: totalSeconds,
        ));
        timerController?.updateUsedSeconds(0);
        bool isFirstLaunch =
            _localStorage.retrieveBool(LSKey.isFirstLaunch) ?? true;
        if (!isFirstLaunch) {
          timerController?.resume();
        }
        reportSession();
        log('Success startSpeedGame:$data\n${response.data} ');
      } catch (error, stackTrace) {
        log('Error startSpeedGame:$data\n${error.toString()} $stackTrace');
      } finally {
        gamePostingStatus = Status.done;
        hideLoading(context);
      }
    }
  }

  @action
  Future<void> _postSpeedGameScoreNetworkCall() async {
    if (gameId == "" || gameId == null) {
      gamePostingStatus = Status.pending;
    }
    final post = _functions.httpsCallable('postSpeedGameScore');

    List<String> guesses = getUsedWordArray();

    String? guess =
        (timerController?.remainingTime.inSeconds != 0 && guesses.isNotEmpty)
            ? guesses.last
            : null;

    final Map<String, dynamic> data = {
      "gameId": gameId,
      "guess": guess,
      "usedSeconds": timerController?.usedTime.inSeconds,
      "isBoosterUsed": isBoosterUsed,
    };

    try {
      final HttpsCallableResult<dynamic> response = await post(data);
      log('success posting game score:$data\n${response.data} ');
    } catch (error, stackTrace) {
      log('Error posting game score:$data\n${error.toString()} $stackTrace');
    } finally {
      gamePostingStatus = Status.done;
    }
  }

  @action
  Future<void> _postGameScoreNetworkCall() async {
    if (gameId == "" || gameId == null) {
      gamePostingStatus = Status.pending;
    }
    final post = _functions.httpsCallable('postSingleGameScore');
    List<String> guesses = getUsedWordArray();
    String solution = wordOfTheSession ?? "";
    String name = "${difficultyConfig.lettersCount}Letter";
    String type = "unlimited";

    final Map<String, dynamic> data = {
      "gameId": gameId,
      "guesses": guesses,
      "solution": solution,
      "startedAt": startedAt,
      "name": name,
      "puzzle": wordOfTheSessionIndex,
      "type": type,
    };

    try {
      final HttpsCallableResult<dynamic> response = await post(data);
      gameId = response.data;
      reportSession();
      log('success posting game score: ${response.data}  $data');
    } catch (error) {
      log('Error posting game score: ${error.toString()}  $data');
    } finally {
      gamePostingStatus = Status.done;
    }
  }

  @action
  List<String> getUsedWordArray() {
    List<String> words = [];
    for (var row in submittedRows) {
      String word = "";
      for (var element in row.letterList) {
        word = word + element.letter;
      }

      words.add(word);
      word = "";
    }
    return words;
  }

  void _markCharacterEvaluation() {
    List<String> wordOfTheSessionChars = wordOfTheSession!.split('');

    int index = 0;
    // mark the ones that are correct
    currentGameRow.toList().forEach((element) {
      if (wordOfTheSessionChars.elementAt(index) == element.letter) {
        element.evaluation = Evaluation.correct;
        wordOfTheSessionChars[index] = '';
        // Add to letter used set
        lettersUsed.add(element);
      }

      index++;
    });

    index = 0;
    // mark the ones that are present
    currentGameRow.toList().forEach((element) {
      if (element.evaluation == Evaluation.tbd &&
          wordOfTheSessionChars.contains(element.letter)) {
        element.evaluation = Evaluation.present;
        wordOfTheSessionChars[wordOfTheSessionChars.indexOf(element.letter)] =
            '';
        // Add to letter used set
        lettersUsed.add(element);
      }

      index++;
    });

    index = 0;
    // mark the ones that are absent
    currentGameRow.toList().forEach((element) {
      if (element.evaluation == Evaluation.tbd) {
        element.evaluation = Evaluation.absent;
      }

      // Add to letter used set
      lettersUsed.add(element);
      index++;
    });
  }

  void _evaluateUsedLetters() {
    currentGameRow.toList().forEach((guess) {
      try {
        final used = lettersUsed.firstWhere((used) => used == guess);

        if (used.evaluation == Evaluation.present &&
            guess.evaluation == Evaluation.correct) {
          lettersUsed.remove(used);
          lettersUsed.add(guess);
        }

        if (guess.evaluation == Evaluation.present &&
            used.evaluation == Evaluation.absent) {
          lettersUsed.remove(used);
          lettersUsed.add(guess);
        }
      } on StateError {
        return;
      }
    });
  }

  MapEntry<int, LetterNode>? _determineHint() {
    final range = {for (var i = 0; i < difficultyConfig.lettersCount; i++) i};
    List<String> wordOfTheSessionChars = wordOfTheSession!.split('');

    for (var submittedRow in submittedRows) {
      submittedRow.toList().asMap().forEach((index, element) {
        if (element.evaluation == Evaluation.correct) {
          range.removeWhere((rangeIndex) => rangeIndex == index);
        }
      });
    }

    hints.keys.toList().forEach((hintIndex) {
      range.removeWhere((rangeIndex) => rangeIndex == hintIndex);
    });

    final rangeIndex = randomElement<int>(range);
    if (rangeIndex != null) {
      final letter = wordOfTheSessionChars.elementAt(rangeIndex);

      return MapEntry(rangeIndex, LetterNode(letter: letter));
    } else {
      return null;
    }
  }

  @action
  void validateHint() {
    final range = {for (var i = 0; i < difficultyConfig.lettersCount; i++) i};

    for (var submittedRow in submittedRows) {
      submittedRow.toList().asMap().forEach((index, element) {
        if (element.evaluation == Evaluation.correct) {
          range.removeWhere((rangeIndex) => rangeIndex == index);
        }
      });
    }

    hints.removeWhere((hintIndex, _) => !range.contains(hintIndex));
  }

  @action
  void skipLevel() {
    skipStatus = Status.pending;
    _adRepository
        .loadAndShowRewardedAdNoSSV(AdRewardUnitId.skipUnlimitedLevel)
        .then((value) => skipStatus = Status.done)
        .catchError((error) => skipStatus = Status.error);
  }

  @action
  void reset({bool hardReset = false}) {
    currentGameRow = GameRow(maxLetters: difficultyConfig.lettersCount);
    keepPlayingPopupType = KeepPlayingPopupType.none;
    currentRowNumber = 1;
    submittedRows = ObservableList.of([]);
    status = null;
    ongoingGameStatus = null;
    errorMessage = '';
    lettersUsed = {};
    oldLettersUsed = {};
    hasGameEnded = false;
    startedAt = null;
    gameId = null;
    isGameEndedBeforePlayed = null;
    wordOfTheSession = null;
    hints = ObservableMap.of({});
    wordOfTheSessionIndex = null;
    isBoosterUsed = false;
    gameResult = null;
    isUsedExtraRow = false;

    if (hardReset) {
      _localStorage.remove(difficultyConfig.unlimitedGameSessionKey);
    }
  }

  @computed
  bool get isBoardCompleted =>
      submittedRows.length == difficultyConfig.rowCount;

  @computed
  bool get canShowAd => !mainStore.isPlayPass && !mainStore.isSubscribed;

  @computed
  bool get isHintAvailable {
    final hint = _determineHint();
    return hint != null &&
        !hints.containsKey(hint.key) &&
        enableBoosters &&
        !hasGameEnded;
  }

  @computed
  bool get isHelpingHandAvailable => enableBoosters && !hasGameEnded;

  @computed
  bool get isSkipAvailable =>
      enableBoosters && !hasGameEnded && submittedRows.isNotEmpty;

  int speedModeDuration(DifficultyConfig selectedDifficultyConfig) {
    return (selectedDifficultyConfig.lettersCount == 5
            ? mainStore.speed5LetterDuration
            : selectedDifficultyConfig.lettersCount == 6
                ? mainStore.speed6LetterDuration
                : mainStore.speed7LetterDuration) ??
        0;
  }
}
