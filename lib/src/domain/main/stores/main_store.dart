import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/config_param.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/repositories/ironsource_ad_repository.dart';

import '../../../services/remote_config/remote_config_service_contract.dart';

part 'main_store.g.dart';

class MainStore = _MainStore with _$MainStore;

abstract class _MainStore with Store {
  final _localStorage = ServiceLocator.locate<LocalStorage>();
  final RemoteConfigServiceContract _configService =
      ServiceLocator.locate<RemoteConfigServiceContract>();

  @observable
  String currentRouteName = '/';
  @observable
  int coins = 0;
  @observable
  bool isSubscribed = false;
  @observable
  int multiPlayerGamesPlayed = 0;
  @observable
  bool isPlayPass = false;
  @observable
  bool isSpeedMode = false;
  @observable
  int? speed5LetterDuration;
  @observable
  int? speed6LetterDuration;
  @observable
  int? speed7LetterDuration;

  @observable
  @action
  void updatePlayPassValue(bool value) {
    isPlayPass = value;
    IronSourceAdRepository.isPlayPass = value;
  }

  @action
  void updateMultiPlayerGamesPlayed({required int num}) {
    multiPlayerGamesPlayed = num;
  }

  @action
  updateGameMode(bool isSpeedMode) {
    this.isSpeedMode = isSpeedMode;
    if (isSpeedMode) {
      setSpeedModeDuration();
    }
  }

  void setSpeedModeDuration() {
    if (speed5LetterDuration == null ||
        speed6LetterDuration == null ||
        speed7LetterDuration == null) {
      speed5LetterDuration =
          _configService.getInt(ConfigParam.speed5LetterDuration);
      speed6LetterDuration =
          _configService.getInt(ConfigParam.speed6LetterDuration);
      speed7LetterDuration =
          _configService.getInt(ConfigParam.speed7LetterDuration);
    }
  }

  @computed
  bool get isFirstLaunch {
    bool firstLaunch = _localStorage.retrieveBool(LSKey.isFirstLaunch) ?? true;
    return firstLaunch;
  }

  @computed
  bool get isFirstMultiplayerLaunch {
    bool firstLaunch =
        _localStorage.retrieveBool(LSKey.isFirstMultiplayerLaunch) ?? true;
    return firstLaunch;
  }

  @computed
  bool get isTermsAccepted {
    return _localStorage.retrieveBool(LSKey.isTermsAccepted) ?? false;
  }

  @computed
  bool get isAllowedToPlayMultiplayer {
    return coins >= 10 || isSubscribed || isPlayPass;
  }
}
