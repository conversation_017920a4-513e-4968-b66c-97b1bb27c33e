import 'dart:async';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/services/auth/auth_service.dart';

part 'connection_store.g.dart';

class ConnectionStore = _ConnectionStore with _$ConnectionStore;

abstract class _ConnectionStore with Store {
  final _authService = ServiceLocator.locate<AuthService>();
  final isOffline = {
    'state': 'offline',
    'last_seen': ServerValue.timestamp,
    'last_match_id': null,
  };

  final isOnline = {
    'state': 'online',
    'last_seen': ServerValue.timestamp,
    'last_match_id': null,
  };
  StreamSubscription<DatabaseEvent>? connectionStreamSub;
  DatabaseReference? userStatusRef;

  void handleConnection(String? matchId) {
    if (_authService.isAuthenticated()) {
      goOnline();
      // Set the last match id that is being played
      isOffline['last_match_id'] = isOnline['last_match_id'] = matchId;
      // Create a reference to this user's specific status node.
      // This is where we will store data about being online/offline.
      userStatusRef =
          FirebaseDatabase.instance.ref('/status/' + _authService.user!.uid);
      final connectedRef = FirebaseDatabase.instance.ref(".info/connected");
      if (!kIsWeb) {
        userStatusRef?.keepSynced(true);
      }

      // Create a reference to the special '.info/connected' path in
      // Realtime Database. This path returns `true` when connected
      // and `false` when disconnected.
      connectionStreamSub = connectedRef.onValue.listen((event) {
        final isConnected = event.snapshot.value as bool? ?? false;
        // If we're not currently connected, don't do anything.
        if (!isConnected) return;

        // If we are currently connected, then use the 'onDisconnect()'
        // method to add a set which will only trigger once this
        // client has disconnected by closing the app,
        // losing internet, or any other means.
        userStatusRef?.onDisconnect().set(isOffline).then((value) {
          // The promise returned from .onDisconnect().set() will
          // resolve as soon as the server acknowledges the onDisconnect()
          // request, NOT once we've actually disconnected:
          // https://firebase.google.com/docs/reference/js/firebase.database.OnDisconnect

          // We can now safely set ourselves as 'online' knowing that the
          // server will mark us as offline once we lose connection.
          userStatusRef?.set(isOnline);
        });
      });
    }
  }

  void closeConnection() {
    goOffline();
    connectionStreamSub?.cancel();
  }

  void goOnline() {
    FirebaseDatabase.instance.goOnline();
  }

  void goOffline() {
    FirebaseDatabase.instance.goOffline();
  }
}
