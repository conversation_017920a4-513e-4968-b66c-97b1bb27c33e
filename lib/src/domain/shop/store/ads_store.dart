// import 'package:google_mobile_ads/google_mobile_ads.dart';
// import 'package:ironsource_mediation/ironsource_mediation.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/ad_placements.dart';
import 'package:wordle/src/core/helpers/app_admob.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/repositories/ironsource_ad_repository.dart';
import 'package:wordle/src/services/auth/auth_service.dart';

part 'ads_store.g.dart';

class AdsStore = _AdsStore with _$AdsStore;

abstract class _AdsStore with Store {
  @observable
  Status? status;
  @observable
  String errorMessage = '';
  final _authService = ServiceLocator.locate<AuthService>();
  final _adRepository = ServiceLocator.locate<IronSourceAdRepository>();

  @action
  void watchRewardedAd(AdRewardUnitId unitId) {
    if (_authService.isAuthenticated()) {
      status = Status.pending;

      _adRepository
          .loadAndShowRewardedAdWithAdPlacement(
              unitId, adPlacement10Coins, _authService.user!.uid)
          .then((value) {
        status = Status.done;
      }).onError((error, stackTrace) {
        status = Status.error;
      });
    }
  }
}
