import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/product_type.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/models/product.dart';
import 'package:wordle/src/model/verify_purchase_model.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';

import '../../../core/constants/config_param.dart';
import '../../../core/constants/events.dart';
import '../../../core/constants/firestore_keys.dart';
import '../../../core/constants/shop_constant.dart';
import '../../../core/constants/user_prop.dart';
import '../../../core/helpers/users.dart';
import '../../../core/utils/local_storage.dart';
import '../../../services/remote_config/remote_config_service_contract.dart';
import '../../main/stores/main_store.dart';
import '../../multiplayer/stores/multiplayer_store.dart';

part 'shop_store.g.dart';

class ShopStore = _ShopStore with _$ShopStore;

abstract class _ShopStore with Store {
  final _functions = FirebaseFunctions.instance;
  final _authService = ServiceLocator.locate<AuthService>();
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final _mainStore = ServiceLocator.locate<MainStore>();
  final _configService = ServiceLocator.locate<RemoteConfigServiceContract>();
  final _localStorage = ServiceLocator.locate<LocalStorage>();
  final userMetaRef =
      FirebaseFirestore.instance.collection(FirestoreKeys.userMeta);
  final multiplayerStore = ServiceLocator.locate<MultiplayerStore>();

  @observable
  Status? productStatus;
  @observable
  Status? subscriptionStatus;
  @observable
  Status? purchaseStatus;
  @observable
  ObservableList<Product> products = ObservableList.of([]);
  Map<String, String> productTitles = {
    'premium_month': 'PREMIUM (Monthly)',
    'premium_year': 'PREMIUM (Yearly)',
  };
  @observable
  ObservableList<Product> subscriptions = ObservableList.of([]);
  @observable
  String subscriptionErrorMessage = '';
  @observable
  String productsErrorMessage = '';
  @observable
  String errorMessage = '';
  @observable
  bool isPurchasePending = false;
  @observable
  Status? offerStatus;
  @observable
  bool isSettingPlayPassCompleted = false;
  StreamSubscription? purchaseListStream;
  bool isPurchaseVerificationReq = false;
  String previousTransactionId = "";
  String? previousSubscriptionEmail;
  String? previousLoginProvider;

  @action
  Future<void> loadStore() async {
    // Connect to underlying store first
    if (!(await InAppPurchase.instance.isAvailable())) {
      productsErrorMessage = subscriptionErrorMessage = 'Items unavailable';
      productStatus = subscriptionStatus = Status.error;
      return;
    }

    await Future.wait([
      _loadSubscriptions(),
      _loadProducts(),
    ]);
  }

  @action
  Future<void> _loadSubscriptions() async {
    subscriptionStatus = Status.pending;

    try {
      // Query products
      ProductDetailsResponse response =
          await InAppPurchase.instance.queryProductDetails(subscriptionIds);

      if (response.error == null) {
        final tempProducts = <Product>[];
        for (var productDetail in response.productDetails) {
          final product = Product(
            id: productDetail.id,
            price: productDetail.price,
            type: ProductType.nonConsumable,
            label: productTitles[productDetail.id],
          );

          product.details = productDetail;
          tempProducts.add(product);
        }

        if (response.productDetails.isEmpty &&
            response.notFoundIDs.isNotEmpty &&
            tempProducts.isEmpty) {
          subscriptionStatus = Status.noData;
        } else {
          subscriptions = ObservableList.of(tempProducts);
          subscriptionStatus = Status.done;
        }
      } else {
        subscriptionErrorMessage = 'An unexpected error occured';
        subscriptionStatus = Status.error;
      }
    } catch (error) {
      subscriptionErrorMessage = 'Error loading subscriptions.';
      subscriptionStatus = Status.error;
    }
  }

  Future<void> restorePurchases() async {
    final userId = _authService.user!.uid;
    final hash = sha256.convert(utf8.encode(userId)).toString();
    await InAppPurchase.instance.restorePurchases(applicationUserName: hash);
    debugPrint("restore purchase called userId :  $userId  hash : $hash");
  }

  @action
  Future<void> _loadProducts() async {
    productStatus = Status.pending;
    try {
      // Query products
      final response =
          await InAppPurchase.instance.queryProductDetails(productIds);
      if (response.error == null) {
        int index = 0;
        var regex = RegExp(r'\s\(.*\)');
        final tempProducts = <Product>[];
        response.productDetails
            .sort((a, b) => a.rawPrice.compareTo(b.rawPrice));

        for (var productDetail in response.productDetails) {
          final product = Product(
            id: productDetail.id,
            label: productDetail.title.replaceFirst(regex, ''),
            price: productDetail.price,
            imagePath: productImages[index],
            type: ProductType.consumable,
          );

          product.details = productDetail;
          tempProducts.add(product);
          index++;
        }

        if (response.productDetails.isEmpty &&
            response.notFoundIDs.isNotEmpty &&
            tempProducts.isEmpty) {
          productStatus = Status.noData;
        } else {
          products = ObservableList.of(tempProducts);
          productStatus = Status.done;
        }
      } else {
        productsErrorMessage = 'An unexpected error occured';
        productStatus = Status.error;
      }
    } catch (error) {
      productsErrorMessage = 'Error loading coins.';
      productStatus = Status.error;
    }
  }

  @action
  void purchase(Product product) {
    purchaseStatus = Status.pending;
    final userId = _authService.user!.uid;
    final hash = sha256.convert(utf8.encode(userId)).toString();
    final details = product.details!;
    final purchaseParam = PurchaseParam(
      productDetails: details,
      applicationUserName: hash,
    );

    if (product.type == ProductType.consumable) {
      InAppPurchase.instance.buyConsumable(purchaseParam: purchaseParam);
    } else {
      InAppPurchase.instance.buyNonConsumable(purchaseParam: purchaseParam);
    }
  }

  Future<void> setPlayPassOnServer({required bool isPlayPass}) async {
    final setPlayPass = _functions.httpsCallable('updatePlayPassStatus');
    final stopwatch = Stopwatch(); // Start the stopwatch

    try {
      stopwatch.reset();
      stopwatch.start();
      await setPlayPass({"isPlayPass": isPlayPass});
      log('Time to execute setPlayPass: ${stopwatch.elapsedMilliseconds} ms',
          name: 'ExecutionTime');

      stopwatch.reset();
      debugPrint("success in updating play pass to $isPlayPass");
      log('Time to print success message: ${stopwatch.elapsedMilliseconds} ms',
          name: 'ExecutionTime');
    } catch (error, stackTrace) {
      stopwatch.reset(); // Reset stopwatch before the next measurement
      debugPrint(
          "error in updating play pass to $isPlayPass $error $stackTrace");
      log('Time to print error message: ${stopwatch.elapsedMilliseconds} ms',
          name: 'ExecutionTime');
    }
  }

  Future<void> setPlayPass({required bool isPlayPass}) async {
    debugPrint("set play pass value to : $isPlayPass");
    _mainStore.updatePlayPassValue(isPlayPass);

    if (_mainStore.isPlayPass) {
      double revenue = _configService.getDouble(ConfigParam.playPassRevenueUsd);
      debugPrint("revenue we got is : $revenue");
      _eventLogger.log(Events.playPassRevenueUsd, params: {
        EventParam.currency: "USD",
        EventParam.value: revenue.toString(),
      });
      MethodChannel platform = MethodChannel('com.chinloyal.wordle/channel');
      platform
          .invokeMethod('logPlayPassReveueEventInSingular', {'value': revenue});

      bool isFirstLaunch =
          _localStorage.retrieveBool(LSKey.isFirstLaunchPlayPass) ?? true;
      if (isFirstLaunch) {
        _eventLogger.log(Events.playPassFirstOpen);
      }
    }
    await _localStorage.saveBool(LSKey.isFirstLaunchPlayPass, false);
    setUserProp(
      key: UserProp.isPlayPass,
      value: (isPlayPass).toString(),
    );

    await setPlayPassOnServer(isPlayPass: isPlayPass);

    isSettingPlayPassCompleted = true;
    _eventLogger.log(
      Events.isSettingPlayPassCompleted,
      params: AppStartupParam().param,
    );
  }

  @action
  Future<void> onPurchaseUpdate(List<PurchaseDetails> purchases) async {
    bool isPlayPass = false;

    bool shouldShowTransferIapPopup =
        _localStorage.retrieveBool(LSKey.shouldShowTransferIapPopup) ?? true;

    for (var purchaseDetails in purchases) {
      debugPrint(
        "update purchases called - productID: ${purchaseDetails.productID} status: ${purchaseDetails.status} transactionDate: ${purchaseDetails.transactionDate} verificationData: ${purchaseDetails.verificationData}",
      );

      bool isPremiumUser = _mainStore.isSubscribed || _mainStore.isPlayPass;
      bool isSubscription =
          iapProductMap[purchaseDetails.productID]["type"] == "SUBSCRIPTION";

      if (isSubscription &&
          shouldShowTransferIapPopup &&
          !isPurchaseVerificationReq &&
          !isPremiumUser) {
        VerifyPurchaseModel verifyPurchaseModel =
            await verifyPurchase(purchaseDetails, false);
        bool status = verifyPurchaseModel.data?.isVerified ?? false;
        String? email = verifyPurchaseModel.data?.email;
        String? loginProvider = verifyPurchaseModel.data?.loginProvider;

        if (status) {
          previousTransactionId = purchaseDetails.purchaseID ?? "";
          previousSubscriptionEmail = email;
          previousLoginProvider = loginProvider;
        }
      }

      if (isPurchaseVerificationReq) {
        await verifyPurchase(purchaseDetails, true);
      }

      if (purchaseDetails.productID == playPassProductId) {
        isPlayPass = await isProductStatusValid(purchaseDetails);
      }
    }

    _eventLogger.log(
      Events.updatePurchaseLoopCalled,
      params: AppStartupParam().param,
    );

    if (!isPurchaseVerificationReq) {
      purchaseListStream?.cancel();
      isSettingPlayPassCompleted = false;
      await setPlayPass(isPlayPass: isPlayPass);
    } else {
      isSettingPlayPassCompleted = true;
    }
    isPurchaseVerificationReq = true;
  }

  @action
  Future<VerifyPurchaseModel> verifyPurchase(
      PurchaseDetails purchaseDetails, shouldUpdateDB) async {
    VerifyPurchaseModel verifyPurchaseModel = VerifyPurchaseModel();

    try {
      isPurchasePending = false;
      if (purchaseDetails.status == PurchaseStatus.pending) {
        isPurchasePending = true;
      }

      final verifyPurchaseOnServer = _functions.httpsCallable('verifyPurchase');
      final res = await verifyPurchaseOnServer({
        'source': purchaseDetails.verificationData.source,
        'verificationData':
            purchaseDetails.verificationData.serverVerificationData, //token
        'productId': purchaseDetails.productID,
        'transactionId': purchaseDetails.purchaseID,
        'shouldUpdateDB': shouldUpdateDB,
      });

      debugPrint(
          "Called verifyPurchase for source : ${purchaseDetails.verificationData.source}  verificationData : ${purchaseDetails.verificationData.serverVerificationData} productId : ${purchaseDetails.productID} and got result  : ${res.data}");

      if (res.data != null && res.data is Map<String, dynamic>) {
        verifyPurchaseModel =
            VerifyPurchaseModel.fromJson(res.data as Map<String, dynamic>);
      } else {
        throw FormatException('Response data is not in expected format.');
      }

      bool isValid = verifyPurchaseModel.data?.isVerified ?? false;

      if (isValid && shouldUpdateDB) {
        _eventLogger.logPurchase(
          transactionId: purchaseDetails.purchaseID,
          source: purchaseDetails.verificationData.source,
          productIds: [purchaseDetails.productID],
        );
        purchaseStatus = Status.done;
      } else {
        errorMessage = 'Unable to verify purchase';
        purchaseStatus = Status.error;
      }
      // Mark purchase as complete or user will be refunded
      if (purchaseDetails.pendingCompletePurchase) {
        await InAppPurchase.instance.completePurchase(purchaseDetails);
      }
      return verifyPurchaseModel;
    } catch (error) {
      errorMessage = 'An unexpected error occurred';
      purchaseStatus = Status.error;
      debugPrint(error.toString());
      return verifyPurchaseModel;
    }
  }

  @action
  Future<bool> isProductStatusValid(PurchaseDetails purchaseDetails) async {
    try {
      final verifyPurchaseOnServer =
          _functions.httpsCallable('isProductStatusValid');
      final result = await verifyPurchaseOnServer({
        'source': purchaseDetails.verificationData.source,
        'verificationData':
            purchaseDetails.verificationData.serverVerificationData, //token
        'productId': purchaseDetails.productID,
      });

      debugPrint(
          "Called testVerifyPurchase for source : ${purchaseDetails.verificationData.source}  verificationData : ${purchaseDetails.verificationData.serverVerificationData} productId : ${purchaseDetails.productID} and got result  : ${result.data}");

      final isValid = result.data as bool;

      // Mark purchase as complete or user will be refunded
      if (purchaseDetails.pendingCompletePurchase) {
        await InAppPurchase.instance.completePurchase(purchaseDetails);
      }
      return isValid;
    } catch (error, stackTrace) {
      debugPrint("Error: $error  StackTrace : $stackTrace");
      return false;
    }
  }

  @action
  void claimCoinsForSocialSignIn() {
    offerStatus = Status.pending;

    final claimCoins = _functions.httpsCallable('claimCoinsForSocialSignIn');

    claimCoins().then((value) {
      offerStatus = Status.done;
    }).catchError((error) {
      errorMessage = 'An unexpected error occurred';
      offerStatus = Status.error;
    });
  }
}
