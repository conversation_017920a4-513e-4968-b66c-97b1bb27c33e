import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:in_app_notification/in_app_notification.dart';
import 'package:oktoast/oktoast.dart';
import 'package:responsive_framework/responsive_wrapper.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/router/base_arguments.dart';
import 'package:wordle/src/router/start_up_controller.dart';
import 'package:wordle/src/ui/theme/app_theme.dart';

import 'settings/settings_controller.dart';

/// The Widget that configures your application.
class App extends StatelessWidget {
  App({
    Key? key,
    required this.startUpController,
  }) : super(key: key);

  final settingsController = ServiceLocator.locate<SettingsController>();
  final StartUpController startUpController;

  @override
  Widget build(BuildContext context) {
    settingsController.determineDarkMode(context);
    // The AnimatedBuilder Widget listens to the SettingsController for changes.
    // Whenever the user updates their settings, the MaterialApp is rebuilt.
    return AnimatedBuilder(
      animation: settingsController,
      builder: (BuildContext context, Widget? _) {
        return OKToast(
          child: InAppNotification(
            child: MaterialApp(
              builder: (context, child) => MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaler: TextScaler.linear(
                    1.0,
                  ),
                ),
                child: ResponsiveWrapper.builder(
                  child,
                  maxWidth: 500,
                  minWidth: 320,
                  defaultScale: true,
                  defaultName: MOBILE,
                  breakpoints: const [
                    ResponsiveBreakpoint.resize(320, name: MOBILE),
                    ResponsiveBreakpoint.resize(600, name: TABLET),
                    ResponsiveBreakpoint.resize(1000, name: DESKTOP),
                  ],
                  background: Container(
                    color: AppColors.of(context).colorTone7,
                  ),
                ),
              ),
              debugShowCheckedModeBanner: false,
              navigatorKey: AppRouter.navigatorKey,
              initialRoute: startUpController.getInitialRoute(),
              // Providing a restorationScopeId allows the Navigator built by the
              // MaterialApp to restore the navigation stack when a user leaves and
              // returns to the app after it has been killed while running in the
              // background.
              restorationScopeId: 'app',

              // Provide the generated AppLocalizations to the MaterialApp. This
              // allows descendant Widgets to display the correct translations
              // depending on the user's locale.
              localizationsDelegates: AppLocalizations.localizationsDelegates,
              supportedLocales: const [
                Locale('en', ''), // English, no country code
                Locale('fr', 'CA'), // French, Canadian French
                Locale('es', ''), // Spanish, no country code
              ],

              // Define a light and dark color theme. Then, read the user's
              // preferred ThemeMode (light, dark, or system default) from the
              // SettingsController to display the correct theme.
              theme: appTheme(context),
              darkTheme: darkTheme(context),
              themeMode: settingsController.themeMode,

              // Define a function to handle named routes in order to support
              // Flutter web url navigation and deep linking.
              onGenerateRoute: (RouteSettings routeSettings) {
                return MaterialPageRoute<BaseArguments>(
                  settings: routeSettings,
                  builder: (BuildContext context) {
                    return AppRouter.generateRoute(routeSettings);
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }
}
