import 'dart:developer';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:wordle/src/core/constants/config_param.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';
import 'package:wordle/src/services/remote_config/remote_config_service_contract.dart';
import 'package:wordle/src/ui/views/force_update_view.dart';

class StartUpController {
  final _localStorage = ServiceLocator.locate<LocalStorage>();
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final _configService = ServiceLocator.locate<RemoteConfigServiceContract>();
  final _analytics = FirebaseAnalytics.instance;
  final _authService = ServiceLocator.locate<AuthService>();

  Future<void> initializeApp({
    bool shouldLogAppOpened = true,
  }) async {
    _analytics.setUserId(id: _authService.user?.uid);
    if (shouldLogAppOpened) {
      _eventLogger.log(Events.appOpened);
    }
  }

  String _initRoute() {
    if (_configService.getBool(ConfigParam.forceUpdate)) {
      return ForceUpdateView.routeName;
    }

    return '/';
  }

  String getInitialRoute() {
    String route = _initRoute();

    log("Startup Route: $route");
    return route;
  }

  bool get isFirstLaunch {
    return _localStorage.retrieveBool(LSKey.isFirstLaunch) ?? true;
  }
}
