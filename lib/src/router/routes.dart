import 'package:flutter/material.dart';
import 'package:wordle/src/router/base_arguments.dart';
import 'package:wordle/src/ui/splash_screen.dart';
import 'package:wordle/src/ui/views/daily_challenge/daily_challenge_comments_view.dart';
import 'package:wordle/src/ui/views/daily_challenge/daily_challenge_view.dart';
import 'package:wordle/src/ui/views/entrance/entrance_view.dart';
import 'package:wordle/src/ui/views/force_update_view.dart';
import 'package:wordle/src/ui/views/multiplayer/match_making_view.dart';
import 'package:wordle/src/ui/views/multiplayer/multiplayer_landing_view.dart';
import 'package:wordle/src/ui/views/multiplayer/multiplayer_leaderboard_view.dart';
import 'package:wordle/src/ui/views/multiplayer/multiplayer_match_view.dart';
import 'package:wordle/src/ui/views/settings/privacy_policy_view.dart';
import 'package:wordle/src/ui/views/settings/settings_view.dart';
import 'package:wordle/src/ui/views/settings/terms_and_conditions_view.dart';
import 'package:wordle/src/ui/views/shop/shop_view.dart';
import 'package:wordle/src/ui/views/unlimited_challenge/unlimited_challenge_view.dart';

import '../data/models/difficulty_item_model.dart';

typedef AppRouteBuilder = Widget Function(BaseArguments?);

Map<String, AppRouteBuilder> routes = {
  SplashView.routeName: (_) => SplashView(),
  EntranceView.routeName: (_) => EntranceView(),
  DailyChallengeView.routeName: (args) =>
      DailyChallengeView(args: args as DailyChallengeArgs?),
  DailyChallengeCommentsView.routeName: (args) =>
      DailyChallengeCommentsView(args: args as DailyChallengeCommentsArgs),
  UnlimitedChallengeView.routeName: (args) =>
      UnlimitedChallengeView(args: args as UnlimitedChallengeArgs?),
  SettingsView.routeName: (_) => SettingsView(),
  PrivacyPolicyView.routeName: (_) => PrivacyPolicyView(),
  TermsAndConditionsView.routeName: (_) => TermsAndConditionsView(),
  MatchMakingView.routeName: (_) => MatchMakingView(),
  MultiplayerMatchView.routeName: (_) => MultiplayerMatchView(),
  ShopView.routeName: (_) => ShopView(),
  MultiplayerLandingView.routeName: (_) => MultiplayerLandingView(),
  ForceUpdateView.routeName: (_) => ForceUpdateView(),
  MultiplayerLeaderboardView.routeName: (_) => MultiplayerLeaderboardView(),
};
