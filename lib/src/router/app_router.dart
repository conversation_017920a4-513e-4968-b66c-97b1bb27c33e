import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:wordle/src/router/base_arguments.dart';
import 'package:wordle/src/router/routes.dart';

class AppRouter {
  static final _navigatorKey = GlobalKey<NavigatorState>();

  static GlobalKey<NavigatorState> get navigatorKey => _navigatorKey;

  static String? currentRouteName;

  static Widget generateRoute(RouteSettings routeSettings) {
    BaseArguments? args;
    if (routeSettings.arguments is BaseArguments?) {
      args = routeSettings.arguments as BaseArguments?;
    }

    if (!routes.containsKey(routeSettings.name)) {
      throw '${routeSettings.name} not registered.';
    }

    currentRouteName = routeSettings.name;
    return Sizer(builder: (context, orientation, deviceType) {
      return routes[routeSettings.name]!.call(args);
    });
  }

  static String? pushRestorable<T extends BaseArguments>(String routeName,
      {T? arguments}) {
    return _navigatorKey.currentState
        ?.restorablePushNamed(routeName, arguments: arguments);
  }

  static Future<T?> push<T>(String routeName,
      {BaseArguments? arguments}) async {
    return await _navigatorKey.currentState
        ?.pushNamed<T>(routeName, arguments: arguments);
  }

  static Future<T?> pushReplacement<T extends BaseArguments>(String routeName,
      {T? arguments}) async {
    return await _navigatorKey.currentState
        ?.pushReplacementNamed(routeName, arguments: arguments);
  }

  static void pop([dynamic result]) {
    _navigatorKey.currentState?.pop(result);
  }

  static Future<T?> pushAndRemoveUntil<T extends BaseArguments>(
    String routeName,
    bool Function(Route<dynamic>) predicate, {
    T? arguments,
  }) async {
    return await _navigatorKey.currentState?.pushNamedAndRemoveUntil(
        routeName, (route) => false,
        arguments: arguments);
  }
}
