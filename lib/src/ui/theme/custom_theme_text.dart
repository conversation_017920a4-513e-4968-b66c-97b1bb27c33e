import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/font_family.dart';

class CustomThemeText {
  static TextStyle stymieTextWhite({
    required BuildContext context,
    required double fontSize,
    required FontWeight fontWeight,
    textDecoration = TextDecoration.none,
    double height = 1.1,
  }) {
    return Theme.of(context).textTheme.displayLarge!.copyWith(
          fontSize: fontSize,
          fontWeight: fontWeight,
          fontFamily: FontFamily.stymie,
          height: height,
          decoration: textDecoration,
        );
  }

  static TextStyle stymieTextBlack({
    required BuildContext context,
    required double fontSize,
    required FontWeight fontWeight,
    double height = 1.1,
    textDecoration = TextDecoration.none,
  }) {
    return Theme.of(context).textTheme.labelLarge!.copyWith(
          fontSize: fontSize,
          fontWeight: fontWeight,
          fontFamily: FontFamily.stymie,
          height: height,
          decoration: textDecoration,
        );
  }

  static TextStyle urbanistTextWhite({
    required BuildContext context,
    required double fontSize,
    required FontWeight fontWeight,
    double height = 1.1,
    textDecoration = TextDecoration.none,
  }) {
    return Theme.of(context).textTheme.displayLarge!.copyWith(
          fontSize: fontSize,
          fontWeight: fontWeight,
          fontFamily: FontFamily.urbanist,
          height: height,
          decoration: textDecoration,
        );
  }

  static TextStyle urbanistTextBlack({
    required BuildContext context,
    required double fontSize,
    required FontWeight fontWeight,
    double height = 1.1,
    textDecoration = TextDecoration.none,
  }) {
    return Theme.of(context).textTheme.labelLarge!.copyWith(
          fontSize: fontSize,
          fontWeight: fontWeight,
          fontFamily: FontFamily.urbanist,
          height: height,
          decoration: textDecoration,
        );
  }
}
