import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';

class CustomTheme {
  static Color getWhiteBorderColor(BuildContext context) {
    return isLightMode(context) ? AppColors.white : AppColors.black;
  }

  static Color getBlackBorderColor(BuildContext context) {
    return isLightMode(context) ? AppColors.black : AppColors.white;
  }

  static Color getWhiteIconColor(BuildContext context) {
    return isLightMode(context) ? AppColors.white : AppColors.black;
  }

  static Color getBlackIconColor(BuildContext context) {
    return isLightMode(context) ? AppColors.black : AppColors.white;
  }

  static bool isLightMode(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    final isLightMode = brightness == Brightness.light;
    return isLightMode;
  }
}
