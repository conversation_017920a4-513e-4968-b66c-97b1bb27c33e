import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wordle/src/core/constants/font_family.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/settings/settings_controller.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';

ThemeData appTheme(BuildContext context) {
  final settingsController = ServiceLocator.locate<SettingsController>();
  final primaryColor =
      settingsController.isHighContrast ? Color(0xfff5793a) : Color(0xff6aaa64);
  return ThemeData(
    brightness: Brightness.light,
    colorScheme: ColorScheme.light(primary: primaryColor),
    fontFamily: FontFamily.clearSans,
    textTheme: TextTheme(
      bodyLarge: TextStyle(fontFamily: FontFamily.clearSans),
      bodyMedium: TextStyle(fontFamily: FontFamily.clearSans),
      displayLarge: TextStyle(
        //home page mode button
        color: Colors.white,
      ),
      labelLarge: TextStyle(
        color: Colors.black,
      ),
    ),
    bottomSheetTheme: const BottomSheetThemeData(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10),
          topRight: Radius.circular(10),
        ),
      ),
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: TextStyle(
        fontSize: 24,
        color: Colors.black,
        fontWeight: FontWeight.w900,
        fontFamily: FontFamily.stymie,
      ),
    ),
  );
}

ThemeData darkTheme(BuildContext context) {
  final settingsController = ServiceLocator.locate<SettingsController>();
  final primaryColor =
      settingsController.isHighContrast ? Color(0xfff5793a) : Color(0xff538d4e);

  return ThemeData(
    brightness: Brightness.dark,
    fontFamily: FontFamily.clearSans,
    textTheme: TextTheme(
      bodyLarge: TextStyle(fontFamily: FontFamily.clearSans),
      bodyMedium: TextStyle(fontFamily: FontFamily.clearSans),
      displayLarge: TextStyle(
        color: Colors.black,
      ),
      labelLarge: TextStyle(
        color: Colors.white,
      ),
      bodySmall: TextStyle(),
    ),
    colorScheme: ColorScheme.dark(primary: primaryColor),
    bottomSheetTheme: const BottomSheetThemeData(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10),
          topRight: Radius.circular(10),
        ),
      ),
    ),
    appBarTheme: const AppBarTheme(
      // backgroundColor: Colors.blue,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarBrightness: Brightness.dark,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),

      elevation: 0,
      centerTitle: true,
      titleTextStyle: TextStyle(
        fontSize: 24,
        color: Colors.white,
        fontWeight: FontWeight.w900,
        fontFamily: FontFamily.stymie,
      ),
    ),
  );
}

TextStyle buttonTextStyle({
  required BuildContext context,
  FontWeight fontWeight = FontWeight.w500,
  double fontSize = 16,
}) {
  return CustomThemeText.urbanistTextWhite(
      context: context, fontSize: fontSize, fontWeight: fontWeight);
}
