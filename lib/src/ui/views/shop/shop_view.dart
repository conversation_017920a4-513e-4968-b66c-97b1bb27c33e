import 'dart:async';
import 'dart:math';

import 'package:expandable_page_view/expandable_page_view.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_overlay_loader/flutter_overlay_loader.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/helpers/app_admob.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/strings/shop_strings.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/core/utils/word_list.dart';
import 'package:wordle/src/data/models/product.dart';
import 'package:wordle/src/domain/auth/store/auth_store.dart';
import 'package:wordle/src/domain/main/stores/main_store.dart';
import 'package:wordle/src/domain/shop/store/ads_store.dart';
import 'package:wordle/src/domain/shop/store/shop_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/services/billing/billing_service.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';
import 'package:wordle/src/settings/settings_controller.dart';
import 'package:wordle/src/ui/widgets/bottom_sheets/auth_bottom_sheet.dart';
import 'package:wordle/src/ui/widgets/coins_card.dart';
import 'package:wordle/src/ui/widgets/coins_display.dart';
import 'package:wordle/src/ui/widgets/dialogs/info_dialog.dart';
import 'package:wordle/src/ui/widgets/dialogs/thank_you_coin_dialog.dart';
import 'package:wordle/src/ui/widgets/premium_subscription_card.dart';
import 'package:wordle/src/ui/widgets/shimmer_loaders/coins_loader.dart';
import 'package:wordle/src/ui/widgets/shimmer_loaders/subscription_loader.dart';
import 'package:wordle/src/ui/widgets/status_swatch.dart';
import 'package:wordle/src/ui/widgets/subscribed_card.dart';

class ShopView extends StatefulWidget {
  const ShopView({Key? key}) : super(key: key);
  static const routeName = '/shop';

  @override
  State<ShopView> createState() => _ShopViewState();
}

class _ShopViewState extends State<ShopView> {
  final settingsController = ServiceLocator.locate<SettingsController>();
  final eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final wordList = ServiceLocator.locate<WordList>();
  final shopStore = ServiceLocator.locate<ShopStore>();
  final _authStore = ServiceLocator.locate<AuthStore>();
  final _mainStore = ServiceLocator.locate<MainStore>();
  final _adsStore = ServiceLocator.locate<AdsStore>();
  final _authService = ServiceLocator.locate<AuthService>();
  final _billingService = ServiceLocator.locate<BillingService>();
  final List<ReactionDisposer> _reactionDisposers = [];
  final carouselController = PageController(viewportFraction: 0.8);
  Timer? _carouselTimer;

  @override
  void initState() {
    super.initState();
    eventLogger.log(Events.shopVisited);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_authService.isAuthenticated()) {
        shopStore.loadStore();
        ScaffoldMessenger.of(context).showMaterialBanner(
          MaterialBanner(
            leading: Icon(
              Icons.login_rounded,
              color: Color(0xff2196f3),
            ),
            backgroundColor: Color(0xff2196f3).withOpacity(0.2),
            content: Text(
              'Sign in to make a purchase',
              style: TextStyle(
                fontSize: 15,
                color: Color(0xff2196f3),
              ),
            ),
            actions: [
              TextButton(
                child: Text(
                  'LOGIN',
                  style: TextStyle(
                    color: Color(0xff2196f3),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                onPressed: () {
                  if (!_authService.isAuthenticated()) {
                    showModalBottomSheet(
                      context: context,
                      builder: (_) => AuthBottomSheet(
                        onAuthenticate: () {
                          String name =
                              _authService.user?.getDisplayName() ?? '';

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Signed in as: $name'),
                            ),
                          );
                          ScaffoldMessenger.of(context)
                              .removeCurrentMaterialBanner();
                        },
                      ),
                    );
                  }
                },
              )
            ],
          ),
        );
      }
    });

    if (!kIsWeb) {
      _billingService.purchaseStream.listen(
        shopStore.onPurchaseUpdate,
        onDone: () {
          _billingService.dispose();
        },
      );
    }

    _reactionDisposers.add(
      reaction<Status?>((_) => shopStore.purchaseStatus, (status) {
        _handlePending(status);
        _handleError(status);
        _handleDone(status);
      }),
    );
    _reactionDisposers.add(
      reaction<Status?>((_) => _adsStore.status, (status) {
        _handlePending(status);
        _handleAdError(status);
      }),
    );

    // Carousel autoplay
    _carouselTimer = Timer.periodic(const Duration(seconds: 10), (_) {
      if (carouselController.page == 0) {
        carouselController.animateToPage(
          1,
          duration: const Duration(milliseconds: 800),
          curve: Curves.fastOutSlowIn,
        );
      } else {
        carouselController.animateToPage(
          0,
          duration: const Duration(milliseconds: 800),
          curve: Curves.fastOutSlowIn,
        );
      }
    });
  }

  @override
  void dispose() {
    super.dispose();

    for (var reactionDisposer in _reactionDisposers) {
      reactionDisposer();
    }

    if (_carouselTimer != null) {
      _carouselTimer!.cancel();
    }
  }

  @override
  Widget build(BuildContext context) {
    _authStore.loadAuth();
    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () {
        ScaffoldMessenger.of(context).removeCurrentMaterialBanner();
        AppRouter.pop();
        return Future.value(true);
      },
      child: Observer(builder: (context) {
        return Scaffold(
          backgroundColor: AppColors.of(context).colorTone7,
          appBar: AppBar(
            title: Text(
              'SHOP',
              style: TextStyle(
                color: AppColors.of(context).colorTone1,
              ),
            ),
            iconTheme: Theme.of(context).iconTheme,
            actions: [
              if (_authStore.isAuthenticated)
                Padding(
                  padding: EdgeInsets.only(right: 5),
                  child: CoinsDisplay(),
                ),
            ],
          ),
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 10),
                    if (!kIsWeb) ...[
                      Text(
                        'Subscriptions',
                        style: TextStyle(
                          color: AppColors.of(context).colorTone1,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      SizedBox(height: 5),
                      if (!_mainStore.isSubscribed)
                        StatusSwatch(
                          status: shopStore.subscriptionStatus,
                          pendingBuilder: (_) => SubscriptionLoader(),
                          doneBuilder: (_) => Column(
                            children: [
                              ExpandablePageView.builder(
                                controller: carouselController,
                                itemCount: shopStore.subscriptions.length,
                                itemBuilder: (context, index) {
                                  if (!carouselController
                                      .position.haveDimensions) {
                                    return SubscriptionLoader();
                                  }
                                  final item = shopStore.subscriptions[index];
                                  return AnimatedBuilder(
                                    animation: carouselController,
                                    builder: (_, __) => Transform.scale(
                                      scale: max(
                                        0.8,
                                        (1 -
                                            (carouselController.page! - index)
                                                    .abs() /
                                                2),
                                      ),
                                      child: PremiumSubscriptionCard(
                                        title: item.label ?? 'PREMIUM',
                                        price: "${item.price}/" +
                                            subscriptionExtra[item.id]![
                                                'period'],
                                        onPurchase: () {
                                          _onPurchase(item);
                                        },
                                        bulletPoints: subscriptionExtra[
                                            item.id]!['benefits'],
                                      ),
                                    ),
                                  );
                                },
                              ),
                              SizedBox(height: 5),
                              Text(
                                '*If you select the monthly subscription option, your subscription will automatically renew every month until you cancel. If you select the yearly subscription option, your subscription will automatically renew every year until you cancel. You can cancel your subscription at anytime in the Google Play Store. Subscription is not required to play. ',
                                style: TextStyle(fontSize: 8.5),
                              )
                            ],
                          ),
                          errorBuilder: (_) =>
                              Text(shopStore.subscriptionErrorMessage),
                          noDataBuilder: (_) =>
                              Text('No subscriptions to display.'),
                        )
                      else
                        SubscribedCard(),
                      SizedBox(height: 20),
                      Text(
                        'Coins',
                        style: TextStyle(
                          color: AppColors.of(context).colorTone1,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      SizedBox(height: 5),
                      StatusSwatch(
                        status: shopStore.productStatus,
                        pendingBuilder: (_) => CoinsLoader(),
                        doneBuilder: (_) => GridView.count(
                          physics: NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          childAspectRatio: 1 / 1.4,
                          crossAxisCount: 3,
                          crossAxisSpacing: 10,
                          mainAxisSpacing: 10,
                          children: [
                            CoinsCard(
                              isFree: true,
                              label: "10 Coins",
                              price: "",
                              imagePath: AppImages.coinSingle,
                              onPurchase: () {
                                _adsStore.watchRewardedAd(
                                    AdRewardUnitId.free10Coins);
                              },
                            ),
                            ...shopStore.products
                                .map(
                                  (product) => CoinsCard(
                                    label: product.label ?? '',
                                    price: product.price,
                                    imagePath: product.imagePath ?? '',
                                    onPurchase: () {
                                      _onPurchase(product);
                                    },
                                  ),
                                )
                                .toList()
                          ],
                        ),
                        errorBuilder: (_) =>
                            Text(shopStore.productsErrorMessage),
                        noDataBuilder: (_) => Text('No coins to display'),
                      ),
                      SizedBox(height: 20),
                    ],
                    Text(
                      'Offers',
                      style: TextStyle(
                        color: AppColors.of(context).colorTone1,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    SizedBox(height: 5),
                    if (_authStore.isUserAnonymous &&
                        _authStore.isAuthenticated)
                      GridView.count(
                        physics: NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        childAspectRatio: 1 / 1.4,
                        crossAxisCount: 3,
                        crossAxisSpacing: 10,
                        mainAxisSpacing: 10,
                        children: [
                          CoinsCard(
                            label: '50 coins',
                            price: 'Sign In',
                            imagePath: AppImages.coin200,
                            onPurchase: () {
                              showModalBottomSheet(
                                context: context,
                                builder: (_) => AuthBottomSheet(
                                  linkGuest: true,
                                  onAuthenticate: () {
                                    _authStore.loadAuth();
                                    String name =
                                        _authService.user?.getDisplayName() ??
                                            '';
                                    shopStore.claimCoinsForSocialSignIn();

                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text('Signed in as: $name'),
                                      ),
                                    );
                                  },
                                ),
                              );
                            },
                          )
                        ],
                      )
                    else
                      Text('No offers to display.')
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  void _onPurchase(Product product) {
    if (!_authService.isAuthenticated()) {
      showModalBottomSheet(
        context: context,
        builder: (_) => AuthBottomSheet(
          onAuthenticate: () {
            String name = _authService.user?.getDisplayName() ?? '';

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Signed in as: $name',
                ),
              ),
            );
            ScaffoldMessenger.of(context).removeCurrentMaterialBanner();
          },
        ),
      );
    } else {
      shopStore.purchase(product);
    }
  }

  void _handlePending(Status? status) {
    if (status == Status.pending) {
      Loader.show(
        context,
        progressIndicator: const CircularProgressIndicator.adaptive(),
      );
    } else {
      Loader.hide();
    }
  }

  void _handleError(Status? status) {
    if (status == Status.error) {
      showDialog(
        context: context,
        builder: (_) => InfoDialog(
          title: 'Error',
          body: shopStore.errorMessage,
        ),
      );
    }
  }

  void _handleAdError(Status? status) {
    if (status == Status.error) {
      showDialog(
        context: context,
        builder: (_) => InfoDialog(
          title: 'Error',
          body: _adsStore.errorMessage,
        ),
      );
    }
  }

  void _handleDone(Status? status) {
    if (status == Status.done && !shopStore.isPurchasePending) {
      showDialog(
        context: context,
        builder: (_) => ThankYouCoinDialog(),
      );
    }

    if (status == Status.done && shopStore.isPurchasePending) {
      showDialog(
        context: context,
        builder: (_) => InfoDialog(
          title: 'Purchase Pending',
          body: "You will receive your coins shortly.",
        ),
      );
    }
  }
}
