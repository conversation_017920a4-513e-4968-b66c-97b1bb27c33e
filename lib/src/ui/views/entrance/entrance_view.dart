import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wordle/main.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/core/constants/config_param.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/constants/font_family.dart';
import 'package:wordle/src/core/utils/env.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/models/difficulty_config.dart';
import 'package:wordle/src/domain/auth/store/auth_store.dart';
import 'package:wordle/src/domain/main/stores/main_store.dart';
import 'package:wordle/src/domain/multiplayer/stores/multiplayer_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';
import 'package:wordle/src/services/fcm/notification_logic_service.dart';
import 'package:wordle/src/services/remote_config/remote_config_service_contract.dart';
import 'package:wordle/src/ui/theme/custom_theme.dart';
import 'package:wordle/src/ui/views/daily_challenge/daily_challenge_view.dart';
import 'package:wordle/src/ui/views/unlimited_challenge/unlimited_challenge_view.dart';
import 'package:wordle/src/ui/widgets/dialogs/restore_subscription_popup.dart';
import 'package:wordle/src/ui/widgets/dialogs/terms_dialog.dart';
import 'package:wordle/src/ui/widgets/entrance_banner_ad.dart';
import 'package:wordle/src/ui/widgets/multiplayer_stats_dialog.dart';
import 'package:wordle/src/ui/widgets/show_loading.dart';
import 'package:wordle/src/ui/widgets/speed_intro_dialog.dart';
import 'package:wordle/src/ui/widgets/switch/custom_switch.dart';

import '../../../core/constants/firestore_keys.dart';
import '../../../core/utils/local_storage.dart';
import '../../../core/utils/word_list.dart';
import '../../../data/models/difficulty_item_model.dart';
import '../../../domain/daily_challenge/stores/daily_challenge_store.dart';
import '../../../domain/shop/store/shop_store.dart';
import '../../../domain/unlimited_challenge/stores/unlimited_challenge_store.dart';
import '../../../services/auth/auth_service.dart';
import '../../../services/stats_service.dart';
import '../../theme/custom_theme_text.dart';
import '../../widgets/bottom_sheets/auth_bottom_sheet.dart';
import '../../widgets/daily_stats_dialog.dart';
import '../../widgets/dialogs/info_dialog.dart';
import '../../widgets/dialogs/speed_mode_result_dialog.dart';
import '../../widgets/drawer/custom_drawer.dart';
import '../../widgets/reusable_game_mode_container.dart';
import '../../widgets/reusable_unlimited_letter_container.dart';
import '../../widgets/unlimited_stats_dialog.dart';
import '../multiplayer/match_making_view.dart';
import '../multiplayer/multiplayer_leaderboard_view.dart';

class EntranceView extends StatefulWidget {
  const EntranceView({Key? key}) : super(key: key);
  static const routeName = '/entranceView';

  @override
  State<EntranceView> createState() => _EntranceViewState();
}

class _EntranceViewState extends State<EntranceView> {
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final _configService = ServiceLocator.locate<RemoteConfigServiceContract>();
  final mainStore = ServiceLocator.locate<MainStore>();
  final multiplayerStore = ServiceLocator.locate<MultiplayerStore>();
  final dailyChallengeStore = ServiceLocator.locate<DailyChallengeStore>();
  final _authService = ServiceLocator.locate<AuthService>();
  final _authStore = ServiceLocator.locate<AuthStore>();
  final wordList = ServiceLocator.locate<WordList>();
  final statsService = ServiceLocator.locate<StatsService>();
  final _localStorage = ServiceLocator.locate<LocalStorage>();
  final _shopStore = ServiceLocator.locate<ShopStore>();

  final unlimitedChallengeStore =
      ServiceLocator.locate<UnlimitedChallengeStore>();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final userMetaRef =
      FirebaseFirestore.instance.collection(FirestoreKeys.userMeta);
  final _mainStore = ServiceLocator.locate<MainStore>();
  final _functions = FirebaseFunctions.instance;

  int retryCount = 0;

  late DifficultyConfig difficultyConfig5;
  late DifficultyConfig difficultyConfig6;
  late DifficultyConfig difficultyConfig7;

  @override
  void initState() {
    super.initState();

    _eventLogger.log(Events.entranceVisited);
    difficultyConfig5 = DifficultyConfig(lettersCount: 5, rowCount: 6);
    difficultyConfig6 = DifficultyConfig(lettersCount: 6, rowCount: 6);
    difficultyConfig7 = DifficultyConfig(lettersCount: 7, rowCount: 7);

    _eventLogger.log(
      Events.entranceVisited,
      params: AppStartupParam().param,
    );

    stopwatch.stop();
    stopwatch.reset();

    if (!mainStore.isTermsAccepted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog(
          barrierDismissible: false,
          context: context,
          builder: (_) => TermsDialog(),
        );
      });
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      loadAll();
      sendStatsToServer();
    });
  }

  void showUnlimitedStats(BuildContext context) {
    showDialog(
      context: context,
      builder: (_) => mainStore.isSpeedMode
          ? SpeedModeResultDialog(
              heading: "STATS",
              letterCount: 5,
              context: context,
              isGameCompleted: false,
              submittedRows: unlimitedChallengeStore.submittedRows,
              gameResult: unlimitedChallengeStore.gameResult,
              gameId: unlimitedChallengeStore.gameId ?? "",
              isUnlimitedGame: true,
              onPlay: _goToNewWord,
              showOpenShareAutomatically: false,
            )
          : UnlimitedStatsDialog(
              onPlay: _goToNewWord,
              heading: 'STATS',
              letterCount: 5,
              context: context,
            ),
    );
  }

  showDailyStats() {
    showDialog(
        context: context,
        builder: (_) => mainStore.isSpeedMode
            ? SpeedModeResultDialog(
                heading: "STATS",
                letterCount: 5,
                context: context,
                isGameCompleted: false,
                submittedRows: dailyChallengeStore.submittedRows,
                gameResult: dailyChallengeStore.gameResult,
                gameId: dailyChallengeStore.gameId ?? "",
                isUnlimitedGame: false,
                showOpenShareAutomatically: false,
              )
            : DailyStatsDialog(
                heading: 'STATS',
                parentContext: context,
                letterCount: 5,
              ));
  }

  void _goToNewWord() {
    AppRouter.pop();
    unlimitedChallengeStore.reset(hardReset: true);
    unlimitedChallengeStore.initialise(
      difficultyConfig: unlimitedChallengeStore.difficultyConfig,
      context: context,
    );
    setState(() {});
  }

  Future<void> loadAuth() async {
    if (retryCount++ < 15) {
      if (_authStore.user?.displayName == null &&
          _authService.isAuthenticated()) {
        await _authStore.loadAuth().then((value) async {
          await Future.delayed(Duration(milliseconds: 250), () async {
            await loadAuth();
          });
        });
      } else {
        loadCoins();
      }
    }
  }

  Future<void> loadCoins() async {
    final userMeta = userMetaRef.doc(_authService.user!.uid).snapshots();
    userMeta.listen((snapshot) {
      _mainStore.coins = snapshot.data()?['coins'] ?? 0;
    });
  }

  Future<void> loadAllWords() async {
    if (wordList.wordsDaily.isEmpty ||
        wordList.wordsUnlimited.isEmpty ||
        wordList.wordList6.isEmpty ||
        wordList.wordList7.isEmpty ||
        wordList.dictionary.isEmpty ||
        wordList.dictionary6.isEmpty ||
        wordList.dictionary7.isEmpty) {
      await wordList.loadWords();
    }
  }

  Future<void> loadDailyMode() async {
    await dailyChallengeStore.initialise(
        difficultyConfig: difficultyConfig5, shouldSyncDailyWord: false);
    dailyChallengeStore.checkAndResetGameIfNeeded();
    await dailyChallengeStore.initialise(
        difficultyConfig: difficultyConfig6, shouldSyncDailyWord: false);
    dailyChallengeStore.checkAndResetGameIfNeeded();
    await dailyChallengeStore.initialise(
        difficultyConfig: difficultyConfig7, shouldSyncDailyWord: false);
    dailyChallengeStore.checkAndResetGameIfNeeded();
  }

  Future<void> loadAll() async {
    showLoading(context);
    await Future.wait([
      loadAllWords(),
      loadAuth(),
      loadDailyMode(),
    ]);
    hideLoading(context).then((value) async {
      NotificationLogicService.showNotificationPopupIfNeeded(context);
      showTransferIapIfNeeded();
    });
  }

  void showTransferIapIfNeeded() {
    bool shouldShowTransferIapPopup =
        _localStorage.retrieveBool(LSKey.shouldShowTransferIapPopup) ?? true;
    bool hasPreviousSubscription = _shopStore.previousTransactionId != "";
    String? email = _shopStore.previousSubscriptionEmail;
    String? loginProvider = _shopStore.previousLoginProvider;
    bool isGuest = _authStore.isUserAnonymous;
    bool isPremium = mainStore.isSubscribed || mainStore.isPlayPass;

    if (isGuest &&
        shouldShowTransferIapPopup &&
        hasPreviousSubscription &&
        !isPremium) {
      RestoreSubscriptionPopup.show(
        context: context,
        email: email ?? "",
        loginProvider: loginProvider ?? "",
      );
    }
  }

  Future<Map<String, dynamic>> getLocalStats(
      {LSKey? key, bool isSevenLetter = false}) async {
    Map<String, dynamic> result = {};

    if (key != null) {
      statsService.loadLocalStats(key);
      result = {
        "gamesPlayed": statsService.stats.gamesPlayed,
        "gamesWon": statsService.stats.gamesWon,
        "maxStreak": statsService.stats.maxStreak,
        "currentStreak": statsService.stats.currentStreak,
        "guessDistribution": statsService.stats.guessDistribution.map(
          (key, value) => MapEntry(key.toString(), value),
        ),
        "winPercentage": double.parse(
            (statsService.stats.winPercentage * 100).toStringAsFixed(2)),
      };
    } else {
      result = {
        "gamesPlayed": 0,
        "gamesWon": 0,
        "maxStreak": 0,
        "currentStreak": 0.0,
        "guessDistribution": isSevenLetter
            ? {
                "1": 0,
                "2": 0,
                "3": 0,
                "4": 0,
                "5": 0,
                "6": 0,
                "7": 0,
              }
            : {
                "1": 0,
                "2": 0,
                "3": 0,
                "4": 0,
                "5": 0,
                "6": 0,
              },
        "winPercentage": 0.00
      };
    }
    return result;
  }

  Future<void> sendStatsToServer() async {
    bool isPreviousSinglePlayerStatsRequired =
        _configService.getBool(ConfigParam.isPreviousSinglePlayerStatsRequired);

    if (isPreviousSinglePlayerStatsRequired) {
      Map<String, dynamic> dailyFiveLetterStats =
          await getLocalStats(key: LSKey.stats);
      Map<String, dynamic> dailySixLetterStats = await getLocalStats();
      Map<String, dynamic> dailySevenLetterStats =
          await getLocalStats(isSevenLetter: true);

      Map<String, dynamic> unlimitedFiveLetterStats =
          await getLocalStats(key: LSKey.unlimitedStats);
      Map<String, dynamic> unlimitedSixLetterStats =
          await getLocalStats(key: LSKey.unlimitedStats6);
      Map<String, dynamic> unlimitedSevenLetterStats =
          await getLocalStats(key: LSKey.unlimitedStats7);

      var body = {
        "daily": {
          "5Letter": dailyFiveLetterStats,
          "6Letter": dailySixLetterStats,
          "7Letter": dailySevenLetterStats
        },
        "unlimited": {
          "5Letter": unlimitedFiveLetterStats,
          "6Letter": unlimitedSixLetterStats,
          "7Letter": unlimitedSevenLetterStats,
        },
      };

      final postPreviousSingleGameStats =
          _functions.httpsCallable('postPreviousSingleGameStats');
      postPreviousSingleGameStats(body).then((response) {
        log('postPreviousSingleGameStats called successfully $body');
      }).catchError((error) {
        log('Error Response from postPreviousSingleGameStats $body');
        log(error.toString());
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: AppColors.of(context).colorTone7,
      endDrawer: CustomDrawer(),
      appBar: AppBar(
        automaticallyImplyLeading: false,
        title: Text(
          env(EnvKey.PLATFORM_APP_NAME).toCapitalized(),
          style: TextStyle(
            fontSize: 40,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              _scaffoldKey.currentState?.openEndDrawer();
            },
            icon: Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: CustomTheme.isLightMode(context)
                      ? Colors.black
                      : Colors.white,
                  shape: BoxShape.circle,
                ),
                child: SvgPicture.asset(
                  AppImages.drawerIcon,
                  height: 12,
                  color: CustomTheme.getWhiteIconColor(context),
                )),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Center(
          child: Observer(builder: (context) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Spacer(),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          !mainStore.isSpeedMode
                              ? AppImages.watchIcon
                              : AppImages.watchSpeedIcon,
                          height: 24,
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        Text(
                          "Speed Mode".toUpperCase(),
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        SizedBox(
                          width: 38,
                          height: 38,
                          child: IconButton(
                            onPressed: () {
                              showDialog(
                                  context: context,
                                  builder: (_) => SpeedIntroDialog(
                                        onClick: () async {
                                          if (!mainStore.isSpeedMode) {
                                            showLoading(context);
                                            dailyChallengeStore.reset();
                                            mainStore.updateGameMode(true);
                                            await loadDailyMode();
                                            hideLoading(context);
                                          }
                                          AppRouter.pop();
                                          AppRouter.push(
                                            UnlimitedChallengeView.routeName,
                                            arguments: UnlimitedChallengeArgs(
                                              letter: 5,
                                            ),
                                          );
                                        },
                                      ));
                            },
                            icon: Container(
                              padding: EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: AppColors.gray8E,
                                ),
                              ),
                              child: Icon(
                                Icons.question_mark,
                                size: 16,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 12,
                        ),
                        CustomSwitch(
                          value: mainStore.isSpeedMode,
                          onChanged: (value) async {
                            showLoading(context);
                            dailyChallengeStore.reset();
                            mainStore.updateGameMode(value);
                            await loadDailyMode();
                            hideLoading(context);
                          },
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: Text(
                        "Solve the puzzle as fast as possible to earn maximum points!",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          fontFamily: FontFamily.urbanist,
                          color: CustomTheme.isLightMode(context)
                              ? AppColors.darkGrey
                              : AppColors.blueGrey,
                        ),
                      ),
                    ),
                  ],
                ),
                Spacer(
                  flex: 2,
                ),
                if (_configService.getBool(ConfigParam.enableDailyMode))
                  ReusableGameModeContainer(
                    title: 'Daily',
                    description:
                        "Solve a new puzzle every day. Challenge yourself.",
                    icon: AppImages.dailyModeIcon,
                    color: AppColors.orange,
                    isDaily: true,
                    onPress: () {
                      AppRouter.push(
                        DailyChallengeView.routeName,
                        arguments: DailyChallengeArgs(letter: 5),
                      );
                    },
                    extraItems: [
                      IconButton(
                          onPressed: () {
                            //_eventLogger.log(Events.statsClicked);
                            showDailyStats();
                          },
                          icon: SvgPicture.asset(
                            AppImages.statCircularIcon,
                            color: CustomTheme.getWhiteIconColor(context),
                          )),
                    ],
                  ),
                SizedBox(height: 8),
                if (_configService.getBool(ConfigParam.enableMultiplayerMode) &&
                    !mainStore.isSpeedMode)
                  ReusableGameModeContainer(
                    title: 'Multiplayer',
                    description: "Compete against players worldwide.",
                    icon: AppImages.multiPlayerModeIcon,
                    color: AppColors.darkYellow,
                    onPress: () {
                      if (!_authService.isAuthenticated()) {
                        showModalBottomSheet(
                          context: context,
                          builder: (_) => AuthBottomSheet(
                            onAuthenticate: () {
                              String name =
                                  _authService.user?.getDisplayName() ?? '';
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Signed in as: $name'),
                                ),
                              );
                              if (mainStore.isAllowedToPlayMultiplayer) {
                                AppRouter.push(MatchMakingView.routeName);
                              } else {
                                showDialog(
                                  context: context,
                                  builder: (_) => InfoDialog(
                                    title: 'Error',
                                    body:
                                        'Not enough coins to join a match. You need at least 10 coins to join a match.',
                                  ),
                                );
                              }
                            },
                          ),
                        );
                      } else {
                        if (mainStore.isAllowedToPlayMultiplayer) {
                          AppRouter.push(MatchMakingView.routeName);
                        } else {
                          showDialog(
                            context: context,
                            builder: (_) => InfoDialog(
                              title: 'Error',
                              body:
                                  'Not enough coins to join a match. You need at least 10 coins to join a match.',
                            ),
                          );
                        }
                      }
                    },
                    extraItems: [
                      IconButton(
                          onPressed: () {
                            if (!_authService.isAuthenticated()) {
                              showModalBottomSheet(
                                context: context,
                                builder: (_) => AuthBottomSheet(
                                  onAuthenticate: () {
                                    String name =
                                        _authService.user?.getDisplayName() ??
                                            '';
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text('Signed in as: $name'),
                                      ),
                                    );
                                    AppRouter.push(
                                        MultiplayerLeaderboardView.routeName);
                                  },
                                ),
                              );
                            } else {
                              AppRouter.push(
                                  MultiplayerLeaderboardView.routeName);
                            }
                          },
                          icon: SvgPicture.asset(
                            AppImages.trophyCircularIcon,
                            color: CustomTheme.getWhiteIconColor(context),
                          )),
                      IconButton(
                          onPressed: () async {
                            showLoading(context);
                            multiplayerStore.loadStats().then((value) {
                              hideLoading(context);
                              showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (_) => MultiplayerStatsDialog(),
                              );
                            });
                          },
                          icon: SvgPicture.asset(
                            AppImages.statCircularIcon,
                            color: CustomTheme.getWhiteIconColor(context),
                          )),
                    ],
                  ),
                SizedBox(height: 8),
                if (_configService.getBool(ConfigParam.enableUnlimitedMode))
                  ReusableGameModeContainer(
                    title: 'Unlimited',
                    description: "Endless word-hunting fun!",
                    icon: AppImages.unlimitedModeIcon,
                    color: AppColors.green,
                    onPress: () {
                      AppRouter.push(UnlimitedChallengeView.routeName);
                    },
                    extraItems: [
                      IconButton(
                          onPressed: () {
                            showUnlimitedStats(context);
                          },
                          icon: SvgPicture.asset(
                            AppImages.statCircularIcon,
                            color: CustomTheme.getWhiteIconColor(context),
                          )),
                    ],
                  ),
                SizedBox(
                  height: 32,
                ),
                Row(
                  children: [
                    Expanded(
                        child: ReusableUnlimitedLetterContainer(
                      numberOfLetter: 6,
                      onPress: () {
                        AppRouter.push(UnlimitedChallengeView.routeName,
                            arguments: UnlimitedChallengeArgs(letter: 6));
                      },
                    )),
                    SizedBox(
                      width: 8,
                    ),
                    Expanded(
                        child: ReusableUnlimitedLetterContainer(
                      numberOfLetter: 7,
                      onPress: () {
                        AppRouter.push(UnlimitedChallengeView.routeName,
                            arguments: UnlimitedChallengeArgs(letter: 7));
                      },
                    ))
                  ],
                ),
                Spacer(
                  flex: 10,
                ),
              ],
            );
          }),
        ),
      ),
      bottomNavigationBar: EntranceBannerAd(),
    );
  }
}
