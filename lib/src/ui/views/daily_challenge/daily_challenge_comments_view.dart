import 'dart:async';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/constants/firestore_keys.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/models/daily_comment.dart';
import 'package:wordle/src/domain/daily_challenge/stores/daily_comment_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/router/base_arguments.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';
import 'package:wordle/src/ui/views/daily_challenge/write_daily_comment_view.dart';
import 'package:wordle/src/ui/widgets/ads/daily_comment_native_ad.dart';
import 'package:wordle/src/ui/widgets/buttons/hiding_fab.dart';
import 'package:wordle/src/ui/widgets/comment_widget.dart';
import 'package:wordle/src/ui/widgets/dialogs/info_dialog.dart';
import 'package:wordle/src/ui/widgets/shimmer_loaders/comment_loader.dart';

class DailyChallengeCommentsView extends StatefulWidget {
  final DailyChallengeCommentsArgs args;
  const DailyChallengeCommentsView({Key? key, required this.args})
      : super(key: key);
  static const routeName = '/daily/comments';

  @override
  State<DailyChallengeCommentsView> createState() =>
      _DailyChallengeCommentsViewState();
}

class _DailyChallengeCommentsViewState
    extends State<DailyChallengeCommentsView> {
  late final DailyCommentStore _dailyCommentStore;
  final List<ReactionDisposer> reactionDisposers = [];
  final GlobalKey<AnimatedListState> _animatedListKey = GlobalKey();
  final ScrollController scrollController = ScrollController();
  final userMetaRef =
      FirebaseFirestore.instance.collection(FirestoreKeys.userMeta);
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? userMetaStreamSub;
  final _authService = ServiceLocator.locate<AuthService>();
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  String? comment;

  void _onSubmissionError(Status? status) {
    if (status == Status.error) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('There was an error posting your comment.'),
        action: SnackBarAction(
          label: 'RETRY',
          onPressed: () {
            _goToWriteComment();
          },
        ),
      ));
    }
  }

  void _onSubmissionDone(Status? status) {
    if (status == Status.done) {
      comment = null;
    }
  }

  Future<void> _goToWriteComment() async {
    comment = await Navigator.of(context).push<String>(MaterialPageRoute(
      builder: (context) => WriteDailyCommentView(prefilledComment: comment),
      fullscreenDialog: true,
    ));

    if (comment != null && comment!.isNotEmpty) {
      _dailyCommentStore.submitComment(
        puzzleNumber: widget.args.puzzleNumber,
        comment: comment!,
      );
    }
  }

  void _removeComment(int index, DailyComment comment) {
    _animatedListKey.currentState!.removeItem(
      index,
      (context, animation) => SlideTransition(
        position: Tween<Offset>(
          begin: Offset(-1.0, 0.0),
          end: Offset(0.0, 0.0),
        ).animate(animation),
        child: SizeTransition(
          sizeFactor: animation,
          child: CommentWidget(comment: comment),
        ),
      ),
    );

    _dailyCommentStore.dailyComments.removeAt(index);

    if (_dailyCommentStore.dailyComments.isEmpty) {
      _dailyCommentStore.commentsStatus = Status.noData;
    }
  }

  void openInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => InfoDialog(
        title: 'About Discussions',
        body: '''
Discussions is currently in beta testing at the moment.

All comments are moderated. Leaving any comment that is rude, mean, contains profanity or insults will be removed and the user subsequently banned.

Please discuss nicely. If there are comments not caught by our moderation you can flag a comment to report and hide it from your feed.
''',
      ),
    );
  }

  void _onUserBanned(bool isBanned) {
    if (isBanned) {
      ScaffoldMessenger.of(context).showMaterialBanner(
        MaterialBanner(
          forceActionsBelow: true,
          padding: EdgeInsets.all(20),
          content: Text(
              'You have been banned from leaving comments for violating our Terms of Service. If you think this is a mistake, contact our support email listed on the store.'),
          leading: Icon(Icons.warning),
          backgroundColor: Colors.redAccent,
          actions: <Widget>[
            TextButton(
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentMaterialBanner();
              },
              child: Text(
                'DISMISS',
                style: TextStyle(color: Colors.red.shade900),
              ),
            ),
          ],
        ),
      );
    } else {
      ScaffoldMessenger.of(context).hideCurrentMaterialBanner();
    }
  }

  @override
  void initState() {
    super.initState();
    _eventLogger.log(Events.dailyDiscussionsVisited);
    _dailyCommentStore = DailyCommentStore();

    reactionDisposers.add(
      reaction<Status?>((_) => _dailyCommentStore.submissionStatus,
          (submissionStatus) {
        _onSubmissionError(submissionStatus);
        _onSubmissionDone(submissionStatus);
      }),
    );

    reactionDisposers.add(
      reaction<bool>((_) => _dailyCommentStore.isBannedComments, (isBanned) {
        _onUserBanned(isBanned);
      }),
    );

    _dailyCommentStore.getComments(widget.args.puzzleNumber);

    final userMeta = userMetaRef.doc(_authService.user!.uid).snapshots();

    userMetaStreamSub = userMeta.listen((snapshot) {
      _dailyCommentStore.isBannedComments =
          snapshot.data()?['isBannedComments'] ?? false;
    });
  }

  @override
  void dispose() {
    for (var reactionDisposer in reactionDisposers) {
      reactionDisposer();
    }

    userMetaStreamSub?.cancel();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () {
        ScaffoldMessenger.of(context).removeCurrentMaterialBanner();
        AppRouter.pop();
        return Future.value(true);
      },
      child: Scaffold(
        backgroundColor: AppColors.of(context).colorTone7,
        appBar: AppBar(
          iconTheme: IconThemeData(color: AppColors.of(context).colorTone2),
          title: AutoSizeText(
            'Discussions (BETA)',
            style: TextStyle(
              color: AppColors.of(context).colorTone1,
            ),
            maxLines: 1,
            minFontSize: 10,
          ),
          actions: [
            IconButton(
                onPressed: openInfoDialog, icon: Icon(Icons.info_outline))
          ],
        ),
        body: Observer(builder: (context) {
          // The +1 is to accommodate the "Load more" button
          final listLength = _dailyCommentStore.dailyComments.length + 1;
          final items = _dailyCommentStore.dailyComments;
          if (_dailyCommentStore.commentsStatus == Status.pending) {
            return ListView.builder(
              itemCount: 20,
              itemBuilder: (context, index) => CommentLoader(),
            );
          } else if (_dailyCommentStore.commentsStatus == Status.done) {
            return RefreshIndicator(
              onRefresh: () {
                _dailyCommentStore.loadMoreStatus = null;
                return _dailyCommentStore.getComments(widget.args.puzzleNumber);
              },
              child: AnimatedList(
                key: _animatedListKey,
                controller: scrollController,
                initialItemCount: listLength,
                itemBuilder: (context, index, animation) {
                  if (index < items.length) {
                    return Observer(builder: (context) {
                      final comment = items[index];
                      final enableTile = !(index == 0 &&
                          _dailyCommentStore.submissionStatus ==
                              Status.pending);
                      // Return an ad if the id of the comment is set to ad
                      if (comment.id == 'ad') {
                        return DailyCommentNativeAd();
                      }

                      // Comment widget with transition
                      return SlideTransition(
                        position: Tween<Offset>(
                          begin: Offset(-1.0, 0.0),
                          end: Offset(0.0, 0.0),
                        ).animate(animation),
                        child: SizeTransition(
                          sizeFactor: animation,
                          child: CommentWidget(
                            enabled: enableTile,
                            comment: comment,
                            onFlag: () {
                              _dailyCommentStore.flagComment(comment.id);
                              _removeComment(index, comment);
                            },
                          ),
                        ),
                      );
                    });
                  } else {
                    return Observer(builder: (context) {
                      if (_dailyCommentStore.loadMoreStatus == Status.pending) {
                        return CircularProgressIndicator.adaptive();
                      } else if ((_dailyCommentStore.loadMoreStatus ==
                                  Status.done ||
                              _dailyCommentStore.loadMoreStatus == null) &&
                          _dailyCommentStore.retrievedCount >=
                              _dailyCommentStore.pageCount) {
                        return TextButton(
                          onPressed: () {
                            String? lastCommentId;

                            if (_dailyCommentStore.dailyComments.last.id ==
                                'ad') {
                              lastCommentId = _dailyCommentStore
                                  .dailyComments[
                                      _dailyCommentStore.dailyComments.length -
                                          2]
                                  .id;
                            } else {
                              lastCommentId =
                                  _dailyCommentStore.dailyComments.last.id;
                            }
                            _dailyCommentStore.loadMoreComments(
                              widget.args.puzzleNumber,
                              lastCommentId,
                            );
                          },
                          child: Text('Load more'),
                        );
                      } else {
                        return SizedBox.shrink();
                      }
                    });
                  }
                },
              ),
            );
          } else if (_dailyCommentStore.commentsStatus == Status.noData) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'Be the first to leave a comment!',
                    style: TextStyle(fontSize: 16),
                    maxLines: 3,
                  ),
                  SizedBox(height: 10),
                  Icon(
                    Icons.chat,
                    size: 50,
                    color: Colors.grey,
                  ),
                ],
              ),
            );
          } else {
            return Center(
              child: Text(
                'There was an error loading comments. Please try again later.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16, color: Colors.red),
              ),
            );
          }
        }),
        floatingActionButton: Observer(builder: (context) {
          final isBanned = _dailyCommentStore.isBannedComments;
          return HidingFAB(
            controller: scrollController,
            backgroundColor: AppColors.of(context).present,
            child: Icon(Icons.add),
            onPressed: !isBanned
                ? () {
                    _eventLogger.log(Events.addDailyCommentClicked);
                    _goToWriteComment();
                  }
                : null,
          );
        }),
      ),
    );
  }
}

class DailyChallengeCommentsArgs extends BaseArguments {
  final int puzzleNumber;

  DailyChallengeCommentsArgs(this.puzzleNumber);
}
