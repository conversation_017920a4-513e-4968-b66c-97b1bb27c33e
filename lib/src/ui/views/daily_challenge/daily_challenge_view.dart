import 'dart:developer';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:in_app_notification/in_app_notification.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/config_param.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/constants/game_result.dart';
import 'package:wordle/src/core/constants/result_toast_message.dart';
import 'package:wordle/src/core/helpers/toast.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/core/utils/word_list.dart';
import 'package:wordle/src/data/models/difficulty_item_model.dart';
import 'package:wordle/src/domain/daily_challenge/stores/daily_challenge_store.dart';
import 'package:wordle/src/domain/main/stores/main_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';
import 'package:wordle/src/services/remote_config/remote_config_service_contract.dart';
import 'package:wordle/src/services/stats_service.dart';
import 'package:wordle/src/ui/theme/custom_theme.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';
import 'package:wordle/src/ui/views/daily_challenge/daily_challenge_comments_view.dart';
import 'package:wordle/src/ui/widgets/bottom_sheets/auth_bottom_sheet.dart';
import 'package:wordle/src/ui/widgets/daily_stats_dialog.dart';
import 'package:wordle/src/ui/widgets/dialogs/confirm_dialog.dart';
import 'package:wordle/src/ui/widgets/dialogs/loading_dialog.dart';
import 'package:wordle/src/ui/widgets/game_board.dart';
import 'package:wordle/src/ui/widgets/game_keyboard.dart';
import 'package:wordle/src/ui/widgets/game_row_container.dart';
import 'package:wordle/src/ui/widgets/how_to_modal.dart';
import 'package:wordle/src/ui/widgets/main/main_scaffold.dart';
import 'package:wordle/src/ui/widgets/solution_text.dart';

import '../../../core/constants/app_images.dart';
import '../../../core/constants/map_const.dart';
import '../../../core/strings/app_string.dart';
import '../../../data/models/difficulty_config.dart';
import '../../theme/app_theme.dart';
import '../../widgets/buttons/booster_button.dart';
import '../../widgets/dialogs/speed_mode_result_dialog.dart';
import '../../widgets/reusable_timer_bar.dart';
import '../unlimited_challenge/unlimited_challenge_view.dart';

class DailyChallengeView extends StatefulWidget {
  final DailyChallengeArgs? args;

  const DailyChallengeView({Key? key, required this.args}) : super(key: key);
  static const routeName = '/daily';

  @override
  State<DailyChallengeView> createState() => _DailyChallengeViewState();
}

class _DailyChallengeViewState extends State<DailyChallengeView>
    with TickerProviderStateMixin {
  final dailyChallengeStore = ServiceLocator.locate<DailyChallengeStore>();
  final mainStore = ServiceLocator.locate<MainStore>();
  final wordList = ServiceLocator.locate<WordList>();
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final _localStorage = ServiceLocator.locate<LocalStorage>();
  final statsService = ServiceLocator.locate<StatsService>();
  final _authService = ServiceLocator.locate<AuthService>();
  final _remoteConfigService =
      ServiceLocator.locate<RemoteConfigServiceContract>();

  final List<ReactionDisposer> reactionDisposers = [];
  final inAppReview = InAppReview.instance;
  late final int puzzleNumber;

  late DifficultyConfig selectedDifficultyConfig;
  bool _isMounted = true;

  late final AnimationController _discussBtnController = AnimationController(
    vsync: this,
    duration: Duration(milliseconds: 250),
    value: 1,
  );

  late final Animation<double> _animation = CurvedAnimation(
    parent: _discussBtnController,
    curve: Curves.easeOutBack,
  );

  @override
  void initState() {
    super.initState();

    if (mainStore.isFirstLaunch) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showHowToModal(context);
      });
    }

    _eventLogger.log(Events.dailyChallengeVisited);
    reactionDisposers.add(
      reaction<GameResult?>((_) => dailyChallengeStore.gameResult,
          (gameResult) {
        _onWon(context, gameResult);
      }),
    );

    reactionDisposers.add(
      reaction<bool>(
          (_) =>
              (dailyChallengeStore.hasGameEnded && !mainStore.isSpeedMode) ||
              (dailyChallengeStore.hasGameEnded &&
                  mainStore.isSpeedMode &&
                  dailyChallengeStore.ongoingGameStatus == Status.done),
          (hasGameEnded) {
        if (hasGameEnded) {
          _gameEnded(hasGameEnded, context);
        }
      }),
    );

    reactionDisposers.add(
      reaction<Status?>((_) => dailyChallengeStore.syncStatus, (status) {
        _onSyncStatus(context, status);
      }),
    );

    reactionDisposers.add(reaction<String?>(
      (_) => dailyChallengeStore.helpingHandWord,
      _onHelpingHand,
    ));

    selectedDifficultyConfig =
        difficultyItemsMap[widget.args?.letter ?? 5]!.difficultyConfig;

    dailyChallengeStore.setTimerController(
      TimerController(
        duration: Duration(
          seconds:
              dailyChallengeStore.speedModeDuration(selectedDifficultyConfig),
        ),
      ),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      initDailyMode();
    });
    puzzleNumber = wordList.puzzleNumber;
  }

  @override
  void dispose() {
    _isMounted = false;
    for (var reactionDisposer in reactionDisposers) {
      reactionDisposer();
    }
    super.dispose();
  }

  Future<void> initDailyMode() async {
    dailyChallengeStore.reset();
    await dailyChallengeStore.initialise(
        difficultyConfig: selectedDifficultyConfig, shouldSyncDailyWord: true);

    await dailyChallengeStore.getOngoingSpeedGame(context: context);

    if (dailyChallengeStore.hasGameEnded) {
      _discussBtnController.reverse();
    }
  }

  void _onWon(BuildContext context, GameResult? gameResult) {
    if (gameResult == GameResult.won) {
      Future.delayed(Duration(milliseconds: dailyChallengeStore.timeToWait))
          .then((value) {
        showGameToast(
          context: context,
          msg: resultToastMessage[dailyChallengeStore.currentRowNumber - 2],
          isSuccess: true,
        );
      });

      if (_localStorage.retrieveBool(LSKey.reviewRequested) != true) {
        statsService.loadStats(
          parentContext: context,
          gameType: selectedDifficultyConfig.dailyStatsType.gameType,
          gameName: selectedDifficultyConfig.dailyStatsType.gameName,
        );
        if (statsService.stats.gamesPlayed > 1) {
          inAppReview.isAvailable().then((isAvailable) {
            log('in_app_review available: $isAvailable');
            if (isAvailable) {
              _localStorage.saveBool(LSKey.reviewRequested, true);
              inAppReview.requestReview();
            }
          });
        }
      }
    }
  }

  void _gameEnded(bool hasGameEnded, BuildContext newContext) {
    if (hasGameEnded) {
      if (dailyChallengeStore.timerController?.remainingTime.inSeconds == 0 &&
          mainStore.isSpeedMode &&
          dailyChallengeStore.gameResult == GameResult.lost) {
        showGameToast(context: context, msg: "Time’s up!", isSuccess: false);
      }
      Future.delayed(Duration(milliseconds: dailyChallengeStore.timeToWait))
          .then((value) {
        if (_isMounted) {
          _discussBtnController.reverse();
        }
      });

      Future.delayed(
              Duration(milliseconds: dailyChallengeStore.timeToWait + 1500))
          .then((value) {
        if (_isMounted) {
          String heading = "Congratulations";
          if (dailyChallengeStore.gameResult != GameResult.won) {
            heading = "Thanks for playing!";
          }

          showStats(
            newContext: newContext,
            heading: heading,
            shouldShareAutomatically: true,
            isFromResult: dailyChallengeStore.isGameEndedBeforePlayed != true,
          );
        }
      });
    }
  }

  void showStats({
    required BuildContext newContext,
    required String heading,
    required bool shouldShareAutomatically,
    required bool isFromResult,
  }) {
    if (!_isMounted || !Navigator.of(newContext).mounted) {
      return;
    }

    if (dailyChallengeStore.isStatsVisibleToUser) {
      Navigator.of(newContext).pop();
    }

    dailyChallengeStore.isStatsVisibleToUser = true;
    showDialog(
      context: newContext,
      builder: (_) => mainStore.isSpeedMode
          ? SpeedModeResultDialog(
              heading: heading,
              context: context,
              letterCount: selectedDifficultyConfig.lettersCount,
              isGameCompleted: dailyChallengeStore.hasGameEnded,
              submittedRows: dailyChallengeStore.submittedRows,
              gameResult: dailyChallengeStore.gameResult,
              gameId: dailyChallengeStore.gameId ?? "",
              isUnlimitedGame: false,
              showOpenShareAutomatically: isFromResult,
            )
          : DailyStatsDialog(
              heading: heading,
              parentContext: newContext,
              letterCount: selectedDifficultyConfig.lettersCount,
              isGameCompleted: dailyChallengeStore.hasGameEnded,
            ),
    ).then((value) {
      if (_isMounted) {
        dailyChallengeStore.isStatsVisibleToUser = false;
      }
    });
  }

  void _onHelpingHand(String? helpingHandWord) {
    if (helpingHandWord != null) {
      InAppNotification.show(
        duration: Duration(seconds: 5),
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.of(context).octoberPurple,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            children: [
              Text(AppStrings.helpingHandText),
              Text(
                helpingHandWord.toUpperCase(),
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
              )
            ],
          ),
        ),
        context: context,
      );
    }
  }

  void showHowToModal(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => HowToModal(),
    ).then(
      (value) => dailyChallengeStore.timerController?.resume(),
    );
  }

  void _onSyncStatus(BuildContext context, Status? status) {
    if (status == Status.pending) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => LoadingDialog(),
      );
    } else if (status == Status.done) {
      AppRouter.pop();
    } else if (status == Status.error) {
      _eventLogger.log(Events.dailyModeSyncFailed);
      AppRouter.pop();
      showDialog(
        context: context,
        builder: (_) => ConfirmDialog(
          title: 'Retry Sync',
          body:
              'Error syncing daily word, make sure you have an internet connection, your word will be out of sync with other players. Would you like to retry?',
          confirmText: 'Retry',
          onConfirm: () {
            AppRouter.pop();
            dailyChallengeStore.syncWordOfTheDay();
          },
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return MainScaffold(
      title: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Visibility(
            visible: mainStore.isSpeedMode,
            child: Padding(
              padding: const EdgeInsets.only(right: 6),
              child: SvgPicture.asset(
                AppImages.watchSpeedIcon,
                height: 26,
              ),
            ),
          ),
          AutoSizeText(
            "Daily",
            style: CustomThemeText.stymieTextBlack(
                context: context, fontSize: 26, fontWeight: FontWeight.w900),
            maxLines: 1,
            minFontSize: 10,
          ),
          SizedBox(
            width: 6,
          ),
          AutoSizeText(
            "#$puzzleNumber",
            style: CustomThemeText.urbanistTextBlack(
                context: context, fontSize: 18, fontWeight: FontWeight.w500),
            maxLines: 1,
            minFontSize: 8,
          ),
        ],
      ),
      onStatsPressed: () {
        _eventLogger.log(Events.statsClicked);
        showStats(
          newContext: context,
          heading: 'STATS',
          shouldShareAutomatically: false,
          isFromResult: false,
        );
      },
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          child: Observer(builder: (context) {
            return Column(
              children: [
                Observer(builder: (context) {
                  return GameBoard(
                    difficultyConfig: dailyChallengeStore.difficultyConfig,
                    submittedRows: dailyChallengeStore.submittedRows,
                    timerController: dailyChallengeStore.timerController,
                    showTimer: !dailyChallengeStore.hasGameEnded,
                    onTimerEnd: () {
                      dailyChallengeStore.endGameDueToTimeout();
                    },
                    currentRowBuilder: () => Observer(
                      builder: (context) {
                        return GameRowContainer(
                          hints: dailyChallengeStore.hints,
                          row: dailyChallengeStore.currentGameRow,
                          maxNodes:
                              dailyChallengeStore.difficultyConfig.lettersCount,
                          rowCount:
                              dailyChallengeStore.difficultyConfig.rowCount,
                        );
                      },
                    ),
                  );
                }),
                Visibility(
                  visible: dailyChallengeStore.hasGameEnded,
                  child: Padding(
                    padding: EdgeInsets.only(top: 16),
                    child: ElevatedButton(
                      onPressed: () {
                        AppRouter.pop();
                        AppRouter.push(
                          UnlimitedChallengeView.routeName,
                          arguments: UnlimitedChallengeArgs(
                            letter: selectedDifficultyConfig.lettersCount,
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.of(context).saleGreen,
                        elevation: 0,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'NEW WORDE',
                            style: buttonTextStyle(context: context),
                          ),
                          SizedBox(
                            width: 6,
                          ),
                          Icon(
                            Icons.arrow_forward_ios_outlined,
                            size: 15,
                            color: CustomTheme.getWhiteIconColor(context),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Spacer(),
                if (dailyChallengeStore.gameResult == GameResult.lost)
                  FutureBuilder(
                      future: Future.delayed(Duration(
                          milliseconds: dailyChallengeStore.timeToWait)),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState == ConnectionState.done) {
                          return SolutionText(
                            text: dailyChallengeStore.wordOfTheSession
                                    ?.toUpperCase() ??
                                "",
                          );
                        } else {
                          return SizedBox.shrink();
                        }
                      }),
                Spacer(),
                Observer(builder: (context) {
                  return Visibility(
                    visible: !dailyChallengeStore.hasGameEnded &&
                        selectedDifficultyConfig.lettersCount != 5,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 7),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          BoosterButton(
                            isAdFree:
                                dailyChallengeStore.hintManager?.isHintAdFree(
                                      isHelpingHand: false,
                                    ) ==
                                    true,
                            count: dailyChallengeStore.hintManager
                                    ?.getCount(isHelpingHand: false) ??
                                0,
                            iconPath: AppImages.hintIconNew,
                            onPressed: dailyChallengeStore.hintManager
                                        ?.isHintAvailable(
                                      isHelpingHand: false,
                                    ) ==
                                    false
                                ? null
                                : () {
                                    dailyChallengeStore
                                        .onHintClicked(
                                            selectedDifficultyConfig:
                                                selectedDifficultyConfig,
                                            context: context)
                                        .then(
                                          (value) => setState(() {}),
                                        );
                                  },
                          ),
                          SizedBox(
                            width: 14,
                          ),
                          BoosterButton(
                            isAdFree:
                                dailyChallengeStore.hintManager?.isHintAdFree(
                                      isHelpingHand: true,
                                    ) ==
                                    true,
                            count: dailyChallengeStore.hintManager
                                    ?.getCount(isHelpingHand: true) ??
                                0,
                            iconPath: AppImages.helpIconNew,
                            onPressed: dailyChallengeStore.hintManager
                                        ?.isHintAvailable(
                                      isHelpingHand: true,
                                    ) ==
                                    false
                                ? null
                                : () {
                                    dailyChallengeStore
                                        .onHelpingHandClick(
                                            selectedDifficultyConfig:
                                                selectedDifficultyConfig,
                                            context: context)
                                        .then((value) => setState(() {}));
                                  },
                          ),
                        ],
                      ),
                    ),
                  );
                }),
                SizedBox(
                  height: 8,
                ),
                Observer(builder: (context) {
                  return GameKeyBoard(
                      oldLettersUsed: dailyChallengeStore.oldLettersUsed,
                      lettersUsed: dailyChallengeStore.lettersUsed,
                      delayInMilliseconds: dailyChallengeStore.timeToWait,
                      isEnterButtonEnabled:
                          dailyChallengeStore.gamePostingStatus !=
                              Status.pending,
                      onEnter: () async {
                        if (!dailyChallengeStore.hasGameEnded) {
                          await dailyChallengeStore.submitGuess();
                          if (dailyChallengeStore.status == Status.error) {
                            showGameToast(
                                context: context,
                                msg: dailyChallengeStore.errorMessage,
                                isSuccess: false);
                          }
                          setState(() {});
                        }
                      },
                      onDelete: () {
                        if (!dailyChallengeStore.hasGameEnded) {
                          dailyChallengeStore.removeLastLetter();
                        }
                      },
                      onKeyPressed: (key) {
                        if (!dailyChallengeStore.hasGameEnded) {
                          dailyChallengeStore.addLetter(key);
                        }
                      });
                }),
              ],
            );
          }),
        ),
      ),
      floatingActionButton: Builder(builder: (context) {
        return Observer(
          builder: (BuildContext context) {
            if (_remoteConfigService
                    .getBool(ConfigParam.enableDailyModeComments) &&
                dailyChallengeStore.hasGameEnded) {
              return AnimatedBuilder(
                  animation: _discussBtnController,
                  builder: (_, __) {
                    return AnimatedContainer(
                      duration: Duration(milliseconds: 250),
                      curve: Curves.easeOutBack,
                      transform: Matrix4.translationValues(
                          0.0, _animation.value * 100, 0.0),
                      child: FloatingActionButton(
                        backgroundColor: AppColors.of(context).correct,
                        child: Icon(
                          Icons.chat,
                          color: Colors.white,
                        ),
                        onPressed: () {
                          _eventLogger.log(Events.dailyDiscussionsClicked);
                          if (_authService.isAuthenticated()) {
                            _goToComments();
                          } else {
                            showModalBottomSheet(
                              context: context,
                              builder: (_) => AuthBottomSheet(
                                onAuthenticate: () {
                                  String name =
                                      _authService.user?.getDisplayName() ?? '';

                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('Signed in as: $name'),
                                    ),
                                  );
                                  _goToComments();
                                },
                              ),
                            );
                          }
                        },
                      ),
                    );
                  });
            } else {
              return SizedBox.shrink();
            }
          },
        );
      }),
    );
  }

  void _goToComments() {
    AppRouter.push(
      DailyChallengeCommentsView.routeName,
      arguments: DailyChallengeCommentsArgs(puzzleNumber),
    );
  }
}
