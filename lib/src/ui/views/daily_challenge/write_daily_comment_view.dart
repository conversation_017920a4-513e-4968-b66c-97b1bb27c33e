import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';

class WriteDailyCommentView extends StatefulWidget {
  final String? prefilledComment;
  const WriteDailyCommentView({
    Key? key,
    this.prefilledComment,
  }) : super(key: key);

  @override
  State<WriteDailyCommentView> createState() => _WriteDailyCommentViewState();
}

class _WriteDailyCommentViewState extends State<WriteDailyCommentView> {
  final _textfieldController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _textfieldController.text = widget.prefilledComment ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // title: Text('Write a comment'),
        iconTheme: IconThemeData(color: AppColors.of(context).colorTone2),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(_textfieldController.text);
            },
            child: Text(
              'POST',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          )
        ],
      ),
      body: Container(
        padding: EdgeInsets.symmetric(horizontal: 8),
        child: TextField(
          autofocus: true,
          controller: _textfieldController,
          decoration: InputDecoration(
            border: InputBorder.none,
            hintText: 'Write a comment...',
          ),
          minLines: 6,
          keyboardType: TextInputType.multiline,
          maxLines: null,
          maxLength: 400,
          buildCounter: (context,
              {required int currentLength,
              required bool isFocused,
              maxLength}) {
            return Text(
              '$currentLength/$maxLength',
              semanticsLabel: 'character count',
            );
          },
        ),
      ),
    );
  }
}
