import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';

class TermsAndConditionsView extends StatelessWidget {
  TermsAndConditionsView({Key? key}) : super(key: key);
  static const routeName = '/settings/toc';
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();

  @override
  Widget build(BuildContext context) {
    _eventLogger.log(Events.tocVisited);
    return Scaffold(
      backgroundColor: AppColors.of(context).colorTone7,
      appBar: AppBar(
        title: Text(
          'TERMS & CONDITIONS',
          style: TextStyle(
            color: AppColors.of(context).colorTone1,
          ),
        ),
        iconTheme: Theme.of(context).iconTheme,
      ),
      body: WebViewWidget(
        controller: WebViewController()
          ..loadRequest(Uri.parse('https://www.takigames.io/terms-of-service')),
      ),
    );
  }
}
