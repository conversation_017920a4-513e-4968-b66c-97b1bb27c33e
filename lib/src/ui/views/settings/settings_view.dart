import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_overlay_loader/flutter_overlay_loader.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/constants/keyboard_layout.dart';
import 'package:wordle/src/core/helpers/toast.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/domain/auth/store/auth_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';
import 'package:wordle/src/settings/settings_controller.dart';
import 'package:wordle/src/ui/theme/app_theme.dart';
import 'package:wordle/src/ui/views/settings/privacy_policy_view.dart';
import 'package:wordle/src/ui/views/settings/terms_and_conditions_view.dart';
import 'package:wordle/src/ui/widgets/bottom_sheets/auth_bottom_sheet.dart';
import 'package:wordle/src/ui/widgets/dialogs/confirm_dialog.dart';

import '../../../domain/multiplayer/stores/multiplayer_store.dart';

class SettingsView extends StatefulWidget {
  const SettingsView({
    Key? key,
  }) : super(key: key);
  static const routeName = '/settings';

  @override
  State<SettingsView> createState() => _SettingsViewState();
}

class _SettingsViewState extends State<SettingsView> {
  final settingsController = ServiceLocator.locate<SettingsController>();
  final eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final multiplayerStore = ServiceLocator.locate<MultiplayerStore>();
  final _authStore = ServiceLocator.locate<AuthStore>();
  final List<ReactionDisposer> _reactionDisposers = [];
  final _localStorage = ServiceLocator.locate<LocalStorage>();

  @override
  void initState() {
    super.initState();
    eventLogger.log(Events.settingsVisited);
    _reactionDisposers.add(
      reaction<Status?>((_) => _authStore.loginStatus, (status) {
        _handlePending(context, status);
        _handleError(context, status);
        _handleDone(context, status);
      }),
    );
  }

  @override
  void dispose() {
    super.dispose();

    for (var reactionDisposer in _reactionDisposers) {
      reactionDisposer();
    }
  }

  void triggerLogin() {
    showModalBottomSheet(
      context: context,
      builder: (_) => AuthBottomSheet(
        onAuthenticate: () {
          _authStore.loadAuth();
          String name = _authStore.user?.displayName ?? '';

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Signed in as: $name'),
            ),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.of(context).colorTone7,
      appBar: AppBar(
        title: Text(
          'SETTINGS',
          style: TextStyle(
            color: AppColors.of(context).colorTone1,
          ),
        ),
        iconTheme: Theme.of(context).iconTheme,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
          child: LayoutBuilder(builder: (context, constraints) {
            return SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minWidth: constraints.maxWidth,
                  minHeight: constraints.maxHeight,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      ListTile(
                        title: const Text('Dark Mode'),
                        trailing: Switch.adaptive(
                          value: settingsController.isDarkMode,
                          onChanged: (_) {
                            settingsController.updateThemeMode();
                          },
                        ),
                      ),
                      ListTile(
                        title: const Text('High Contrast Mode'),
                        subtitle: Text('For the color blind'),
                        trailing: Switch.adaptive(
                          value: settingsController.isHighContrast,
                          onChanged: (_) {
                            settingsController.toggleHighContrast();
                          },
                        ),
                      ),
                      ListTile(
                        title: const Text('Privacy Policy'),
                        onTap: () {
                          AppRouter.push(PrivacyPolicyView.routeName);
                        },
                        trailing: const Icon(Icons.arrow_forward_ios),
                      ),
                      ListTile(
                        title: const Text('Terms & Conditions'),
                        onTap: () {
                          AppRouter.push(TermsAndConditionsView.routeName);
                        },
                        trailing: const Icon(Icons.arrow_forward_ios),
                      ),
                      if (_authStore.isAuthenticated)
                        ListTile(
                          title: const Text('ID'),
                          subtitle: Text(_authStore.user!.uid),
                          onTap: () {
                            Clipboard.setData(
                              ClipboardData(text: _authStore.user!.uid),
                            );
                            toast(
                                context: context,
                                msg: 'ID copied to clipboard.');
                          },
                          trailing: const Icon(Icons.copy),
                        ),
                      Divider(
                        color: AppColors.of(context).colorTone2,
                      ),
                      Text(
                        'Preferences',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.of(context).colorTone2,
                        ),
                      ),
                      ListTile(
                        title: const Text('"Enter" key on left'),
                        trailing: Switch.adaptive(
                          value: settingsController.isEnterOnLeft,
                          onChanged: (_) {
                            settingsController.toggleEnterOnLeft();
                          },
                        ),
                      ),
                      ListTile(
                        title: Text('Keyboard Layout: ' +
                            settingsController.keyboardLayout.getName()),
                        trailing: PopupMenuButton<KeyboardLayout>(
                          initialValue: settingsController.keyboardLayout,
                          onSelected: (layout) {
                            settingsController.updateKeyboardLayout(layout);
                          },
                          itemBuilder: (context) => [
                            PopupMenuItem(
                              child: Text(KeyboardLayout.qwerty.getName()),
                              value: KeyboardLayout.qwerty,
                            ),
                            PopupMenuItem(
                              child: Text(KeyboardLayout.qwertz.getName()),
                              value: KeyboardLayout.qwertz,
                            ),
                            PopupMenuItem(
                              child: Text(KeyboardLayout.azerty.getName()),
                              value: KeyboardLayout.azerty,
                            ),
                          ],
                        ),
                      ),
                      ListTile(
                        title: const Text('Full Screen Mode'),
                        trailing: Switch.adaptive(
                          value: settingsController.isFullScreen,
                          onChanged: (_) {
                            settingsController.toggleFullScreen();
                          },
                        ),
                      ),
                      SizedBox(height: 10),
                      Observer(builder: (context) {
                        return Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            if (_authStore.isAuthenticated)
                              if (!_authStore.isUserAnonymous)
                                Expanded(
                                  child: ElevatedButton(
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          AppColors.of(context).badRed,
                                    ),
                                    child: Text(
                                      'SIGN OUT',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                    onPressed: () {
                                      _authStore.signOut();
                                    },
                                  ),
                                )
                              else
                                Expanded(
                                  child: SizedBox(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Center(
                                          child: Text('Signed in as guest'),
                                        ),
                                        ElevatedButton(
                                          child: Text(
                                            "Already have an account"
                                                .toUpperCase(),
                                            style: buttonTextStyle(
                                                context: context),
                                          ),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                AppColors.of(context).saleGreen,
                                          ),
                                          onPressed: () {
                                            showDialog(
                                              context: context,
                                              builder: (context) =>
                                                  ConfirmDialog(
                                                title:
                                                    "Login to existing account",
                                                body:
                                                    "You will be logged out of your guest account and you will no longer have access to it. Are you sure you want to continue?",
                                                cancelText: 'No',
                                                confirmText: 'Yes',
                                                onConfirm: () async {
                                                  if (_authStore
                                                      .isUserAnonymous) {
                                                    await _localStorage.save(
                                                        LSKey.oldUserId,
                                                        _authStore.user?.uid ??
                                                            "");
                                                  }
                                                  _authStore.signOut();
                                                  _authStore.loadAuth();
                                                  AppRouter.pop();
                                                  triggerLogin();
                                                },
                                              ),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                            else
                              Expanded(
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        AppColors.of(context).saleGreen,
                                  ),
                                  child: Text(
                                    'LOGIN',
                                    style: buttonTextStyle(context: context),
                                  ),
                                  onPressed: triggerLogin,
                                ),
                              )
                          ],
                        );
                      }),
                      Expanded(child: Container()),
                      // Row(
                      //   children: [
                      //     const Text('Copyright 2022. All Rights Reserved.')
                      //   ],
                      // )
                    ],
                  ),
                ),
              ),
            );
          }),
        ),
      ),
    );
  }

  void _handlePending(BuildContext context, Status? status) {
    if (status == Status.pending) {
      Loader.show(
        context,
        progressIndicator: const CircularProgressIndicator.adaptive(),
      );
    } else {
      Loader.hide();
    }
  }

  void _handleError(BuildContext context, Status? status) {
    if (status == Status.error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(_authStore.errorMessage)),
      );
    }
  }

  void _handleDone(BuildContext context, Status? status) {
    if (status == Status.done) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Signed Out.')),
      );
    }
  }
}
