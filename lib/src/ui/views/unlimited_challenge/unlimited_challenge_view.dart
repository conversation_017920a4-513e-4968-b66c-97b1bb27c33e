import 'package:auto_size_text/auto_size_text.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_overlay_loader/flutter_overlay_loader.dart';
import 'package:flutter_svg/svg.dart';
import 'package:in_app_notification/in_app_notification.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/constants/game_result.dart';
import 'package:wordle/src/core/constants/result_toast_message.dart';
import 'package:wordle/src/core/enums/app_enums.dart';
import 'package:wordle/src/core/helpers/toast.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/strings/app_string.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/core/utils/word_list.dart';
import 'package:wordle/src/data/models/difficulty_config.dart';
import 'package:wordle/src/domain/main/stores/main_store.dart';
import 'package:wordle/src/domain/unlimited_challenge/stores/unlimited_challenge_store.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';
import 'package:wordle/src/ui/widgets/buttons/booster_button.dart';
import 'package:wordle/src/ui/widgets/game_board.dart';
import 'package:wordle/src/ui/widgets/game_keyboard.dart';
import 'package:wordle/src/ui/widgets/game_row_container.dart';
import 'package:wordle/src/ui/widgets/how_to_modal.dart';
import 'package:wordle/src/ui/widgets/main/main_scaffold.dart';
import 'package:wordle/src/ui/widgets/solution_text.dart';
import 'package:wordle/src/ui/widgets/unlimited_stats_dialog.dart';

import '../../../core/constants/map_const.dart';
import '../../../data/models/difficulty_item_model.dart';
import '../../theme/app_theme.dart';
import '../../theme/custom_theme.dart';
import '../../theme/custom_theme_text.dart';
import '../../widgets/dialogs/speed_mode_result_dialog.dart';
import '../../widgets/keep_playing_dialog.dart';
import '../../widgets/reusable_timer_bar.dart';
import '../../widgets/show_loading.dart';

class UnlimitedChallengeView extends StatefulWidget {
  final UnlimitedChallengeArgs? args;

  const UnlimitedChallengeView({Key? key, required this.args})
      : super(key: key);

  static const routeName = '/unlimited';

  @override
  State<UnlimitedChallengeView> createState() => _UnlimitedChallengeViewState();
}

class _UnlimitedChallengeViewState extends State<UnlimitedChallengeView> {
  final unlimitedChallengeStore =
      ServiceLocator.locate<UnlimitedChallengeStore>();
  final mainStore = ServiceLocator.locate<MainStore>();
  final wordList = ServiceLocator.locate<WordList>();
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final _localStorage = ServiceLocator.locate<LocalStorage>();
  final List<ReactionDisposer> reactionDisposers = [];
  final inAppReview = InAppReview.instance;
  final GlobalKey<GameBoardState> _gameBoardKey = GlobalKey<GameBoardState>();
  bool isShowingBadge = true;
  String? selectedDifficultyName;
  late DifficultyConfig selectedDifficultyConfig;

  @override
  void initState() {
    super.initState();
    _eventLogger.log(Events.unlimitedChallengeVisited);
    isShowingBadge = _localStorage
            .retrieveBool(LSKey.newFeatureUnlimitedDifficultyOnUnlimited) ==
        null;

    if (mainStore.isFirstLaunch) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog(
          context: context,
          builder: (context) => HowToModal(),
        ).then(
          (value) => unlimitedChallengeStore.timerController?.resume(),
        );
      });
    }

    reactionDisposers.add(reaction<GameResult?>(
        (_) => unlimitedChallengeStore.gameResult, (gameResult) {
      _onWon(context, gameResult);
    }));

    reactionDisposers.add(reaction<bool>(
        (_) =>
            (unlimitedChallengeStore.hasGameEnded && !mainStore.isSpeedMode) ||
            (unlimitedChallengeStore.hasGameEnded &&
                mainStore.isSpeedMode &&
                unlimitedChallengeStore.ongoingGameStatus == Status.done),
        (hasGameEnded) {
      if (hasGameEnded) {
        _gameEnded(context, hasGameEnded);
      }
    }));

    reactionDisposers.add(reaction<bool>(
        (_) =>
            unlimitedChallengeStore.keepPlayingPopupType ==
                KeepPlayingPopupType.outOfTries &&
            !unlimitedChallengeStore.isUsedExtraRow,
        (shouldShowKeepPlatingPopup) {
      if (shouldShowKeepPlatingPopup) {
        showKeepPlayingDialog(parentContext: context);
      }
    }));

    reactionDisposers.add(reaction<bool>(
        (_) =>
            unlimitedChallengeStore.keepPlayingPopupType ==
            KeepPlayingPopupType.keepStreaks, (shouldShowKeepPlatingPopup) {
      if (shouldShowKeepPlatingPopup) {
        showKeepPlayingDialog(parentContext: context);
      }
    }));

    reactionDisposers.add(reaction<String?>(
      (_) => unlimitedChallengeStore.helpingHandWord,
      _onHelpingHand,
    ));

    reactionDisposers.add(reaction<Status?>(
      (_) => unlimitedChallengeStore.skipStatus,
      _onSkipLevel,
    ));
    selectedDifficultyConfig =
        difficultyItemsMap[widget.args?.letter ?? 5]!.difficultyConfig;

    unlimitedChallengeStore.setTimerController(
      TimerController(
        duration: Duration(
          seconds: unlimitedChallengeStore
              .speedModeDuration(selectedDifficultyConfig),
        ),
      ),
    );
    initUnlimitedStore();
  }

  bool _isMounted = true;

  @override
  void dispose() {
    _isMounted = false;
    for (var reactionDisposer in reactionDisposers) {
      reactionDisposer();
    }
    Loader.hide();
    super.dispose();
  }

  Future<void> initUnlimitedStore() async {
    selectedDifficultyName =
        difficultyItemsMap[selectedDifficultyConfig.lettersCount]?.name;

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (mainStore.isSpeedMode) {
        showLoading(context);
      }
      unlimitedChallengeStore.reset();

      await unlimitedChallengeStore.initialise(
          difficultyConfig: selectedDifficultyConfig, context: context);

      if (mainStore.isSpeedMode) {
        hideLoading(context);
      }
      await unlimitedChallengeStore.getOngoingSpeedGame(
          context: context, selectedDifficultyConfig: selectedDifficultyConfig);

      if (unlimitedChallengeStore.keepPlayingPopupType !=
              KeepPlayingPopupType.none &&
          !unlimitedChallengeStore.hasGameEnded) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          showKeepPlayingDialog(parentContext: context);
        });
      }
    });
  }

  void onDifficultyChanged() async {
    _eventLogger.log(mainStore.isSpeedMode
        ? Events.unlimitedSpeedDifficultyClicked
        : Events.unlimitedDifficultyClicked);
    unlimitedChallengeStore.reset();
    await unlimitedChallengeStore.initialise(
      difficultyConfig: selectedDifficultyConfig,
      context: context,
    );
    await unlimitedChallengeStore.getOngoingSpeedGame(
        context: context, selectedDifficultyConfig: selectedDifficultyConfig);
    setState(() {});
  }

  void _onWon(BuildContext context, GameResult? gameResult) {
    if (gameResult == GameResult.won) {
      Future.delayed(Duration(milliseconds: unlimitedChallengeStore.timeToWait))
          .then((value) {
        showGameToast(
          context: context,
          isSuccess: true,
          msg: resultToastMessage[unlimitedChallengeStore.currentRowNumber - 2],
        );
      });
      if (_localStorage.retrieveBool(LSKey.reviewRequested) != true) {
        inAppReview.isAvailable().then((isAvailable) {
          if (isAvailable) {
            _localStorage.saveBool(LSKey.reviewRequested, true);
            inAppReview.requestReview();
          }
        });
      }
    }
  }

  void _gameEnded(BuildContext context, bool hasGameEnded) {
    if (hasGameEnded && _isMounted) {
      if (unlimitedChallengeStore.submittedRows.length !=
              unlimitedChallengeStore.difficultyConfig.rowCount &&
          mainStore.isSpeedMode &&
          unlimitedChallengeStore.gameResult == GameResult.lost) {
        showGameToast(context: context, msg: "Time’s up!", isSuccess: false);
      }

      Future.delayed(
        Duration(milliseconds: unlimitedChallengeStore.timeToWait + 1500),
      ).then((value) {
        if (_isMounted && mounted) {
          String resultTitle = AppStrings.congratulationsText;
          if (unlimitedChallengeStore.gameResult != GameResult.won) {
            resultTitle = AppStrings.thanksForPlayingText;
          }

          showStats(
            context: context,
            heading: resultTitle,
            shouldShowAns: true,
            isFromResult:
                unlimitedChallengeStore.isGameEndedBeforePlayed != true,
          );
        }
      });
    }
  }

  void showStats({
    required BuildContext context,
    required String heading,
    required bool shouldShowAns,
    required bool isFromResult,
  }) {
    if (!_isMounted || !mounted) return;

    if (unlimitedChallengeStore.isStatsVisibleToUser) {
      Navigator.of(context).pop();
    }

    unlimitedChallengeStore.isStatsVisibleToUser = true;

    showDialog(
      context: context,
      builder: (_) => mainStore.isSpeedMode
          ? SpeedModeResultDialog(
              heading: heading,
              letterCount: selectedDifficultyConfig.lettersCount,
              context: context,
              isGameCompleted: shouldShowAns,
              submittedRows: unlimitedChallengeStore.submittedRows,
              gameResult: unlimitedChallengeStore.gameResult,
              gameId: unlimitedChallengeStore.gameId ?? "",
              isUnlimitedGame: true,
              showOpenShareAutomatically: isFromResult,
              onPlay: _goToNewWord,
            )
          : UnlimitedStatsDialog(
              onPlay: _goToNewWord,
              heading: heading,
              isGameCompleted: shouldShowAns,
              letterCount: selectedDifficultyConfig.lettersCount,
              context: context,
            ),
    ).then((value) {
      if (_isMounted) {
        unlimitedChallengeStore.isStatsVisibleToUser = false;
      }
    });
  }

  void showKeepPlayingDialog({
    required BuildContext parentContext,
  }) {
    if (!_isMounted || !mounted) return;

    bool shouldShowKeepStreak = unlimitedChallengeStore.keepPlayingPopupType ==
        KeepPlayingPopupType.keepStreaks;

    bool canShowPop =
        !(!shouldShowKeepStreak && unlimitedChallengeStore.isUsedExtraRow);

    if (canShowPop) {
      showDialog(
        barrierDismissible: false,
        context: parentContext,
        builder: (_) => KeepPlayingDialog(
          shouldShowKeepStreak: shouldShowKeepStreak,
          parentContext: parentContext,
        ),
      );
    }
  }

  Future<void> _goToNewWord({bool shouldPop = true}) async {
    if (!_isMounted || !mounted) return;

    if (shouldPop) {
      Navigator.of(context).pop();
    }

    if (mainStore.isSpeedMode) {
      unlimitedChallengeStore.startSpeedGame(
          context: context, selectedDifficultyConfig: selectedDifficultyConfig);
    } else {
      unlimitedChallengeStore.reset(hardReset: true);
      await unlimitedChallengeStore.initialise(
        difficultyConfig: selectedDifficultyConfig,
        context: context,
      );
    }

    if (_isMounted) {
      setState(() {});
      _gameBoardKey.currentState?.scrollToTop();
    }
  }

  void _onHelpingHand(String? helpingHandWord) {
    if (helpingHandWord != null) {
      InAppNotification.show(
        duration: Duration(seconds: 5),
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.of(context).octoberPurple,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            children: [
              Text(AppStrings.helpingHandText),
              Text(
                helpingHandWord.toUpperCase(),
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
              )
            ],
          ),
        ),
        context: context,
      );
    }
  }

  void _onSkipLevel(Status? status) {
    if (status == Status.pending) {
      Loader.show(
        context,
        progressIndicator: const CircularProgressIndicator.adaptive(),
      );
    } else if (status == Status.done) {
      Loader.hide();
      _goToNewWord();
      _eventLogger.log(mainStore.isSpeedMode
          ? Events.didUseSkipLevelSpeedAd
          : Events.didUseSkipLevelAd);
    } else if (status == Status.error) {
      Loader.hide();
      toast(context: context, msg: AppStrings.adErrorMessage);
    } else {
      Loader.hide();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MainScaffold(
      title: Row(
        children: [
          Visibility(
            visible: mainStore.isSpeedMode,
            child: Padding(
              padding: const EdgeInsets.only(right: 6),
              child: SvgPicture.asset(
                AppImages.watchSpeedIcon,
                height: 26,
              ),
            ),
          ),
          AutoSizeText(
            AppStrings.unlimitedText,
            style: CustomThemeText.stymieTextBlack(
                context: context, fontSize: 24, fontWeight: FontWeight.w900),
            maxLines: 1,
            minFontSize: 10,
          ),
          DropdownButtonHideUnderline(
            child: DropdownButton2<String>(
              isExpanded: false,
              isDense: true,
              hint: Text(
                difficultyItemsMap.values.first.name,
                style: CustomThemeText.urbanistTextBlack(
                  context: context,
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              items: difficultyItemsMap.values
                  .map((DifficultyItemModel item) => DropdownMenuItem<String>(
                        value: item.name,
                        child: Text(
                          item.name,
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ))
                  .toList(),
              value: selectedDifficultyName,
              onChanged: (value) {
                selectedDifficultyName = value;
                DifficultyItemModel difficultyItem = difficultyItemsMap.values
                    .firstWhere(
                        (element) => element.name == selectedDifficultyName);
                selectedDifficultyConfig = difficultyItem.difficultyConfig;
                onDifficultyChanged();
              },
              buttonStyleData: ButtonStyleData(
                padding: const EdgeInsets.only(
                  left: 10,
                ),
                elevation: 0,
              ),
              iconStyleData: IconStyleData(
                icon: Icon(Icons.keyboard_arrow_down),
                iconSize: 22,
                iconEnabledColor: CustomTheme.getBlackIconColor(context),
                iconDisabledColor: Colors.grey,
              ),
              dropdownStyleData: DropdownStyleData(
                maxHeight: 120,
                width: 180,
                padding: EdgeInsets.zero,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.warmGray)),
                offset: Offset(-65, -12),
              ),
              menuItemStyleData: MenuItemStyleData(
                height: 35,
                padding: EdgeInsets.only(left: 14, right: 14),
                selectedMenuItemBuilder: (ctx, child) {
                  return Container(
                    color: Colors.black.withOpacity(.1),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        child,
                        Padding(
                          padding: const EdgeInsets.only(right: 12),
                          child: Icon(
                            Icons.check_rounded,
                            size: 20,
                            color: AppColors.green,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
          SizedBox(
            width: 8,
          )
        ],
      ),
      onStatsPressed: () {
        _eventLogger.log(Events.statsClicked);
        showStats(
          context: context,
          heading: AppStrings.statsText.toUpperCase(),
          shouldShowAns: false,
          isFromResult: false,
        );
      },
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          child: Observer(builder: (context) {
            return Column(
              children: [
                Observer(builder: (context) {
                  return GameBoard(
                    difficultyConfig: unlimitedChallengeStore.difficultyConfig,
                    submittedRows: unlimitedChallengeStore.submittedRows,
                    timerController: unlimitedChallengeStore.timerController,
                    showTimer: !unlimitedChallengeStore.hasGameEnded,
                    onTimerEnd: () {
                      unlimitedChallengeStore.endGameDueToTimeout(
                          context: context);
                    },
                    currentRowBuilder: () => Observer(builder: (context) {
                      return GameRowContainer(
                        hints: unlimitedChallengeStore.hints,
                        row: unlimitedChallengeStore.currentGameRow,
                        maxNodes: unlimitedChallengeStore
                            .difficultyConfig.lettersCount,
                        rowCount:
                            unlimitedChallengeStore.difficultyConfig.rowCount,
                      );
                    }),
                    key: _gameBoardKey,
                  );
                }),
                Visibility(
                  visible: unlimitedChallengeStore.hasGameEnded,
                  child: Padding(
                    padding: EdgeInsets.only(top: 16),
                    child: ElevatedButton(
                      onPressed: () async {
                        _goToNewWord(shouldPop: false);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.of(context).saleGreen,
                        elevation: 0,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            AppStrings.newWordeText.toUpperCase(),
                            style: buttonTextStyle(context: context),
                          ),
                          SizedBox(
                            width: 6,
                          ),
                          Icon(
                            Icons.arrow_forward_ios_outlined,
                            size: 15,
                            color: CustomTheme.getWhiteIconColor(context),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Spacer(),
                if (unlimitedChallengeStore.gameResult == GameResult.lost)
                  FutureBuilder(
                      future: Future.delayed(Duration(
                          milliseconds: unlimitedChallengeStore.timeToWait)),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState == ConnectionState.done) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 7),
                            child: SolutionText(
                              text: unlimitedChallengeStore.wordOfTheSession
                                      ?.toUpperCase() ??
                                  '',
                            ),
                          );
                        } else {
                          return SizedBox.shrink();
                        }
                      }),
                Spacer(),
                Observer(builder: (context) {
                  return Visibility(
                    visible: !unlimitedChallengeStore.hasGameEnded,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 7),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          BoosterButton(
                            isAdFree: unlimitedChallengeStore.hintManager
                                    ?.isHintAdFree(isHelpingHand: false) ??
                                false,
                            count: unlimitedChallengeStore.hintManager
                                    ?.getCount(isHelpingHand: false) ??
                                0,
                            iconPath: AppImages.hintIconNew,
                            onPressed: unlimitedChallengeStore.hintManager
                                        ?.isHintAvailable(
                                            isHelpingHand: false) ==
                                    false
                                ? null
                                : () {
                                    if (_isMounted) {
                                      unlimitedChallengeStore
                                          .onHintClicked(
                                            selectedDifficultyConfig:
                                                selectedDifficultyConfig,
                                            context: context,
                                          )
                                          .then((value) => setState(() {}));
                                    }
                                  },
                          ),
                          SizedBox(width: 14),
                          BoosterButton(
                            isAdFree: unlimitedChallengeStore.hintManager
                                    ?.isHintAdFree(isHelpingHand: true) ??
                                false,
                            count: unlimitedChallengeStore.hintManager
                                    ?.getCount(isHelpingHand: true) ??
                                0,
                            iconPath: AppImages.helpIconNew,
                            onPressed: unlimitedChallengeStore.hintManager
                                        ?.isHintAvailable(
                                            isHelpingHand: true) ==
                                    false
                                ? null
                                : () {
                                    if (_isMounted) {
                                      unlimitedChallengeStore
                                          .onHelpingHandClick(
                                            selectedDifficultyConfig:
                                                selectedDifficultyConfig,
                                            context: context,
                                          )
                                          .then((value) => setState(() {}));
                                    }
                                  },
                          ),
                        ],
                      ),
                    ),
                  );
                }),
                SizedBox(
                  height: 8,
                ),
                Observer(builder: (context) {
                  return GameKeyBoard(
                    oldLettersUsed: unlimitedChallengeStore.oldLettersUsed,
                    lettersUsed: unlimitedChallengeStore.lettersUsed,
                    delayInMilliseconds: unlimitedChallengeStore.timeToWait,
                    isEnterButtonEnabled:
                        unlimitedChallengeStore.gamePostingStatus !=
                            Status.pending,
                    onEnter: () async {
                      if (!unlimitedChallengeStore.hasGameEnded) {
                        await unlimitedChallengeStore.submitGuess();
                        if (unlimitedChallengeStore.status == Status.error) {
                          showGameToast(
                              context: context,
                              msg: unlimitedChallengeStore.errorMessage,
                              isSuccess: false);
                        }
                        setState(() {});
                      }
                    },
                    onDelete: () {
                      if (!unlimitedChallengeStore.hasGameEnded) {
                        unlimitedChallengeStore.removeLastLetter();
                      }
                    },
                    onKeyPressed: (key) {
                      if (!unlimitedChallengeStore.hasGameEnded) {
                        unlimitedChallengeStore.addLetter(key);
                      }
                    },
                  );
                }),
              ],
            );
          }),
        ),
      ),
    );
  }
}
