import 'dart:async';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/main.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/app_gradients.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/core/constants/firestore_keys.dart';
import 'package:wordle/src/core/helpers/toast.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/models/multiplayer_game.dart';
import 'package:wordle/src/domain/multiplayer/stores/match_making_store.dart';
import 'package:wordle/src/domain/multiplayer/stores/multiplayer_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';
import 'package:wordle/src/ui/views/multiplayer/multiplayer_match_view.dart';
import 'package:wordle/src/ui/views/unlimited_challenge/unlimited_challenge_view.dart';
import 'package:wordle/src/ui/widgets/app_avatar.dart';
import 'package:wordle/src/ui/widgets/folding_cube.dart';

import '../../../core/utils/env.dart';
import '../../theme/custom_theme.dart';

class MatchMakingView extends StatefulWidget {
  const MatchMakingView({Key? key}) : super(key: key);
  static const routeName = '/multiplayer-landing/match-make';

  @override
  State<MatchMakingView> createState() => _MatchMakingViewState();
}

class _MatchMakingViewState extends State<MatchMakingView>
    with WidgetsBindingObserver {
  final _authService = ServiceLocator.locate<AuthService>();
  final matchMakingStore = ServiceLocator.locate<MatchMakingStore>();
  final multiplayerStore = ServiceLocator.locate<MultiplayerStore>();
  final gamesRef = FirebaseFirestore.instance.collection(FirestoreKeys.games);
  final List<ReactionDisposer> reactionDisposers = [];
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? gameStream;

  void _handleDone(Status? status) {
    if (status == Status.done) {
      final game = gamesRef.doc(matchMakingStore.currentMatchId).snapshots();
      gameStream = game.listen((snapshot) {
        final multiplayerGame = MultiplayerGame.fromJson(snapshot.data() ?? {});

        /// Player2 gets set last in every match
        if (multiplayerGame.playerCount == 2) {
          multiplayerStore.currentMatch = multiplayerGame;
          AppRouter.pushReplacement(MultiplayerMatchView.routeName);
        }
      });
    }
  }

  void _handleError(Status? status) {
    if (status == Status.error) {
      toast(context: context, msg: 'There was a problem finding a match.');
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    matchMakingStore.changeShowUnlimitedButton(value: false);
    multiplayerStore.reset();
    matchMakingStore.findMatch();

    reactionDisposers.add(
      reaction<Status?>((_) => matchMakingStore.status, (status) {
        _handleDone(status);
        _handleError(status);
      }),
    );
  }

  @override
  void dispose() {
    for (var reactionDisposer in reactionDisposers) {
      reactionDisposer();
    }

    gameStream?.cancel();
    WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.inactive) return;

    final isBackground = (state == AppLifecycleState.paused ||
        state == AppLifecycleState.detached);

    if (isBackground) {
      matchMakingStore.cancelMatch();
      AppRouter.pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async {
        matchMakingStore.cancelMatch();
        return true;
      },
      child: Scaffold(
          body: Container(
        padding: EdgeInsets.symmetric(horizontal: 51),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppGradients.of(context).entranceBg,
          ),
        ),
        width: double.infinity,
        child: SafeArea(
          child: Column(
            children: [
              SizedBox(
                height: 16,
              ),
              Text(
                env(EnvKey.PLATFORM_APP_NAME).toCapitalized(),
                style: CustomThemeText.stymieTextBlack(
                  context: context,
                  fontSize: 40,
                  fontWeight: FontWeight.w900,
                ),
              ),
              Spacer(
                flex: 2,
              ),
              SvgPicture.asset(
                AppImages.multiplayerTitleIcon,
              ),
              Spacer(
                flex: 2,
              ),
              Container(
                height: MediaQuery.of(context).size.height * .16,
                padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                decoration: BoxDecoration(
                  color: AppColors.darkYellow,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    AppAvatar(
                      size: 30,
                      iconSize: 26,
                      backgroundColor: Colors.black,
                      photoURL: _authService.user?.getPhotoURL(),
                    ),
                    SizedBox(
                      width: 20,
                    ),
                    Expanded(child: Observer(
                      builder: (BuildContext context) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            AutoSizeText(
                              "You",
                              maxLines: 1,
                              style: CustomThemeText.urbanistTextWhite(
                                fontSize: 24,
                                fontWeight: FontWeight.w500,
                                context: context,
                              ),
                            ),
                            SizedBox(
                              height: 12,
                            ),
                            Text(
                              _authService.user?.displayName ?? '',
                              style: CustomThemeText.urbanistTextWhite(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                context: context,
                              ),
                            ),
                          ],
                        );
                      },
                    ))
                  ],
                ),
              ),
              SizedBox(height: 16),
              Text(
                "VS",
                style: CustomThemeText.stymieTextBlack(
                  fontSize: 32,
                  fontWeight: FontWeight.w900,
                  context: context,
                ),
              ),
              SizedBox(height: 16),
              Container(
                height: MediaQuery.of(context).size.height * .16,
                padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                decoration: BoxDecoration(
                  color: AppColors.multiPlayerSearchingGreyColor,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    AppAvatar(
                      size: 30,
                      iconSize: 26,
                      backgroundColor: Colors.grey,
                      photoURL: "",
                    ),
                    SizedBox(
                      width: 20,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: AutoSizeText(
                                  "Searching",
                                  maxLines: 1,
                                  style: CustomThemeText.urbanistTextWhite(
                                    fontSize: 24,
                                    fontWeight: FontWeight.w500,
                                    context: context,
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: 12,
                              ),
                              FoldingCube(
                                color: CustomTheme.getWhiteIconColor(context),
                                size: MediaQuery.of(context).size.height * .033,
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 12,
                          ),
                          AutoSizeText(
                            "Searching for a match",
                            maxLines: 1,
                            style: CustomThemeText.urbanistTextWhite(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              context: context,
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
              Spacer(
                flex: 3,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Observer(builder: (context) {
                    return Visibility(
                      visible: matchMakingStore.showUnlimitedButton,
                      child: Padding(
                        padding: const EdgeInsets.only(right: 16),
                        child: MaterialButton(
                          onPressed: () {
                            matchMakingStore.cancelMatch();
                            AppRouter.pushReplacement(
                                UnlimitedChallengeView.routeName);
                          },
                          padding: EdgeInsets.symmetric(
                            horizontal: 24,
                          ),
                          color: AppColors.green,
                          elevation: 0,
                          height: 42,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(23.0),
                          ),
                          child: Text(
                            'Unlimited'.toUpperCase(),
                            style: CustomThemeText.urbanistTextWhite(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              context: context,
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
                  OutlinedButton(
                    onPressed: () {
                      matchMakingStore.cancelMatch();
                      AppRouter.pop();
                    },
                    style: OutlinedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      side: BorderSide(
                        width: 1.0,
                        color: CustomTheme.getBlackIconColor(
                          context,
                        ),
                      ),
                      minimumSize: Size(115, 40),
                    ),
                    child: Text(
                      'CANCEL',
                      style: CustomThemeText.urbanistTextBlack(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        context: context,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 36),
            ],
          ),
        ),
      )),
    );
  }
}
