import 'dart:async';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/main.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/constants/firestore_keys.dart';
import 'package:wordle/src/core/constants/game_result.dart';
import 'package:wordle/src/core/helpers/toast.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/strings/app_string.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/core/utils/word_list.dart';
import 'package:wordle/src/data/models/multiplayer_game.dart';
import 'package:wordle/src/domain/daily_challenge/stores/timer_store.dart';
import 'package:wordle/src/domain/main/stores/connection_store.dart';
import 'package:wordle/src/domain/main/stores/main_store.dart';
import 'package:wordle/src/domain/multiplayer/stores/match_making_store.dart';
import 'package:wordle/src/domain/multiplayer/stores/multiplayer_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';
import 'package:wordle/src/ui/views/entrance/entrance_view.dart';
import 'package:wordle/src/ui/widgets/app_avatar.dart';
import 'package:wordle/src/ui/widgets/coins_display.dart';
import 'package:wordle/src/ui/widgets/dialogs/confirm_dialog.dart';
import 'package:wordle/src/ui/widgets/game_board.dart';
import 'package:wordle/src/ui/widgets/game_keyboard.dart';
import 'package:wordle/src/ui/widgets/game_row_container.dart';
import 'package:wordle/src/ui/widgets/match_timer.dart';
import 'package:wordle/src/ui/widgets/multiplayer_results_dialog.dart';
import 'package:wordle/src/ui/widgets/solution_text.dart';

import '../../../core/constants/result_toast_message.dart';
import '../../theme/custom_theme_text.dart';
import '../../widgets/show_loading.dart';

class MultiplayerMatchView extends StatefulWidget {
  const MultiplayerMatchView({Key? key}) : super(key: key);
  static const routeName = '/multiplayer-landing/match-make/multiplayer';

  @override
  State<MultiplayerMatchView> createState() => _MultiplayerMatchViewState();
}

class _MultiplayerMatchViewState extends State<MultiplayerMatchView> {
  final multiplayerStore = ServiceLocator.locate<MultiplayerStore>();
  final mainStore = ServiceLocator.locate<MainStore>();
  final wordList = ServiceLocator.locate<WordList>();
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final List<ReactionDisposer> reactionDisposers = [];
  final _authService = ServiceLocator.locate<AuthService>();
  final gamesRef = FirebaseFirestore.instance.collection(FirestoreKeys.games);
  final matchMakingStore = ServiceLocator.locate<MatchMakingStore>();
  final _timerStore = ServiceLocator.locate<TimerStore>();
  final _connectionStore = ServiceLocator.locate<ConnectionStore>();
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>?
      gameStreamSubscription;

  int loadStatCount = 0;

  Future<void> loadStats({required GameResult result}) async {
    await multiplayerStore.loadStats().then((_) {
      if (multiplayerStore.hasErrorInStats && loadStatCount++ < 6) {
        Future.delayed(Duration(milliseconds: 500)).then((_) async {
          loadStats(result: result);
        });
      } else if ((mainStore.multiPlayerGamesPlayed ==
              multiplayerStore.multiPlayerStatsModel.gamesPlayed) &&
          (loadStatCount++ < 6)) {
        Future.delayed(Duration(milliseconds: 500)).then((_) async {
          loadStats(result: result);
        });
      } else {
        mainStore.updateMultiPlayerGamesPlayed(
            num: multiplayerStore.multiPlayerStatsModel.gamesPlayed ?? 0);
        hideLoading(context);
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (_) => MultiplayerResultsDialog(result: result),
        );
      }
    });
  }

  void _onGameResult(GameResult result) {
    _timerStore.resetMultiplayerTimerTo(
      multiplayerStore.currentMatch?.player1?.timeLeft ?? 120,
    );

    if (result == GameResult.won) {
      showGameToast(
        context: context,
        isSuccess: true,
        msg: resultToastMessage[multiplayerStore.currentRowNumber - 2],
      );
    }

    Future.delayed(Duration(milliseconds: 600)).then((_) async {
      showLoading(context);
      loadStats(result: result);
    });

    Future.delayed(Duration(milliseconds: 700)).then((value) {
      multiplayerStore.endGame();
    });
  }

  @override
  void initState() {
    super.initState();

    _eventLogger.log(Events.onlineMpChallengeVisited);

    _timerStore.initMultiplayerTimer(
      multiplayerStore.currentMatch?.player1?.timeLeft ?? 120,
    );
    final game = gamesRef.doc(matchMakingStore.currentMatchId).snapshots();
    gameStreamSubscription = game.listen((snapshot) {
      final multiplayerGame = MultiplayerGame.fromJson(snapshot.data() ?? {});
      multiplayerStore.turn = multiplayerGame.turn;
      multiplayerStore.wordOfTheSession = multiplayerGame.solution;
      multiplayerStore.syncGuesses(multiplayerGame.guesses ?? []);
      multiplayerStore.winner = multiplayerGame.winner;
    });

    reactionDisposers.add(
      reaction<String?>((_) => multiplayerStore.turn, (turn) {
        _timerStore.resetMultiplayerTimerTo(
          multiplayerStore.currentMatch?.player1?.timeLeft ?? 120,
        );
        _timerStore.startMultiplayerTimer(
          multiplayerStore.currentMatch?.player1?.timeLeft ?? 120,
          isMyTurn: multiplayerStore.isMyTurn,
          onEnd: multiplayerStore.isMyTurn ? onEnd : null,
        );
      }),
    );

    reactionDisposers.add(
      reaction<String?>((_) => multiplayerStore.winner, (winner) {
        if (winner != null) {
          _onGameResult(multiplayerStore.matchResult);
        }
      }),
    );

    _connectionStore.handleConnection(matchMakingStore.currentMatchId);
    multiplayerStore.checkOpponentStatus();
  }

  void onEnd() {
    _connectionStore.goOffline();
  }

  @override
  void dispose() {
    for (var reactionDisposer in reactionDisposers) {
      reactionDisposer();
    }
    gameStreamSubscription?.cancel();
    _connectionStore.closeConnection();
    multiplayerStore.closeOpponentStatusStream();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async {
        if (!multiplayerStore.hasGameEnded) {
          return (await showDialog<bool>(
                context: context,
                builder: (context) => ConfirmDialog(
                  title: AppStrings.leaveMatchText,
                  body: AppStrings.leaveMatchMessage,
                  onConfirm: () {
                    AppRouter.pop(true);
                  },
                  onCancel: () {
                    AppRouter.pop(false);
                  },
                ),
              )) ??
              false;
        } else {
          return true;
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.of(context).colorTone7,
        appBar: AppBar(
          iconTheme: IconThemeData(color: AppColors.of(context).colorTone2),
          centerTitle: false,
          automaticallyImplyLeading: false,
          title: Row(
            children: [
              InkWell(
                child: Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: Icon(
                    Icons.arrow_back_ios_new,
                    color: AppColors.of(context).colorTone1,
                  ),
                ),
                onTap: () async {
                  if (!multiplayerStore.hasGameEnded) {
                    (await showDialog<bool>(
                      context: context,
                      builder: (context) => ConfirmDialog(
                        title: AppStrings.leaveMatchText,
                        body: AppStrings.leaveMatchMessage,
                        onConfirm: () {
                          AppRouter.pop();
                          AppRouter.pushAndRemoveUntil(
                              EntranceView.routeName, (route) => false);
                        },
                        onCancel: () {
                          AppRouter.pop();
                        },
                      ),
                    ));
                  } else {
                    AppRouter.pop();
                  }
                },
              ),
              AutoSizeText(
                AppStrings.multiplayerText.toCapitalized(),
                style: CustomThemeText.stymieTextBlack(
                    context: context,
                    fontSize: 26,
                    fontWeight: FontWeight.w900),
                maxLines: 1,
                minFontSize: 10,
              ),
            ],
          ),
          actions: const [
            Padding(
              padding: EdgeInsets.only(right: 20),
              child: CoinsDisplay(),
            ),
          ],
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
            child: Column(
              children: [
                Observer(builder: (context) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Observer(builder: (context) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    height: 32,
                                    width: 92,
                                    decoration: BoxDecoration(
                                        color: !multiplayerStore.isMyTurn
                                            ? AppColors.darkYellow
                                            : AppColors.nonActiveGrey,
                                        borderRadius:
                                            BorderRadius.circular(16)),
                                    child: Row(
                                      children: [
                                        AppAvatar(
                                          size: 16,
                                          iconSize: 16,
                                          photoURL: multiplayerStore
                                              .opponent.photoURL,
                                          isOnline:
                                              multiplayerStore.isOpponentOnline,
                                        ),
                                        SizedBox(
                                          width: 6,
                                        ),
                                        MatchTimer(
                                          time: _timerStore.opponentTimer,
                                          active: !multiplayerStore.isMyTurn,
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(
                                    height: 4,
                                  ),
                                  AutoSizeText(
                                    multiplayerStore.opponent.displayName ?? '',
                                    style: CustomThemeText.urbanistTextBlack(
                                      context: context,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                    softWrap: false,
                                  ),
                                ],
                              );
                            })
                          ],
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Observer(builder: (context) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Container(
                                    height: 32,
                                    width: 92,
                                    decoration: BoxDecoration(
                                        color: multiplayerStore.isMyTurn
                                            ? AppColors.darkYellow
                                            : AppColors.nonActiveGrey,
                                        borderRadius:
                                            BorderRadius.circular(16)),
                                    child: Row(
                                      children: [
                                        AppAvatar(
                                          size: 16,
                                          iconSize: 16,
                                          photoURL:
                                              _authService.user?.getPhotoURL(),
                                        ),
                                        SizedBox(
                                          width: 6,
                                        ),
                                        MatchTimer(
                                          time: _timerStore.myTimer,
                                          active: multiplayerStore.isMyTurn,
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(
                                    height: 4,
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(right: 4),
                                    child: AutoSizeText(
                                      AppStrings.youText.toUpperCase(),
                                      style: CustomThemeText.urbanistTextBlack(
                                        context: context,
                                        fontSize: 13,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                      softWrap: false,
                                    ),
                                  ),
                                ],
                              );
                            })
                          ],
                        ),
                      ),
                    ],
                  );
                }),
                SizedBox(
                  height: 16,
                ),
                Observer(builder: (context) {
                  multiplayerStore.status;
                  return GameBoard(
                    difficultyConfig: multiplayerStore.difficultyConfig,
                    submittedRows: multiplayerStore.submittedRows,
                    currentRowBuilder: () => Observer(
                      builder: (context) {
                        return GameRowContainer(
                          row: multiplayerStore.currentGameRow,
                          maxNodes:
                              multiplayerStore.difficultyConfig.lettersCount,
                          rowCount: multiplayerStore.difficultyConfig.rowCount,
                        );
                      },
                    ),
                  );
                }),
                Spacer(),
                Observer(
                  builder: (context) {
                    if (multiplayerStore.gameResult == GameResult.lost) {
                      return FutureBuilder(
                          future: Future.delayed(Duration(
                            milliseconds: multiplayerStore.timeToWait,
                          )),
                          builder: (context, snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.done) {
                              return SolutionText(
                                text: multiplayerStore.wordOfTheSession
                                        ?.toUpperCase() ??
                                    '',
                              );
                            } else {
                              return SizedBox.shrink();
                            }
                          });
                    } else {
                      return SizedBox.shrink();
                    }
                  },
                ),
                Spacer(),
                Observer(builder: (context) {
                  return GameKeyBoard(
                    enabled: multiplayerStore.isMyTurn,
                    oldLettersUsed: multiplayerStore.oldLettersUsed,
                    lettersUsed: multiplayerStore.lettersUsed,
                    delayInMilliseconds: multiplayerStore.timeToWait,
                    onEnter: () {
                      if (!multiplayerStore.hasGameEnded) {
                        multiplayerStore.submitGuess();
                        if (multiplayerStore.status == Status.error) {
                          showGameToast(
                              context: context,
                              msg: multiplayerStore.errorMessage,
                              isSuccess: false);
                        }
                      }
                    },
                    onDelete: () {
                      if (!multiplayerStore.hasGameEnded) {
                        multiplayerStore.removeLastLetter();
                      }
                    },
                    onKeyPressed: (key) {
                      if (!multiplayerStore.hasGameEnded) {
                        multiplayerStore.addLetter(key);
                      }
                    },
                  );
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
