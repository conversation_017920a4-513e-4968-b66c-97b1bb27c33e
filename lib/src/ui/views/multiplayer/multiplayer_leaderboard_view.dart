import 'package:auto_size_text/auto_size_text.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_conditional_rendering/conditional.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:intl/intl.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/leaderboard.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/strings/app_string.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/domain/multiplayer/stores/multiplayer_leaderboard_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/ui/theme/custom_theme.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';
import 'package:wordle/src/ui/widgets/app_avatar.dart';
import 'package:wordle/src/ui/widgets/entrance_banner_ad.dart';
import 'package:wordle/src/ui/widgets/leader_top_3.dart';
import 'package:wordle/src/ui/widgets/shimmer_loaders/top_user_loader.dart';

class MultiplayerLeaderboardView extends StatefulWidget {
  const MultiplayerLeaderboardView({Key? key}) : super(key: key);

  static const routeName = '/multiplayer-landing/leaderboard';

  @override
  State<MultiplayerLeaderboardView> createState() =>
      _MultiplayerLeaderboardViewState();
}

class _MultiplayerLeaderboardViewState
    extends State<MultiplayerLeaderboardView> {
  final leaderboardStore = ServiceLocator.locate<MultiplayerLeaderboardStore>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      leaderboardStore.loadLeaderBoard(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColors.of(context).colorTone7,
        appBar: AppBar(
          centerTitle: false,
          automaticallyImplyLeading: false,
          iconTheme: IconThemeData(color: AppColors.of(context).colorTone2),
          title: Observer(builder: (context) {
            return Row(
              children: [
                InkWell(
                  child: Icon(
                    Icons.arrow_back_ios_new,
                    color: AppColors.of(context).colorTone1,
                  ),
                  onTap: () => AppRouter.pop(),
                ),
                SizedBox(
                  width: 5,
                ),
                AutoSizeText(
                  AppStrings.leaderboardText,
                  style: CustomThemeText.stymieTextBlack(
                      context: context,
                      fontSize: 32,
                      fontWeight: FontWeight.w900),
                  maxLines: 1,
                  minFontSize: 10,
                ),
                SizedBox(
                  width: 6,
                ),
                DropdownButtonHideUnderline(
                  child: DropdownButton2<String>(
                    isExpanded: false,
                    isDense: true,
                    customButton: Row(
                      children: [
                        Text(
                          leaderboardStore.selectedFilter,
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Icon(
                          Icons.keyboard_arrow_down,
                          size: 22,
                          color: CustomTheme.getBlackIconColor(context),
                        )
                      ],
                    ),
                    items: leaderboardFilters.keys
                        .map((String item) => DropdownMenuItem<String>(
                              value: item,
                              child: Text(
                                item,
                                style: CustomThemeText.urbanistTextBlack(
                                  context: context,
                                  fontSize: 14,
                                  fontWeight:
                                      leaderboardStore.selectedFilter == item
                                          ? FontWeight.w700
                                          : FontWeight.w400,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ))
                        .toList(),
                    value: leaderboardStore.selectedFilter,
                    onChanged: (value) {
                      leaderboardStore.onDropDownChanged(
                          value: value, context: context);
                    },
                    dropdownStyleData: DropdownStyleData(
                      maxHeight: 120,
                      width: 180,
                      padding: EdgeInsets.zero,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: AppColors.warmGray)),
                      offset: Offset(-65, -12),
                    ),
                    menuItemStyleData: MenuItemStyleData(
                      height: 35,
                      padding: EdgeInsets.only(left: 14, right: 14),
                      selectedMenuItemBuilder: (ctx, child) {
                        return Container(
                          color: Colors.black.withOpacity(.1),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              child,
                              Padding(
                                padding: const EdgeInsets.only(right: 12),
                                child: Icon(
                                  Icons.check_rounded,
                                  size: 20,
                                  color: AppColors.green,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            );
          }),
          bottom: PreferredSize(
            child: Container(
              color: AppColors.of(context).colorTone4,
              height: 1,
            ),
            preferredSize: const Size.fromHeight(1.0),
          ),
        ),
        body: Observer(builder: (context) {
          if (leaderboardStore.leadBoardScores.length > 3) {
            return SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(height: 10),
                  SizedBox(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Flexible(
                          child: Conditional.single(
                            context: context,
                            conditionBuilder: (_) =>
                                leaderboardStore.scoresStatus == Status.done,
                            widgetBuilder: (_) => LeaderTop3(
                              score:
                                  leaderboardStore.leadBoardScores[0].points ??
                                      0,
                              place: 1,
                              playerName: leaderboardStore
                                  .leadBoardScores[0].playerName,
                              avatarUrl:
                                  leaderboardStore.leadBoardScores[0].avatarUrl,
                            ),
                            fallbackBuilder: (_) => TopUserLoader(size: 60),
                          ),
                        ),
                        Flexible(
                          child: Conditional.single(
                            context: context,
                            conditionBuilder: (_) =>
                                leaderboardStore.scoresStatus == Status.done,
                            widgetBuilder: (_) => LeaderTop3(
                              score:
                                  leaderboardStore.leadBoardScores[1].points ??
                                      0,
                              place: 2,
                              playerName: leaderboardStore
                                  .leadBoardScores[1].playerName,
                              avatarUrl:
                                  leaderboardStore.leadBoardScores[1].avatarUrl,
                            ),
                            fallbackBuilder: (_) => TopUserLoader(size: 60),
                          ),
                        ),
                        Flexible(
                          child: Conditional.single(
                            context: context,
                            conditionBuilder: (_) =>
                                leaderboardStore.scoresStatus == Status.done,
                            widgetBuilder: (_) => LeaderTop3(
                              score:
                                  leaderboardStore.leadBoardScores[2].points ??
                                      0,
                              place: 3,
                              playerName: leaderboardStore
                                  .leadBoardScores[2].playerName,
                              avatarUrl:
                                  leaderboardStore.leadBoardScores[2].avatarUrl,
                            ),
                            fallbackBuilder: (_) => TopUserLoader(size: 60),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 20,
                  ),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: ScrollPhysics(parent: null),
                    itemCount: leaderboardStore.leadBoardScores.length - 4,
                    itemBuilder: ((context, index) {
                      final score = leaderboardStore.leadBoardScores[index + 3];
                      // final rank = leaderboardStore.myRank[index + 3];
                      return ListTile(
                        leading: CircleAvatar(
                          radius: 15,
                          child: AutoSizeText(
                            (score.rank ?? "").toString(),
                            style: TextStyle(
                                color: AppColors.of(context).colorTone7),
                          ),
                          backgroundColor: AppColors.leaderboardIndexBgColor,
                        ),
                        title: Row(
                          children: [
                            AppAvatar(
                              size: 20,
                              iconSize: 15,
                              photoURL: score.avatarUrl,
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(score.playerName ?? ''),
                                Text(
                                  NumberFormat.decimalPattern()
                                      .format(score.points ?? 0),
                                  style: TextStyle(fontSize: 13),
                                )
                              ],
                            ),
                          ],
                        ),
                      );
                    }),
                  ),
                  ListTile(
                    leading: CircleAvatar(
                      radius: 15,
                      child: AutoSizeText(
                        ((leaderboardStore
                                        .leadBoardScores[(leaderboardStore
                                                .leadBoardScores.length -
                                            1)]
                                        .rank ==
                                    0)
                                ? "#"
                                : leaderboardStore
                                    .leadBoardScores[(leaderboardStore
                                            .leadBoardScores.length -
                                        1)]
                                    .rank)
                            .toString(),
                        style:
                            TextStyle(color: AppColors.of(context).colorTone7),
                      ),
                      backgroundColor: AppColors.leaderboardIndexBgSelfColor,
                    ),
                    title: Row(
                      children: [
                        AppAvatar(
                          size: 20,
                          iconSize: 15,
                          photoURL: leaderboardStore
                              .leadBoardScores[
                                  (leaderboardStore.leadBoardScores.length - 1)]
                              .avatarUrl,
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(AppStrings.youText),
                            Text(
                              NumberFormat.decimalPattern().format(
                                  leaderboardStore
                                          .leadBoardScores[(leaderboardStore
                                                  .leadBoardScores.length -
                                              1)]
                                          .points ??
                                      0),
                              style: TextStyle(fontSize: 13),
                            )
                          ],
                        ),
                      ],
                    ),
                  )
                ],
              ),
            );
          } else {
            return Text(leaderboardStore.errorMessage);
          }
        }),
        resizeToAvoidBottomInset: false,
        bottomNavigationBar: EntranceBannerAd());
  }
}
