import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/core/strings/app_string.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/domain/main/stores/main_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/ui/views/multiplayer/match_making_view.dart';
import 'package:wordle/src/ui/views/multiplayer/multiplayer_leaderboard_view.dart';
import 'package:wordle/src/ui/widgets/bottom_sheets/auth_bottom_sheet.dart';
import 'package:wordle/src/ui/widgets/dialogs/info_dialog.dart';
import 'package:wordle/src/ui/widgets/how_to_multiplayer_modal.dart';
import 'package:wordle/src/ui/widgets/multiplayer_option.dart';

class MultiplayerLandingView extends StatelessWidget {
  MultiplayerLandingView({Key? key}) : super(key: key);
  static const routeName = '/multiplayer-landing';
  final _authService = ServiceLocator.locate<AuthService>();
  final _mainStore = ServiceLocator.locate<MainStore>();

  @override
  Widget build(BuildContext context) {
    if (_mainStore.isFirstMultiplayerLaunch) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog(
          context: context,
          builder: (context) => HowToMultiplayerModal(),
        );
      });
    }

    return Scaffold(
      backgroundColor: AppColors.of(context).colorTone7,
      appBar: AppBar(
        backgroundColor: AppColors.of(context).colorTone7,
        iconTheme: IconThemeData(color: AppColors.of(context).colorTone2),
        title: AutoSizeText(
          AppStrings.multiplayerText,
          style: TextStyle(
            color: AppColors.of(context).colorTone1,
          ),
          maxLines: 1,
          minFontSize: 10,
        ),
        bottom: PreferredSize(
          child: Container(
            color: AppColors.of(context).colorTone4,
            height: 1,
          ),
          preferredSize: const Size.fromHeight(1.0),
        ),
        actions: [
          IconButton(
            tooltip: AppStrings.howToPlayText,
            icon: SvgPicture.asset(AppImages.helpIcon),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => HowToMultiplayerModal(),
              );
            },
          ),
          IconButton(
            onPressed: () {
              if (!_authService.isAuthenticated()) {
                showModalBottomSheet(
                  context: context,
                  builder: (_) => AuthBottomSheet(
                    onAuthenticate: () {
                      String name = _authService.user?.getDisplayName() ?? '';
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('${AppStrings.signedInAsText} $name'),
                        ),
                      );
                      AppRouter.push(MultiplayerLeaderboardView.routeName);
                    },
                  ),
                );
              } else {
                AppRouter.push(MultiplayerLeaderboardView.routeName);
              }
            },
            icon: FaIcon(
              FontAwesomeIcons.trophy,
              color: Colors.amber,
            ),
          )
        ],
      ),
      body: Container(
        padding: EdgeInsets.all(10),
        child: GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
          children: [
            MultiplayerOption(
              title: AppStrings.classicText,
              subtitle: AppStrings.twoMinPerGuessText,
              imagePath: AppImages.worldConnected,
              onTap: () {
                if (!_authService.isAuthenticated()) {
                  showModalBottomSheet(
                    context: context,
                    builder: (_) => AuthBottomSheet(
                      onAuthenticate: () {
                        String name = _authService.user?.getDisplayName() ?? '';

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('${AppStrings.signedInAsText} $name'),
                          ),
                        );
                        if (_mainStore.isAllowedToPlayMultiplayer) {
                          AppRouter.push(MatchMakingView.routeName);
                        } else {
                          showDialog(
                            context: context,
                            builder: (_) => InfoDialog(
                              title: AppStrings.errorText,
                              body: AppStrings.notEnoughCoinsMessage,
                            ),
                          );
                        }
                      },
                    ),
                  );
                } else {
                  if (_mainStore.isAllowedToPlayMultiplayer) {
                    AppRouter.push(MatchMakingView.routeName);
                  } else {
                    showDialog(
                      context: context,
                      builder: (_) => InfoDialog(
                        title: AppStrings.errorText,
                        body: AppStrings.notEnoughCoinsMessage,
                      ),
                    );
                  }
                }
              },
            ),
            Stack(
              alignment: Alignment.center,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: ColorFiltered(
                    colorFilter: ColorFilter.mode(
                      Colors.black, // 0 = Colored, 1 = Black & White
                      BlendMode.saturation,
                    ),
                    child: MultiplayerOption(
                      title: AppStrings.vsFriendText,
                      subtitle: AppStrings.noTimeLimitMessage,
                      imagePath: AppImages.friendsOnline,
                    ),
                  ),
                ),
                Container(
                  color: Colors.red,
                  child: Text(
                    AppStrings.comingSoonText,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                  width: double.infinity,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
