import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/core/constants/config_param.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/services/remote_config/remote_config_service_contract.dart';
import 'package:wordle/src/ui/views/entrance/entrance_view.dart';

class ForceUpdateView extends StatelessWidget {
  ForceUpdateView({Key? key}) : super(key: key);
  static const routeName = 'force-update';
  final _configService = ServiceLocator.locate<RemoteConfigServiceContract>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 25),
          child: Column(
            children: [
              SizedBox(height: 100),
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.asset(AppImages.appLogo, height: 50),
              ),
              SizedBox(height: 20),
              Text(
                _configService.getString(ConfigParam.forceUpdateMessage),
                style: TextStyle(fontSize: 20),
              ),
              SizedBox(height: 20),
              ElevatedButton(
                child: Text(
                  'UPDATE',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                onPressed: () {
                  launchUrl(Uri.parse(
                      _configService.getString(ConfigParam.forceUpdateLink)));
                },
              ),
              FutureBuilder(
                future: Future.delayed(Duration(seconds: 3)),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.done) {
                    return TextButton(
                      onPressed: () =>
                          AppRouter.pushReplacement(EntranceView.routeName),
                      child: Text('Not now'),
                    );
                  } else {
                    return SizedBox.shrink();
                  }
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
