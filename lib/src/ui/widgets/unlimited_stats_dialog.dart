import 'package:auto_size_text/auto_size_text.dart';
import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/font_family.dart';
import 'package:wordle/src/core/constants/game_result.dart';
import 'package:wordle/src/core/helpers/toast.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/models/ordinal_guess.dart';
import 'package:wordle/src/domain/unlimited_challenge/stores/unlimited_challenge_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/services/stats_service.dart';
import 'package:wordle/src/ui/theme/app_theme.dart';
import 'package:wordle/src/ui/widgets/dialogs/confirm_dialog.dart';
import 'package:wordle/src/ui/widgets/reusable_dialog.dart';
import 'package:wordle/src/ui/widgets/reusable_solution_box.dart';
import 'package:wordle/src/ui/widgets/show_loading.dart';

import '../../core/constants/map_const.dart';
import '../../data/models/difficulty_config.dart';
import '../theme/custom_theme.dart';
import '../theme/custom_theme_text.dart';

class UnlimitedStatsDialog extends StatefulWidget {
  final void Function()? onPlay;
  final String heading;
  final int letterCount;
  final BuildContext context;
  final bool isGameCompleted;

  const UnlimitedStatsDialog({
    Key? key,
    this.onPlay,
    required this.heading,
    required this.letterCount,
    required this.context,
    this.isGameCompleted = false,
  }) : super(key: key);

  @override
  State<UnlimitedStatsDialog> createState() => _UnlimitedStatsDialogState();
}

class _UnlimitedStatsDialogState extends State<UnlimitedStatsDialog> {
  final statsService = ServiceLocator.locate<StatsService>();

  final unlimitedChallengeStore =
      ServiceLocator.locate<UnlimitedChallengeStore>();

  late DifficultyConfig selectedDifficultyConfig;
  final double scoreSize = 26;
  double avg = 0;

  List<OrdinalGuess> ordinalGuesses = [];

  @override
  void initState() {
    super.initState();
    selectedDifficultyConfig =
        difficultyItemsMap[widget.letterCount]!.difficultyConfig;
    loadStatsAndCalculateAvgScore();
  }

  List<charts.Series<OrdinalGuess, String>> createGuessDistributionData() {
    ordinalGuesses =
        statsService.guessDistribution.entries.map<OrdinalGuess>((e) {
      return OrdinalGuess(e.key, e.value);
    }).toList();

    return [
      charts.Series<OrdinalGuess, String>(
        id: 'unlimitedGuessDistribution',
        data: ordinalGuesses,
        domainFn: (ordinalGuess, _) => ordinalGuess.row.toString(),
        measureFn: (ordinalGuess, _) => ordinalGuess.guesses,
        labelAccessorFn: (ordinalGuess, _) => ordinalGuess.guesses.toString(),
        colorFn: (ordinalGuess, __) {
          if (unlimitedChallengeStore.submittedRows.length ==
                  ordinalGuess.row &&
              unlimitedChallengeStore.gameResult == GameResult.won) {
            return AppColors.brightness == Brightness.light
                ? charts.Color(a: 255, r: 106, g: 170, b: 100)
                : charts.Color(a: 255, r: 83, g: 141, b: 78);
          } else {
            return charts.Color.fromHex(code: "#434142");
          }
        },
        outsideLabelStyleAccessorFn: (_, __) {
          final color = AppColors.brightness == Brightness.light
              ? charts.Color.fromHex(code: '#1a1a1b')
              : charts.Color.fromHex(code: '#d7dadc');

          return charts.TextStyleSpec(
              color: color,
              fontSize: 15,
              fontWeight: "BOLD",
              fontFamily: FontFamily.urbanist);
        },
        insideLabelStyleAccessorFn: (_, __) {
          return charts.TextStyleSpec(
              color: charts.MaterialPalette.white,
              fontSize: 15,
              fontWeight: "BOLD",
              fontFamily: FontFamily.urbanist);
        },
      )
    ];
  }

  charts.Color getYAxisColor() {
    return AppColors.brightness == Brightness.light
        ? charts.Color.fromHex(code: '#1a1a1b')
        : charts.Color.fromHex(code: '#d7dadc');
  }

  void calculateAvgScore() {
    int length = 0;
    double sum = 0;
    for (var element in statsService.guessDistribution.entries) {
      if (element.value != 0) {
        length = length + element.value;
      }
      sum = sum + (element.value * element.key);
    }

    avg = length > 0 ? sum / length : 0;
  }

  Future<void> loadStatsAndCalculateAvgScore() async {
    WidgetsBinding.instance!.addPostFrameCallback((_) async {
      showLoading(widget.context);
      await statsService.loadStats(
        parentContext: context,
        gameType: selectedDifficultyConfig.unlimitedStatsType.gameType,
        gameName: selectedDifficultyConfig.unlimitedStatsType.gameName,
      );
      calculateAvgScore();
      hideLoading(widget.context);
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(builder: (context, setState) {
      return reusableDialog(
        context: context,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: 28,
            ),
            Center(
              child: AutoSizeText(
                widget.heading,
                maxLines: 1,
                style: CustomThemeText.stymieTextBlack(
                    context: context,
                    fontSize: 32,
                    fontWeight: FontWeight.w900),
              ),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Text(
                  'STATISTICS',
                  style: CustomThemeText.urbanistTextBlack(
                    context: context,
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                SizedBox(
                  width: 6,
                ),
                Text(
                  selectedDifficultyConfig.difficultyTitle,
                  style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                      fontFamily: FontFamily.urbanist,
                      color: selectedDifficultyConfig.difficultyColor),
                ),
              ],
            ),
            SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Flexible(
                  child: Column(
                    children: [
                      AutoSizeText(
                        statsService.gamesPlayed,
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: scoreSize,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      Text(
                        'Played',
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Flexible(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AutoSizeText(
                        statsService.winPercentage,
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: scoreSize,
                          fontWeight: FontWeight.w700,
                        ),
                        maxLines: 1,
                      ),
                      Text(
                        'Win %',
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Flexible(
                  child: Column(
                    children: [
                      AutoSizeText(
                        statsService.currentStreak,
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: scoreSize,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      Text(
                        'Current Streak',
                        softWrap: true,
                        maxLines: 2,
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                Flexible(
                  child: Column(
                    children: [
                      AutoSizeText(
                        statsService.maxStreak,
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: scoreSize,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      Text(
                        'Max Streak',
                        softWrap: true,
                        maxLines: 2,
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Observer(builder: (context) {
              return Visibility(
                visible: unlimitedChallengeStore.hasGameEnded &&
                    widget.isGameCompleted,
                child: Column(
                  children: [
                    SizedBox(
                      height: 16,
                    ),
                    Text(
                      "Answer".toUpperCase(),
                      style: CustomThemeText.urbanistTextBlack(
                        context: context,
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(
                      height: 6,
                    ),
                    ReusableSolutionBox(
                        solution: unlimitedChallengeStore.wordOfTheSession
                                ?.toUpperCase() ??
                            '',
                        boxSize: 28,
                        fontSize: 20),
                  ],
                ),
              );
            }),
            Padding(
              padding: const EdgeInsets.only(left: 4, top: 24),
              child: Row(
                children: [
                  AutoSizeText(
                    'GUESS DISTRIBUTION',
                    maxLines: 1,
                    style: CustomThemeText.urbanistTextBlack(
                      context: context,
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  SizedBox(width: 6),
                  Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      border: Border.all(
                          color: CustomTheme.getBlackBorderColor(context)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Text(
                          'Avg.Score',
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(width: 4),
                        Text(
                          avg.toStringAsFixed(2),
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: MediaQuery.of(context).size.height * .25,
              child: charts.BarChart(
                createGuessDistributionData(),
                vertical: false,
                barRendererDecorator: charts.BarLabelDecorator<String>(),
                primaryMeasureAxis:
                    charts.NumericAxisSpec(renderSpec: charts.NoneRenderSpec()),
                domainAxis: charts.OrdinalAxisSpec(
                  renderSpec: charts.SmallTickRendererSpec(
                    labelStyle: charts.TextStyleSpec(
                      fontSize: 15,
                      fontWeight: "BOLD",
                      fontFamily: FontFamily.urbanist,
                      color: getYAxisColor(),
                    ),
                  ),
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => ConfirmDialog(
                    title: 'Reset Stats',
                    body: '''Are you sure you want to reset your stats?
You  will lose your streak for this difficulty (${selectedDifficultyConfig.difficultyTitle}), and start from zero.''',
                    onConfirm: () async {
                      showLoading(widget.context);
                      await statsService.resetStats(
                        parentContext: widget.context,
                        gameType: selectedDifficultyConfig
                            .unlimitedStatsType.gameType,
                        gameName: selectedDifficultyConfig
                            .unlimitedStatsType.gameName,
                      );
                      hideLoading(widget.context);
                      widget.onPlay?.call();
                      setState(() {});
                      AppRouter.pop();
                      toast(context: context, msg: 'Unlimited stats reset.');
                    },
                  ),
                );
                setState(() {});
              },
              style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 10),
                  minimumSize: Size(50, 0),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  alignment: Alignment.centerLeft),
              child: Text(
                'RESET STATS',
                style: CustomThemeText.urbanistTextBlack(
                  context: context,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  textDecoration: TextDecoration.underline,
                ),
              ),
            ),
            if (unlimitedChallengeStore.hasGameEnded) ...[
              SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      widget.onPlay?.call();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.of(context).saleGreen,
                      elevation: 0,
                    ),
                    child: Text(
                      'NEW WORDE',
                      style: buttonTextStyle(context: context),
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 12,
              ),
            ],
          ],
        ),
      );
    });
  }
}
