import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/ui/widgets/booster_icon.dart';

class BoostersDialog extends StatelessWidget {
  const BoostersDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppColors.of(context).colorTone7,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(5.0),
      ),
      title: const Text(
        'BOOSTERS',
        style: TextStyle(fontWeight: FontWeight.w600),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      content: SingleChildScrollView(
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BoosterIcon(
                  iconPath: AppImages.hintIconNew,
                  padding:
                      EdgeInsets.only(left: 8, right: 4, top: 6, bottom: 6),
                ),
                SizedBox(width: 5),
                Expanded(
                  child: const Text(
                    'Use this hint booster to reveal one correct letter. You\'ll receive 2 each game.',
                  ),
                ),
              ],
            ),
            SizedBox(height: 10),
            Divider(),
            SizedBox(height: 10),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BoosterIcon(
                  iconPath: AppImages.helpIconNew,
                  padding:
                      EdgeInsets.only(left: 8, right: 4, top: 6, bottom: 6),
                ),
                SizedBox(width: 5),
                Expanded(
                  child: const Text(
                    'Can\'t think of a word? Use a helping hand to get a random word you can use. Use it to try out new starter words, or throw-away words. You\'ll receive 2 each game.',
                  ),
                ),
              ],
            ),
            SizedBox(height: 10),
            Divider(),
            SizedBox(height: 10),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BoosterIcon(
                  iconPath: AppImages.skipAdIconNew,
                  padding:
                      EdgeInsets.only(left: 10, right: 7, top: 5, bottom: 5),
                ),
                SizedBox(width: 5),
                Expanded(
                  child: const Text(
                    'Skip the level without affecting your streak, free users will have to watch an ad to skip, premium users skip for free.',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              TextButton(
                onPressed: () {
                  AppRouter.pop();
                },
                child: const Text(
                  'OK',
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }
}
