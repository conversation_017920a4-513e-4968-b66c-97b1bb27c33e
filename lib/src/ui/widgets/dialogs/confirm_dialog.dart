import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/font_family.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';
import 'package:wordle/src/ui/widgets/reusable_dialog.dart';

import '../../theme/app_theme.dart';
import '../../theme/custom_theme.dart';

class ConfirmDialog extends StatefulWidget {
  final String title;
  final String body;
  final String cancelText;
  final String confirmText;
  final void Function()? onCancel;
  final void Function() onConfirm;

  const ConfirmDialog({
    Key? key,
    required this.title,
    required this.body,
    this.onCancel,
    this.cancelText = 'Cancel',
    this.confirmText = 'Confirm',
    required this.onConfirm,
  }) : super(key: key);

  @override
  _ConfirmDialogState createState() => _ConfirmDialogState();
}

class _ConfirmDialogState extends State<ConfirmDialog> {
  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(statusBarColor: Colors.transparent));
    return reusableDialog(
      context: context,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 15,
          ),
          AutoSizeText(
            widget.title,
            textAlign: TextAlign.center,
            maxLines: 1,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontFamily: FontFamily.stymie,
              fontSize: 26,
            ),
          ),
          const SizedBox(height: 16.0),
          SingleChildScrollView(
            child: AutoSizeText(
              widget.body,
              textAlign: TextAlign.center,
              maxLines: 3,
              style: TextStyle(
                  fontFamily: FontFamily.urbanist,
                  fontWeight: FontWeight.w400,
                  fontSize: 20),
            ),
          ),
          const SizedBox(height: 28.0),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: widget.onCancel ??
                    () {
                      Navigator.of(context).pop();
                    },
                style: OutlinedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  side: BorderSide(
                    width: 1.0,
                    color: CustomTheme.getBlackBorderColor(context),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  widget.cancelText.toUpperCase(),
                  style: CustomThemeText.urbanistTextBlack(
                    context: context,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

              const SizedBox(width: 8.0), // Add spacing between the buttons

              ElevatedButton(
                onPressed: widget.onConfirm,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.of(context).saleGreen,
                  elevation: 0,
                ),
                child: Text(
                  widget.confirmText.toUpperCase(),
                  style: buttonTextStyle(context: context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
