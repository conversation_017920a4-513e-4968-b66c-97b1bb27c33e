import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wordle/main.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';
import 'package:wordle/src/ui/widgets/reusable_dialog.dart';

import '../../../core/constants/app_colors.dart';
import '../../theme/custom_theme.dart';

class CustomNotificationDialog extends StatefulWidget {
  const CustomNotificationDialog({
    Key? key,
  }) : super(key: key);

  @override
  State<CustomNotificationDialog> createState() =>
      _CustomNotificationDialogState();
}

class _CustomNotificationDialogState extends State<CustomNotificationDialog> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(builder: (context, setState) {
      return reusableDialog(
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 32),
        context: context,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "Stay informed!",
              style: CustomThemeText.urbanistTextBlack(
                context: context,
                fontSize: 32,
                fontWeight: FontWeight.w900,
              ),
            ),
            SizedBox(
              height: 28,
            ),
            SvgPicture.asset(
              CustomTheme.isLightMode(context)
                  ? AppImages.notificationIcon
                  : AppImages.notificationBlackThemeIcon,
              height: 94,
            ),
            SizedBox(
              height: 32,
            ),
            Text(
              "Get new game updates and reminders.\nTurn on notifications to stay informed.",
              textAlign: TextAlign.center,
              style: CustomThemeText.urbanistTextBlack(
                context: context,
                height: 1.15,
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(
              height: 28,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: OutlinedButton(
                    onPressed: () {
                      AppRouter.pop();
                    },
                    style: OutlinedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      side: BorderSide(
                        width: 1.0,
                        color: CustomTheme.getBlackIconColor(
                          context,
                        ),
                      ),
                      minimumSize: Size(115, 40),
                    ),
                    child: Text(
                      'No, Thanks'.toUpperCase(),
                      style: CustomThemeText.urbanistTextBlack(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        context: context,
                      ),
                    ),
                  ),
                ),
                MaterialButton(
                  onPressed: () {
                    AppRouter.pop();
                    askNotificationPermission();
                  },
                  padding: EdgeInsets.symmetric(
                    horizontal: 34,
                  ),
                  color: AppColors.green,
                  elevation: 0,
                  height: 42,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(23.0),
                  ),
                  child: Text(
                    'Turn on'.toUpperCase(),
                    style: CustomThemeText.urbanistTextWhite(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      context: context,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }
}
