import 'package:auto_size_text/auto_size_text.dart';
import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/font_family.dart';
import 'package:wordle/src/ui/widgets/graph/game_summary_speed_graph.dart';
import 'package:wordle/src/ui/widgets/reusable_dialog.dart';
import 'package:wordle/src/ui/widgets/speed_mode_stats_image_generator.dart';

import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_images.dart';
import '../../../core/constants/game_result.dart';
import '../../../core/constants/map_const.dart';
import '../../../core/helpers/toast.dart';
import '../../../core/utils/env.dart';
import '../../../core/utils/service_locator.dart';
import '../../../data/models/difficulty_config.dart';
import '../../../data/models/difficulty_item_model.dart';
import '../../../data/models/game_row.dart';
import '../../../data/models/ordinal_guess.dart';
import '../../../domain/daily_challenge/stores/daily_challenge_store.dart';
import '../../../domain/daily_challenge/stores/timer_store.dart';
import '../../../domain/unlimited_challenge/stores/unlimited_challenge_store.dart';
import '../../../router/app_router.dart';
import '../../../services/events/event_logger_contract.dart';
import '../../../services/stats_service.dart';
import '../../theme/app_theme.dart';
import '../../theme/custom_theme.dart';
import '../../theme/custom_theme_text.dart';
import '../../views/unlimited_challenge/unlimited_challenge_view.dart';
import '../reusable_circle_indicator.dart';
import '../show_loading.dart';
import 'confirm_dialog.dart';

class SpeedModeResultDialog extends StatefulWidget {
  final void Function()? onPlay;
  final String heading;
  final int letterCount;
  final BuildContext context;
  final bool isGameCompleted;
  final ObservableList<GameRow> submittedRows;
  final GameResult? gameResult;
  final String gameId;
  final bool isUnlimitedGame;
  final bool showOpenShareAutomatically;

  const SpeedModeResultDialog({
    Key? key,
    this.onPlay,
    required this.heading,
    required this.letterCount,
    required this.context,
    required this.isGameCompleted,
    required this.submittedRows,
    required this.gameResult,
    required this.gameId,
    required this.isUnlimitedGame,
    required this.showOpenShareAutomatically,
  }) : super(key: key);

  @override
  State<SpeedModeResultDialog> createState() => _SpeedIntroDialogState();
}

class _SpeedIntroDialogState extends State<SpeedModeResultDialog>
    with SingleTickerProviderStateMixin {
  final statsService = ServiceLocator.locate<StatsService>();
  final timerStore = ServiceLocator.locate<TimerStore>();
  final dailyChallengeStore = ServiceLocator.locate<DailyChallengeStore>();
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final unlimitedChallengeStore =
      ServiceLocator.locate<UnlimitedChallengeStore>();
  late TabController tabController;
  final double scoreSize = 20;

  late DifficultyConfig selectedDifficultyConfig;
  double avg = 0;
  double graphPercentage = 0;
  num betterThanPercentage = 0;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: 2, vsync: this);
    selectedDifficultyConfig =
        difficultyItemsMap[widget.letterCount]!.difficultyConfig;
    loadStatsAndCalculateAvgScore();
  }

  List<charts.Series<OrdinalGuess, String>> createGuessDistributionData() {
    final ordinalGuesses =
        statsService.guessDistribution.entries.map<OrdinalGuess>((e) {
      return OrdinalGuess(e.key, e.value);
    }).toList();

    return [
      charts.Series<OrdinalGuess, String>(
        id: 'SpeedGuessDistribution',
        data: ordinalGuesses,
        domainFn: (ordinalGuess, _) => ordinalGuess.row.toString(),
        measureFn: (ordinalGuess, _) => ordinalGuess.guesses,
        labelAccessorFn: (ordinalGuess, _) => ordinalGuess.guesses.toString(),
        colorFn: (ordinalGuess, __) {
          if (widget.submittedRows.length == ordinalGuess.row &&
              widget.gameResult == GameResult.won) {
            return AppColors.brightness == Brightness.light
                ? charts.Color(a: 255, r: 106, g: 170, b: 100)
                : charts.Color(a: 255, r: 83, g: 141, b: 78);
          } else {
            return charts.Color.fromHex(code: "#434142");
          }
        },
        outsideLabelStyleAccessorFn: (_, __) {
          final color = AppColors.brightness == Brightness.light
              ? charts.Color.fromHex(code: '#1a1a1b')
              : charts.Color.fromHex(code: '#d7dadc');

          return charts.TextStyleSpec(
            color: color,
            fontSize: 15,
            fontWeight: "BOLD",
            fontFamily: FontFamily.urbanist,
          );
        },
        insideLabelStyleAccessorFn: (_, __) {
          return charts.TextStyleSpec(
            color: charts.MaterialPalette.white,
            fontSize: 15,
            fontWeight: "BOLD",
            fontFamily: FontFamily.urbanist,
          );
        },
      )
    ];
  }

  charts.Color getYAxisColor() {
    return AppColors.brightness == Brightness.light
        ? charts.Color.fromHex(code: '#1a1a1b')
        : charts.Color.fromHex(code: '#d7dadc');
  }

  void calculateGraphPercentage() {
    graphPercentage =
        ((statsService.stats.getSpeedGameDetailsModel?.totalPoints ?? 0) /
                (statsService.stats.getSpeedGameSummaryModel?.maxPoints ?? 1)) *
            100;
  }

  Future<void> loadStatsAndCalculateAvgScore() async {
    final startTime = DateTime.now();

    await statsService.loadStats(
      parentContext: context,
      gameType: widget.isUnlimitedGame
          ? selectedDifficultyConfig.unlimitedStatsType.gameType
          : selectedDifficultyConfig.dailyStatsType.gameType,
      gameName: widget.isUnlimitedGame
          ? selectedDifficultyConfig.unlimitedStatsType.gameName
          : selectedDifficultyConfig.dailyStatsType.gameName,
    );
    calculateAvgScore();
    await loadDetailAndGraphStats();
    calculateGraphPercentage();

    final endTime = DateTime.now();
    final duration = endTime.difference(startTime).inMilliseconds;
    debugPrint("loadStatsAndCalculateAvgScore took $duration");
    isLoading = false;
    if (mounted) {
      if (widget.showOpenShareAutomatically && graphPercentage >= 80) {
        StatsImageGenerator.shareStatsAsImage(
          context: context,
          stats: statsService.stats,
          selectedDifficultyConfig: selectedDifficultyConfig,
          batterThanPercentage: graphPercentage,
          submittedRows: widget.submittedRows,
        );
      }
      setState(() {});
    }
  }

  loadDetailAndGraphStats() async {
    await Future.wait([
      getSpeedGameDetails(),
      getSpeedSummary(),
    ]);
  }

  Future<void> getSpeedGameDetails() async {
    if (widget.gameId != "") {
      await statsService.getSpeedGameDetailsNetworkCall(
        context: context,
        gameId: widget.gameId,
      );
    }
  }

  void calculateAvgScore() {
    int length = 0;
    double sum = 0;
    for (var element in statsService.guessDistribution.entries) {
      if (element.value != 0) {
        length = length + element.value;
      }
      sum = sum + (element.value * element.key);
    }

    avg = length > 0 ? sum / length : 0;
  }

  Future<void> getSpeedSummary() async {
    await statsService.getSpeedGameSummaryNetworkCall(
      context: context,
      gameType: widget.isUnlimitedGame
          ? selectedDifficultyConfig.unlimitedStatsType.gameType
          : selectedDifficultyConfig.dailyStatsType.gameType,
      gameName: widget.isUnlimitedGame
          ? selectedDifficultyConfig.unlimitedStatsType.gameName
          : selectedDifficultyConfig.dailyStatsType.gameName,
    );
    betterThanPercentage =
        statsService.stats.getSpeedGameSummaryModel?.betterPercentage ?? 0;
  }

  Future<void> resetStats() async {
    showLoading(widget.context);
    await statsService.resetStats(
      parentContext: widget.context,
      gameType: widget.isUnlimitedGame
          ? selectedDifficultyConfig.unlimitedStatsType.gameType
          : selectedDifficultyConfig.dailyStatsType.gameType,
      gameName: widget.isUnlimitedGame
          ? selectedDifficultyConfig.unlimitedStatsType.gameName
          : selectedDifficultyConfig.dailyStatsType.gameName,
    );
    hideLoading(widget.context);

    if (!widget.isUnlimitedGame) {
      await dailyChallengeStore.startSpeedGame(
        context: context,
        selectedDifficultyConfig: selectedDifficultyConfig,
      );
      setState(() {});
      AppRouter.pop();
    } else {
      widget.onPlay?.call();
      setState(() {});
    }

    AppRouter.pop();
    toast(
        context: context,
        msg: widget.isUnlimitedGame
            ? 'Unlimited stats reset.'
            : 'Daily stats reset.');
  }

  void goToNewSpeedGame() {
    if (widget.isUnlimitedGame) {
      widget.onPlay?.call();
    } else {
      AppRouter.pop();
      AppRouter.push(
        UnlimitedChallengeView.routeName,
        arguments: UnlimitedChallengeArgs(
          letter: selectedDifficultyConfig.lettersCount,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(builder: (context, setState) {
      return reusableDialog(
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 32),
        context: context,
        child: isLoading
            ? ReusableCircleIndicator()
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: 4,
                  ),
                  Center(
                    child: SizedBox(
                      height: MediaQuery.sizeOf(context).height * .035,
                      child: AutoSizeText(
                        widget.heading,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: CustomThemeText.stymieTextBlack(
                            context: context,
                            fontSize: 32,
                            fontWeight: FontWeight.w900),
                      ),
                    ),
                  ),
                  SizedBox(height: 16),
                  Text(
                    'STATISTICS',
                    style: CustomThemeText.urbanistTextBlack(
                      context: context,
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  SizedBox(
                    height: MediaQuery.sizeOf(context).height * .01,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Flexible(
                        child: Column(
                          children: [
                            SizedBox(
                              height: MediaQuery.sizeOf(context).height * .027,
                              child: AutoSizeText(
                                (statsService.stats.gamesPlayed).toString(),
                                style: CustomThemeText.urbanistTextBlack(
                                  context: context,
                                  fontSize: scoreSize,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                            Text(
                              'Played',
                              style: CustomThemeText.urbanistTextBlack(
                                context: context,
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Flexible(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              height: MediaQuery.sizeOf(context).height * .027,
                              child: AutoSizeText(
                                statsService.winPercentage,
                                style: CustomThemeText.urbanistTextBlack(
                                  context: context,
                                  fontSize: scoreSize,
                                  fontWeight: FontWeight.w700,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Text(
                              'Win %',
                              style: CustomThemeText.urbanistTextBlack(
                                context: context,
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Flexible(
                        child: Column(
                          children: [
                            SizedBox(
                              height: MediaQuery.sizeOf(context).height * .027,
                              child: AutoSizeText(
                                statsService.currentStreak,
                                style: CustomThemeText.urbanistTextBlack(
                                  context: context,
                                  fontSize: scoreSize,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                            Text(
                              'Current Streak',
                              softWrap: true,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: CustomThemeText.urbanistTextBlack(
                                context: context,
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                      Flexible(
                        child: Column(
                          children: [
                            SizedBox(
                              height: MediaQuery.sizeOf(context).height * .027,
                              child: AutoSizeText(
                                statsService.maxStreak,
                                style: CustomThemeText.urbanistTextBlack(
                                  context: context,
                                  fontSize: scoreSize,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                            Text(
                              'Max Streak',
                              softWrap: true,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: CustomThemeText.urbanistTextBlack(
                                context: context,
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: MediaQuery.sizeOf(context).height * .085,
                  ),
                  Stack(
                    clipBehavior: Clip.none,
                    children: [
                      Container(
                        height: MediaQuery.sizeOf(context).height * .13,
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: MediaQuery.sizeOf(context).height * .015,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.smBlue.withOpacity(.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: IntrinsicHeight(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Flexible(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    SizedBox(
                                      height:
                                          MediaQuery.sizeOf(context).height *
                                              .025,
                                      child: AutoSizeText(
                                        "${statsService.stats.bestPoints}pts",
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style:
                                            CustomThemeText.urbanistTextBlack(
                                          context: context,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height:
                                          MediaQuery.sizeOf(context).height *
                                              .0005,
                                    ),
                                    AutoSizeText(
                                      'Best Score',
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: FontFamily.urbanist,
                                        color: CustomTheme.isLightMode(context)
                                            ? Colors.black.withOpacity(.7)
                                            : Colors.white.withOpacity(.7),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: MediaQuery.sizeOf(context).height * .05,
                                child: VerticalDivider(
                                  color: Colors.white.withOpacity(.3),
                                  thickness: 2,
                                ),
                              ),
                              Flexible(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    SizedBox(
                                      height:
                                          MediaQuery.sizeOf(context).height *
                                              .025,
                                      child: AutoSizeText(
                                        "${statsService.stats.averagePoints}pts",
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                        style:
                                            CustomThemeText.urbanistTextBlack(
                                          context: context,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height:
                                          MediaQuery.sizeOf(context).height *
                                              .0005,
                                    ),
                                    AutoSizeText(
                                      'Avg Score',
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: FontFamily.urbanist,
                                        color: CustomTheme.isLightMode(context)
                                            ? Colors.black.withOpacity(.7)
                                            : Colors.white.withOpacity(.7),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: MediaQuery.sizeOf(context).height * .05,
                                child: VerticalDivider(
                                  color: Colors.white.withOpacity(.3),
                                  thickness: 2,
                                ),
                              ),
                              Flexible(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    SizedBox(
                                      height:
                                          MediaQuery.sizeOf(context).height *
                                              .025,
                                      child: AutoSizeText(
                                        statsService
                                            .stats.bestUsedTimeFormatted,
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                        style:
                                            CustomThemeText.urbanistTextBlack(
                                          context: context,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height:
                                          MediaQuery.sizeOf(context).height *
                                              .0005,
                                    ),
                                    AutoSizeText(
                                      'Best time',
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: FontFamily.urbanist,
                                        color: CustomTheme.isLightMode(context)
                                            ? Colors.black.withOpacity(.7)
                                            : Colors.white.withOpacity(.7),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: MediaQuery.sizeOf(context).height * .05,
                                child: VerticalDivider(
                                  color: Colors.white.withOpacity(.3),
                                  thickness: 2,
                                ),
                              ),
                              Flexible(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    SizedBox(
                                      height:
                                          MediaQuery.sizeOf(context).height *
                                              .025,
                                      child: AutoSizeText(
                                        statsService
                                            .stats.averageUsedTimeFormatted,
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                        style:
                                            CustomThemeText.urbanistTextBlack(
                                          context: context,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height:
                                          MediaQuery.sizeOf(context).height *
                                              .0005,
                                    ),
                                    AutoSizeText(
                                      'Avg Time',
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: FontFamily.urbanist,
                                        color: CustomTheme.isLightMode(context)
                                            ? Colors.black.withOpacity(.7)
                                            : Colors.white.withOpacity(.7),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Positioned(
                        top: -55,
                        right: 0,
                        left: 0,
                        child: (statsService.stats.getSpeedGameDetailsModel
                                    ?.totalPoints !=
                                0)
                            ? _currentScoreWidget(
                                totalScore: (statsService
                                            .stats
                                            .getSpeedGameDetailsModel
                                            ?.totalPoints ??
                                        0)
                                    .toString(),
                                linePoints: (statsService
                                            .stats
                                            .getSpeedGameDetailsModel
                                            ?.linePoints ??
                                        0)
                                    .toString(),
                                gussesLength: (statsService
                                            .stats
                                            .getSpeedGameDetailsModel
                                            ?.gussesLength ??
                                        0)
                                    .toString(),
                                secondsPoints: (statsService
                                            .stats
                                            .getSpeedGameDetailsModel
                                            ?.secondsPoints ??
                                        0)
                                    .toString(),
                                usedSeconds: (statsService
                                            .stats
                                            .getSpeedGameDetailsModel
                                            ?.usedSeconds ??
                                        0)
                                    .toString(),
                              )
                            : _noCurrentScoreWidget(),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: MediaQuery.sizeOf(context).height * .0025,
                  ),
                  tabBarWidget(context, setState),
                  ...[
                    SizedBox(height: MediaQuery.of(context).size.height * .004),
                    Visibility(
                      visible:
                          !widget.isGameCompleted && !widget.isUnlimitedGame,
                      child: Center(
                        child: Observer(builder: (_) {
                          return Column(
                            children: [
                              Text('NEXT ${env(EnvKey.PLATFORM_APP_NAME)} IN',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: CustomTheme.isLightMode(context)
                                        ? AppColors.disabled
                                        : AppColors.white,
                                    fontFamily: FontFamily.urbanist,
                                  )),
                              SizedBox(
                                height: 4,
                              ),
                              Text(
                                timerStore.timeToNextWord,
                                style: CustomThemeText.urbanistTextBlack(
                                  context: context,
                                  fontSize: 24,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              SizedBox(
                                height: 8,
                              ),
                            ],
                          );
                        }),
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 124,
                          child: ElevatedButton.icon(
                            onPressed: () {
                              StatsImageGenerator.shareStatsData(
                                context: context,
                                stats: statsService.stats,
                                batterThanPercentage: graphPercentage,
                              );
                            },
                            icon: Icon(
                              Icons.share,
                              color: CustomTheme.getBlackIconColor(context),
                              size: 20,
                            ),
                            label: Text(
                              'SHARE',
                              style: CustomThemeText.urbanistTextBlack(
                                context: context,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              side: BorderSide(
                                width: 1.0,
                                color: CustomTheme.getBlackBorderColor(context),
                              ),
                              elevation: 0,
                            ),
                          ),
                        ),
                        SizedBox(width: 8),
                        SizedBox(
                          height: 40,
                          width: 142,
                          child: Material(
                            borderRadius: BorderRadius.circular(24),
                            child: ElevatedButton(
                              onPressed: () {
                                goToNewSpeedGame();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    AppColors.of(context).saleGreen,
                                elevation: 0,
                                padding: EdgeInsets.zero,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    'New Worde'.toUpperCase(),
                                    style: buttonTextStyle(context: context),
                                  ),
                                  SizedBox(
                                    width: 6,
                                  ),
                                  Icon(
                                    Icons.arrow_forward_ios_outlined,
                                    size: 15,
                                    color:
                                        CustomTheme.getWhiteIconColor(context),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
      );
    });
  }

  Container _currentScoreWidget({
    required String totalScore,
    required String linePoints,
    required String gussesLength,
    required String secondsPoints,
    required String usedSeconds,
  }) {
    return Container(
      height: MediaQuery.sizeOf(context).height * .125,
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.smBlue,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: MediaQuery.sizeOf(context).height * .01,
          ),
          Text(
            "Your Score",
            style: TextStyle(
              color: AppColors.white,
              fontSize: 12,
              fontWeight: FontWeight.w700,
              fontFamily: FontFamily.urbanist,
            ),
          ),
          Row(
            children: [
              SizedBox(
                height: MediaQuery.sizeOf(context).height * .08,
                child: AutoSizeText(
                  totalScore,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 46,
                    fontWeight: FontWeight.w700,
                    fontFamily: FontFamily.urbanist,
                  ),
                ),
              ),
              SizedBox(
                width: 14,
              ),
              Expanded(
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: AutoSizeText(
                            "Solved in $gussesLength guesses",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: AppColors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              fontFamily: FontFamily.urbanist,
                            ),
                          ),
                        ),
                        AutoSizeText(
                          " $linePoints pts",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.end,
                          style: TextStyle(
                            color: AppColors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                            fontFamily: FontFamily.urbanist,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: AutoSizeText(
                            "Solved in $usedSeconds sec",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                            style: TextStyle(
                              color: AppColors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              fontFamily: FontFamily.urbanist,
                            ),
                          ),
                        ),
                        AutoSizeText(
                          " $secondsPoints pts",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: AppColors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                            fontFamily: FontFamily.urbanist,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Container _noCurrentScoreWidget() {
    return Container(
      height: MediaQuery.sizeOf(context).height * .12,
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: CustomTheme.getWhiteIconColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: CustomTheme.isLightMode(context)
              ? AppColors.darkGrey
              : AppColors.white.withOpacity(.6),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    height: MediaQuery.sizeOf(context).height * .01,
                  ),
                  Text(
                    "Your Score",
                    style: TextStyle(
                      color: CustomTheme.isLightMode(context)
                          ? AppColors.black.withOpacity(.6)
                          : AppColors.white.withOpacity(.6),
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                      fontFamily: FontFamily.urbanist,
                    ),
                  ),
                  SizedBox(
                    height: MediaQuery.sizeOf(context).height * .07,
                    child: AutoSizeText(
                      "0",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: CustomThemeText.urbanistTextBlack(
                        fontSize: 46,
                        fontWeight: FontWeight.w700,
                        context: context,
                      ),
                    ),
                  ),
                ],
              ),
              Spacer(),
              Column(
                children: [
                  Row(
                    children: [
                      Visibility(
                        visible: widget.submittedRows.length !=
                            selectedDifficultyConfig.rowCount,
                        child: Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: SvgPicture.asset(
                            AppImages.watchBlueGreyIcon,
                            height: MediaQuery.sizeOf(context).height * .03,
                          ),
                        ),
                      ),
                      SizedBox(
                        height: MediaQuery.sizeOf(context).height * .03,
                        child: AutoSizeText(
                          widget.submittedRows.length ==
                                  selectedDifficultyConfig.rowCount
                              ? "Out of Tries!"
                              : "Time’s up",
                          style: TextStyle(
                            color: CustomTheme.isLightMode(context)
                                ? AppColors.warmGray
                                : AppColors.blueGrey,
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                            fontFamily: FontFamily.urbanist,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 4,
                  ),
                  AutoSizeText(
                    widget.submittedRows.length ==
                            selectedDifficultyConfig.rowCount
                        ? "You've used up all your\nguesses. Thanks for playing!"
                        : "You must solve the puzzle\nwithin the given time",
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    style: CustomThemeText.urbanistTextBlack(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      context: context,
                      height: 1.25,
                    ),
                  )
                ],
              ),
              SizedBox(
                width: 16,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Column tabBarWidget(BuildContext context, StateSetter setState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TabBar(
          dividerColor: AppColors.filledGrey,
          isScrollable: true,
          physics: NeverScrollableScrollPhysics(),
          labelPadding: EdgeInsets.only(right: 16),
          tabAlignment: TabAlignment.start,
          dividerHeight: 1,
          indicatorWeight: 4,
          indicatorSize: TabBarIndicatorSize.label,
          indicatorPadding: EdgeInsets.zero,
          controller: tabController,
          padding: EdgeInsets.zero,
          unselectedLabelColor:
              CustomTheme.getBlackIconColor(context).withOpacity(.55),
          labelStyle: CustomThemeText.urbanistTextBlack(
            context: context,
            fontSize: 15,
            fontWeight: FontWeight.w700,
          ),
          unselectedLabelStyle: CustomThemeText.urbanistTextBlack(
            context: context,
            fontSize: 15,
            fontWeight: FontWeight.w700,
          ),
          tabs: [
            Tab(
              text: 'Game Summary',
            ),
            Tab(
              text: 'Guess Distribution',
            ),
          ],
        ),
        SizedBox(
          height: MediaQuery.of(context).size.height * .225 +
              (betterThanPercentage > 0 ? 75 : 50),
          child: Column(
            children: [
              Flexible(
                child: TabBarView(
                  controller: tabController,
                  children: [
                    Column(
                      children: [
                        SizedBox(
                            height: MediaQuery.of(context).size.height * .01),
                        Text.rich(
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          TextSpan(
                              children: betterThanPercentage > 0
                                  ? [
                                      TextSpan(
                                        text: 'Well done! You did better than ',
                                        style:
                                            CustomThemeText.urbanistTextBlack(
                                          context: context,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      TextSpan(
                                        text:
                                            '${betterThanPercentage.toStringAsFixed(0)}%',
                                        style: TextStyle(
                                          color: AppColors.green,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w800,
                                          fontFamily: FontFamily.urbanist,
                                        ),
                                      ),
                                      TextSpan(
                                        text: widget.isUnlimitedGame
                                            ? " of your previous games!"
                                            : ' of all players! ',
                                        style:
                                            CustomThemeText.urbanistTextBlack(
                                          context: context,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      TextSpan(
                                        text: widget.isUnlimitedGame
                                            ? 'This is how you compare to your previous games:'
                                            : 'This is how you compare to others playing with this word:',
                                        style:
                                            CustomThemeText.urbanistTextBlack(
                                          context: context,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ]
                                  : [
                                      TextSpan(
                                        text:
                                            'This is how you compare to others playing with this word:',
                                        style:
                                            CustomThemeText.urbanistTextBlack(
                                          context: context,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ]),
                        ),
                        GameSummarySpeedGraph(
                          data: statsService
                                  .stats.getSpeedGameSummaryModel?.chartData ??
                              [],
                          userPercentile: graphPercentage.round(),
                          maxValue: statsService
                                  .stats.getSpeedGameSummaryModel?.maxPoints ??
                              0,
                          rangeInterval: 1,
                          height:
                              MediaQuery.of(context).size.height * .225 - 30,
                          userPoints: statsService.stats
                                  .getSpeedGameDetailsModel?.totalPoints ??
                              0,
                        ),
                      ],
                    ),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          height: MediaQuery.of(context).size.height * .25,
                          child: charts.BarChart(
                            createGuessDistributionData(),
                            vertical: false,
                            barRendererDecorator:
                                charts.BarLabelDecorator<String>(
                              insideLabelStyleSpec: charts.TextStyleSpec(
                                fontSize: 12,
                                color: charts.MaterialPalette.black,
                              ),
                              outsideLabelStyleSpec: charts.TextStyleSpec(
                                fontSize: 12,
                                color: charts.MaterialPalette.black,
                              ),
                              labelPosition: charts.BarLabelPosition.auto,
                              labelAnchor: charts.BarLabelAnchor.end,
                            ),
                            primaryMeasureAxis: charts.NumericAxisSpec(
                              renderSpec: charts.NoneRenderSpec(),
                            ),
                            domainAxis: charts.OrdinalAxisSpec(
                              renderSpec: charts.SmallTickRendererSpec(
                                labelStyle: charts.TextStyleSpec(
                                  fontSize: 15,
                                  fontWeight: "BOLD",
                                  fontFamily: FontFamily.urbanist,
                                  color: getYAxisColor(),
                                ),
                                lineStyle: charts.LineStyleSpec(
                                  color: charts.ColorUtil.fromDartColor(
                                      AppColors.nonActiveGrey),
                                  thickness: 1,
                                ),
                              ),
                            ),
                          ),
                        ),
                        avgScoreBox(context),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Row avgScoreBox(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          margin: EdgeInsets.only(left: 8),
          decoration: BoxDecoration(
            color: AppColors.filledGrey,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Avg.Score',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                  fontFamily: FontFamily.urbanist,
                ),
              ),
              SizedBox(
                width: 4,
              ),
              Text(
                avg.toStringAsFixed(2),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                  fontFamily: FontFamily.urbanist,
                ),
              ),
            ],
          ),
        ),
        TextButton(
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => ConfirmDialog(
                title: 'Reset Stats',
                body:
                    '''Are you sure you want to reset your stats?\nYou  will lose your streak for this difficulty daily mode, and start from zero.''',
                onConfirm: () async {
                  await resetStats();
                },
              ),
            );
          },
          style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 10),
              minimumSize: Size(50, 0),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              alignment: Alignment.centerLeft),
          child: Text(
            'RESET STATS',
            style: CustomThemeText.urbanistTextBlack(
              context: context,
              fontSize: 12,
              fontWeight: FontWeight.w500,
              textDecoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }
}
