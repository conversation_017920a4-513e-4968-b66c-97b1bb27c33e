import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wordle/src/core/constants/app_colors.dart';

class LoadingDialog extends StatelessWidget {
  const LoadingDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(statusBarColor: Colors.transparent));
    return Dialog(
      // The background color
      backgroundColor: AppColors.of(context).colorTone7,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: const [
            // The loading indicator
            CircularProgressIndicator(),
            SizedBox(
              height: 15,
            ),
            // Some text
            Text('Syncing...')
          ],
        ),
      ),
    );
  }
}
