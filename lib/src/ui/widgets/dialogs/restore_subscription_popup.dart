import 'package:flutter/material.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';
import 'package:wordle/src/ui/widgets/reusable_dialog.dart';

import '../../../core/constants/app_colors.dart';
import '../../../core/utils/local_storage.dart';
import '../../../core/utils/service_locator.dart';

class RestoreSubscriptionPopup extends StatefulWidget {
  const RestoreSubscriptionPopup({
    Key? key,
    required this.context,
    required this.email,
    required this.loginProvider,
  }) : super(key: key);

  final BuildContext context;
  final String email;
  final String loginProvider;

  @override
  State<RestoreSubscriptionPopup> createState() =>
      _RestoreSubscriptionPopupState();

  static Future<void> show({
    required BuildContext context,
    required String email,
    required String loginProvider,
  }) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return RestoreSubscriptionPopup(
          context: context,
          email: email,
          loginProvider: loginProvider,
        );
      },
    ).then((value) {
      final _localStorage = ServiceLocator.locate<LocalStorage>();
      _localStorage.saveBool(LSKey.shouldShowTransferIapPopup, false);
    });
  }
}

class _RestoreSubscriptionPopupState extends State<RestoreSubscriptionPopup> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(builder: (context, setState) {
      return reusableDialog(
        showCancelButton: true,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 32),
        context: context,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "Alert!",
              style: CustomThemeText.urbanistTextBlack(
                context: context,
                fontSize: 32,
                fontWeight: FontWeight.w900,
              ),
            ),
            const SizedBox(
              height: 24,
            ),
            Text(
              widget.email == ""
                  ? "We have detected previous subscription in your phone. Please login with any social account recover it."
                  : "We have detected an active subscription associated with ${widget.email}(${widget.loginProvider}). Please log in with that account to recover your it.",
              textAlign: TextAlign.center,
              style: CustomThemeText.urbanistTextBlack(
                context: context,
                height: 1.15,
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(
              height: 28,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                MaterialButton(
                  onPressed: () {
                    AppRouter.pop();
                  },
                  padding: const EdgeInsets.symmetric(
                    horizontal: 34,
                  ),
                  color: AppColors.green,
                  elevation: 0,
                  height: 42,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(23.0),
                  ),
                  child: Text(
                    'Ok'.toUpperCase(),
                    style: CustomThemeText.urbanistTextWhite(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      context: context,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }
}
