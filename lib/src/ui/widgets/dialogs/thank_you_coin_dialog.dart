import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lottie/lottie.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/lottie_animations.dart';
import 'package:wordle/src/router/app_router.dart';

///Information dialogue, that accepts a title, body and a call to action
class ThankYouCoinDialog extends StatefulWidget {
  const ThankYouCoinDialog({
    Key? key,
  }) : super(key: key);

  @override
  _ThankYouCoinDialogState createState() => _ThankYouCoinDialogState();
}

class _ThankYouCoinDialogState extends State<ThankYouCoinDialog> {
  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(statusBarColor: Colors.transparent));
    return AlertDialog(
      backgroundColor: AppColors.of(context).colorTone7,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(5.0),
      ),
      title: Text(
        'Thank You',
        style: const TextStyle(fontWeight: FontWeight.w600),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      content: SingleChildScrollView(
        child: Lottie.asset(
          LottieAnimations.coinThrow,
          fit: BoxFit.contain,
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            AppRouter.pop();
          },
          child: Text('CLOSE'),
        )
      ],
    );
  }
}
