import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/router/app_router.dart';

///Information dialogue, that accepts a title, body and a call to action
class InfoDialog extends StatefulWidget {
  final String title;
  final String body;
  final void Function()? onClick;

  const InfoDialog({
    Key? key,
    required this.title,
    required this.body,
    this.onClick,
  }) : super(key: key);

  @override
  _InfoDialogState createState() => _InfoDialogState();
}

class _InfoDialogState extends State<InfoDialog> {
  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(statusBarColor: Colors.transparent));
    return AlertDialog(
      backgroundColor: AppColors.of(context).colorTone7,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(5.0),
      ),
      title: Text(
        widget.title,
        style: const TextStyle(fontWeight: FontWeight.w600),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      content: SingleChildScrollView(child: Text(widget.body)),
      actions: [
        TextButton(
          onPressed: widget.onClick ??
              () {
                AppRouter.pop();
              },
          child: Text('OK'),
        )
      ],
    );
  }
}
