import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/documents.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/router/app_router.dart';

class TermsDialog extends StatefulWidget {
  const TermsDialog({
    Key? key,
  }) : super(key: key);

  @override
  _TermsDialogState createState() => _TermsDialogState();
}

class _TermsDialogState extends State<TermsDialog> {
  final _localStorage = ServiceLocator.locate<LocalStorage>();

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(statusBarColor: Colors.transparent));
    return AlertDialog(
      scrollable: true,
      backgroundColor: AppColors.of(context).colorTone7,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(5.0),
      ),
      title: Text(
        'TERMS AND CONDITIONS',
        style: const TextStyle(fontWeight: FontWeight.w600),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      content: FutureBuilder<String>(
        future: rootBundle.loadString(Documents.termsAcceptMessage),
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            return MarkdownBody(
              data: snapshot.data ?? '',
              onTapLink: (_, link, __) {
                if (link != null) {
                  launchUrl(Uri.parse(link));
                }
              },
            );
          }

          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator.adaptive());
          }

          return const SizedBox.shrink();
        },
      ),
      actions: [
        TextButton(
          onPressed: () {
            _localStorage.saveBool(LSKey.isTermsAccepted, true);
            AppRouter.pop();
          },
          child: Text('ACCEPT'),
        )
      ],
    );
  }
}
