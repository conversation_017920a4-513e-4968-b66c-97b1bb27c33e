import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/data/models/difficulty_config.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/ui/widgets/buttons/difficulty_button.dart';

class DifficultyDialog extends StatelessWidget {
  const DifficultyDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppColors.of(context).colorTone7,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(5.0),
      ),
      title: const Text(
        'DIFFICULTY',
        style: TextStyle(fontWeight: FontWeight.w600),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Column(
            children: [
              DifficultyButton(
                title: "CLASSIC",
                subtitle: "5 Letters, 6 tries",
                color: AppColors.of(context).correct,
                onPressed: () {
                  AppRouter.pop(DifficultyConfig(lettersCount: 5, rowCount: 6));
                },
              ),
              SizedBox(height: 10),
              DifficultyButton(
                title: "INTERMEDIATE",
                subtitle: "6 Letters, 6 tries",
                color: AppColors.of(context).present,
                onPressed: () {
                  AppRouter.pop(DifficultyConfig(lettersCount: 6, rowCount: 6));
                },
              ),
              SizedBox(height: 10),
              DifficultyButton(
                title: "HARD",
                subtitle: "7 Letters, 7 tries",
                color: AppColors.of(context).badRed,
                onPressed: () {
                  AppRouter.pop(DifficultyConfig(lettersCount: 7, rowCount: 7));
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              TextButton(
                onPressed: () {
                  AppRouter.pop();
                },
                child: const Text(
                  'CANCEL',
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }
}
