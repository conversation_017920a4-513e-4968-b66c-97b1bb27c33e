import 'package:flutter/material.dart';

class NativeAdContainer extends StatelessWidget {
  final Widget child;

  /// 90 recommended for small ad type, 320 for medium
  final double minHeight;

  /// 200 recommended for small ad type, 400 for medium
  final double maxHeight;

  const NativeAdContainer({
    Key? key,
    required this.child,
    this.minHeight = 90,
    this.maxHeight = 200,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        minWidth: 320, // minimum recommended width
        minHeight: minHeight, // minimum recommended height
        maxWidth: 400,
        maxHeight: maxHeight,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        child: Si<PERSON><PERSON><PERSON>(
          child: child,
        ),
      ),
    );
  }
}
