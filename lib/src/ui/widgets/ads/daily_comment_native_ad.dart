import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:wordle/src/core/helpers/app_admob.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/repositories/ad_repository.dart';
import 'package:wordle/src/ui/widgets/ads/native_ad_container.dart';

class DailyCommentNativeAd extends StatefulWidget {
  const DailyCommentNativeAd({Key? key}) : super(key: key);

  @override
  State<DailyCommentNativeAd> createState() => _DailyCommentNativeAdState();
}

class _DailyCommentNativeAdState
    extends State<DailyCommentNativeAd> /*with AutomaticKeepAliveClientMixin*/ {
  final _adRepo = ServiceLocator.locate<AdRepository>();

  @override
  Widget build(BuildContext context) {
    // super.build(context);
    return FutureBuilder<NativeAd>(
      future: _adRepo.loadAndShowNativeAd(
        AdNativeUnitId.dailyCommentNative,
        context: context,
        templateType: TemplateType.medium,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done &&
            snapshot.data != null) {
          return NativeAdContainer(
            minHeight: 320,
            maxHeight: 400,
            child: AdWidget(ad: snapshot.data!),
          );
        } else {
          return SizedBox.shrink();
        }
      },
    );
  }

  // @override
  // bool get wantKeepAlive => true;
}
