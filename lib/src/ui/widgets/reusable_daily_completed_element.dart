import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:wordle/src/ui/widgets/reusable_check_icon.dart';

import '../../core/constants/font_family.dart';

class ReusableDailyCompletedElement extends StatelessWidget {
  final int letterCount;
  final VoidCallback onTap;

  const ReusableDailyCompletedElement({
    super.key,
    required this.letterCount,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 10,
            vertical: 14,
          ),
          decoration: BoxDecoration(
            color: Color(0xffA5491A).withOpacity(.7),
            borderRadius: BorderRadius.circular(24),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ReusableCheckIcon(),
              Expanded(
                child: AutoSizeText(
                  "$letterCount letter",
                  maxLines: 1,
                  maxFontSize: 15,
                  minFontSize: 8,
                  style: TextStyle(
                    color: Colors.white,
                    fontFamily: FontFamily.urbanist,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
