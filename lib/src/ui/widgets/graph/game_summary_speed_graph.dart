import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;
import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/font_family.dart';
import 'package:wordle/src/ui/theme/custom_theme.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';

import '../../../model/get_speed_game_summary_model.dart';

class GameSummarySpeedGraph extends StatelessWidget {
  final List<ChartData> data;
  final int userPercentile;
  final int userPoints;
  final int maxValue;
  final double height;
  final int rangeInterval;

  GameSummarySpeedGraph({
    Key? key,
    required this.data,
    required this.userPercentile,
    required this.maxValue,
    required this.height,
    this.rangeInterval = 10,
    required this.userPoints,
  }) : super(key: key);

  int yAxisMax = 0;

  @override
  Widget build(BuildContext context) {
    double graphWidth = MediaQuery.of(context).size.width - 70;
    double userWidth = graphWidth * (userPercentile / 100) - 55;
    double yourPercentagePosition = userWidth.clamp(0, graphWidth - 110);

    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      children: [
        SizedBox(
          height: height,
          width: graphWidth,
          child: charts.BarChart(
            _createSeriesList(),
            animate: false,
            domainAxis: charts.OrdinalAxisSpec(
              viewport:
                  charts.OrdinalViewport('0', (maxValue ~/ rangeInterval) + 1),
              showAxisLine: true,
              renderSpec: charts.SmallTickRendererSpec(
                labelStyle: charts.TextStyleSpec(fontSize: 10),
                tickLengthPx: 0,
                lineStyle: charts.LineStyleSpec(
                  color:
                      charts.ColorUtil.fromDartColor(AppColors.grayChartLine),
                ),
              ),
              tickProviderSpec: charts.StaticOrdinalTickProviderSpec([
                charts.TickSpec(
                  '0',
                  style: charts.TextStyleSpec(
                    color: CustomTheme.isLightMode(context)
                        ? charts.ColorUtil.fromDartColor(AppColors.darkGrey)
                        : charts.ColorUtil.fromDartColor(AppColors.white),
                    fontWeight: "W500",
                    fontSize: 12,
                    fontFamily: FontFamily.urbanist,
                  ),
                ),
                charts.TickSpec(
                  ((maxValue ~/ 10) - 1).toString(),
                  label: (maxValue ~/ rangeInterval).toString(),
                  style: charts.TextStyleSpec(
                    color: CustomTheme.isLightMode(context)
                        ? charts.ColorUtil.fromDartColor(AppColors.darkGrey)
                        : charts.ColorUtil.fromDartColor(AppColors.white),
                    fontWeight: "W500",
                    fontSize: 12,
                    fontFamily: FontFamily.urbanist,
                  ),
                ),
              ]),
            ),
            primaryMeasureAxis: charts.NumericAxisSpec(
              viewport: charts.NumericExtents(0, yAxisMax + .5),
              renderSpec: charts.GridlineRendererSpec(
                labelStyle: charts.TextStyleSpec(
                  color: charts.MaterialPalette.transparent,
                ),
                lineStyle: charts.LineStyleSpec(
                  color: charts.MaterialPalette.transparent,
                ),
              ),
            ),
            defaultRenderer: charts.BarRendererConfig(
              groupingType: charts.BarGroupingType.stacked,
              cornerStrategy: const charts.ConstCornerStrategy(0),
            ),
            layoutConfig: charts.LayoutConfig(
              leftMarginSpec: charts.MarginSpec.fixedPixel(4),
              topMarginSpec: charts.MarginSpec.fixedPixel(0),
              rightMarginSpec: charts.MarginSpec.fixedPixel(8),
              bottomMarginSpec: charts.MarginSpec.fixedPixel(20),
            ),
          ),
        ),
        Positioned(
          bottom: -28,
          left: yourPercentagePosition,
          child: Container(
            width: 110,
            padding: EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: CustomTheme.getBlackBorderColor(context),
              ),
            ),
            child: Center(
              child: Text(
                'You: $userPercentile percentile',
                style: CustomThemeText.urbanistTextBlack(
                  context: context,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  List<charts.Series<ChartData, String>> _createSeriesList() {
    data.sort(
      (a, b) => int.parse(a.group).compareTo(
        int.parse(b.group),
      ),
    );
    data.sort((a, b) {
      yAxisMax = yAxisMax < a.count ? a.count : yAxisMax;
      yAxisMax = yAxisMax < b.count ? b.count : yAxisMax;
      return int.parse(a.group).compareTo(
        int.parse(b.group),
      );
    });

    return [
      charts.Series<ChartData, String>(
        id: 'Percentile',
        colorFn: (ChartData data, _) =>
            int.parse(data.group) == userPoints ~/ (rangeInterval * 10)
                ? charts.ColorUtil.fromDartColor(AppColors.saleGreen1)
                : charts.ColorUtil.fromDartColor(AppColors.grayChartLine),
        domainFn: (ChartData data, _) =>
            (int.parse(data.group) * rangeInterval).toString(),
        measureFn: (ChartData data, _) => data.count,
        data: data,
      )
    ];
  }
}
