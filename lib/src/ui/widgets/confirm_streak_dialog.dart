import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wordle/src/core/strings/app_string.dart';
import 'package:wordle/src/ui/widgets/reusable_dialog.dart';

import '../../core/constants/app_colors.dart';
import '../../core/constants/app_images.dart';
import '../../core/constants/game_result.dart';
import '../../core/utils/service_locator.dart';
import '../../domain/unlimited_challenge/stores/unlimited_challenge_store.dart';
import '../../router/app_router.dart';
import '../theme/app_theme.dart';
import '../theme/custom_theme.dart';
import '../theme/custom_theme_text.dart';

class ConfirmStreakDialog extends StatelessWidget {
  ConfirmStreakDialog(
      {super.key, required this.streakCount, required this.parentContext});

  final int streakCount;
  final unlimitedChallengeStore =
      ServiceLocator.locate<UnlimitedChallengeStore>();
  final BuildContext parentContext;

  @override
  Widget build(BuildContext context) {
    return reusableDialog(
      context: context,
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 32),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 8,
          ),
          AutoSizeText(
            AppStrings.areYourSureText,
            maxLines: 1,
            style: CustomThemeText.stymieTextBlack(
              context: context,
              fontSize: 32,
              fontWeight: FontWeight.w900,
            ),
          ),
          SizedBox(
            height: 20,
          ),
          Text(
            AppStrings.youWillLoseYourText +
                "\n" +
                streakCount.toString() +
                " " +
                AppStrings.gameWinStreakText,
            textAlign: TextAlign.center,
            style: CustomThemeText.urbanistTextBlack(
              context: context,
              fontSize: 20,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(
            height: 48,
          ),
          SizedBox(
            height: 42,
            width: 174,
            child: ElevatedButton(
              onPressed: () {
                unlimitedChallengeStore.endGame(
                  canPop: true,
                  finalGameResult: GameResult.lost,
                );
                AppRouter.pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.of(context).saleGreen,
                padding: EdgeInsets.only(left: 20, right: 12),
                elevation: 0,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    AppStrings.giveUpAndNextText.toUpperCase(),
                    style: buttonTextStyle(context: context),
                  ),
                  SizedBox(
                    width: 6,
                  ),
                  Icon(
                    Icons.arrow_forward_ios_outlined,
                    size: 15,
                    color: CustomTheme.getWhiteIconColor(context),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            height: 12,
          ),
          SizedBox(
            height: 42,
            width: 174,
            child: ElevatedButton(
              onPressed: () {
                unlimitedChallengeStore.onKeepItStreakClick(
                    context: parentContext);
                AppRouter.pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.of(context).saleGreen,
                padding: EdgeInsets.only(left: 20, right: 12),
                elevation: 0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Visibility(
                    visible: unlimitedChallengeStore.canShowAd,
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          AppImages.unlockAdIcon,
                          height: 30,
                        ),
                        SizedBox(
                          width: 8,
                        ),
                      ],
                    ),
                  ),
                  Text(
                    AppStrings.keepItText.toUpperCase(),
                    style: buttonTextStyle(context: context),
                  ),
                  Visibility(
                    visible: unlimitedChallengeStore.canShowAd,
                    child: SizedBox(
                      width: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
