import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_gradients.dart';
import 'package:wordle/src/core/constants/app_images.dart';

class DailyEntranceOption extends StatelessWidget {
  final VoidCallback? onPress;
  const DailyEntranceOption({Key? key, this.onPress}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPress,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: AppGradients.of(context).grownEarly,
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            width: 7,
            color: Color(0xFF4DFF94),
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AutoSizeText(
                    'Daily',
                    style: TextStyle(
                      fontSize: 45,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    maxFontSize: 45,
                    textScaleFactor: 1,
                  ),
                  AutoSizeText(
                    'Solve a new puzzle every day, challenge yourself.',
                    style: TextStyle(color: Colors.white),
                    maxFontSize: 16,
                    textScaleFactor: 1,
                  )
                ],
              ),
            ),
            SizedBox(width: 5),
            Image.asset(
              AppImages.calendarGlossy,
              height: 70,
            )
          ],
        ),
      ),
    );
  }
}
