import 'package:flutter/material.dart';

class PersistentBadge extends StatelessWidget {
  final Widget child;
  final double? positionTop;
  final double? positionBottom;
  final double? positionRight;
  final double? positionLeft;
  final double? badgeSize;
  final bool isShowing;

  const PersistentBadge({
    Key? key,
    required this.child,
    this.positionBottom,
    this.positionLeft,
    this.positionRight,
    this.positionTop,
    this.badgeSize,
    this.isShowing = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!isShowing) return child;
    return Stack(children: [
      child,
      Positioned(
        // draw a red marble
        top: positionTop,
        right: positionRight,
        bottom: positionBottom,
        left: positionLeft,
        child:
            Icon(Icons.brightness_1, size: badgeSize, color: Colors.redAccent),
      )
    ]);
  }
}
