import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wordle/src/core/constants/app_images.dart';

class TimerController extends ChangeNotifier {
  AnimationController? _controller;
  bool _isRunning = false;
  Duration _duration;
  Duration _usedTime;

  TimerController({
    required Duration duration,
    int usedSeconds = 0,
  })  : _duration = duration,
        _usedTime = Duration(seconds: usedSeconds);

  void setAnimationController(AnimationController controller) {
    _controller = controller;
    _controller!.duration = _duration;
    _controller!.value =
        (_duration - _usedTime).inMilliseconds / _duration.inMilliseconds;
  }

  void start() {
    if (_controller != null && !_isRunning) {
      _controller!.reverse(from: _controller!.value);
      _isRunning = true;
      notifyListeners();
    }
  }

  void pause() {
    if (_controller != null && _isRunning) {
      _controller!.stop();
      _isRunning = false;
      notifyListeners();
    }
  }

  void resume() {
    if (_controller != null && !_isRunning) {
      _controller!.reverse(from: _controller!.value);
      _isRunning = true;
      notifyListeners();
    }
  }

  void stop() {
    if (_controller != null) {
      _controller!.reset();
      _isRunning = false;
      notifyListeners();
    }
  }

  void updateDuration(Duration newDuration) {
    _duration = newDuration;
    if (_controller != null) {
      _controller!.duration = newDuration;
      _controller!.value =
          (_duration - _usedTime).inMilliseconds / newDuration.inMilliseconds;
    }
    notifyListeners();
  }

  void updateUsedSeconds(int newUsedSeconds) {
    _usedTime = Duration(seconds: newUsedSeconds);
    if (_controller != null) {
      _controller!.value =
          (_duration - _usedTime).inMilliseconds / _duration.inMilliseconds;
    }
    notifyListeners();
  }

  bool get isRunning => _isRunning;

  Duration get remainingTime {
    if (_controller != null) {
      return _controller!.duration! * _controller!.value;
    }
    return Duration.zero;
  }

  Duration get usedTime {
    return _duration - remainingTime;
  }
}

class ReusableTimerBar extends StatefulWidget {
  final TimerController controller;
  final VoidCallback? onTimerEnd;

  const ReusableTimerBar({
    Key? key,
    required this.controller,
    this.onTimerEnd,
  }) : super(key: key);

  @override
  _ReusableTimerBarState createState() => _ReusableTimerBarState();
}

class _ReusableTimerBarState extends State<ReusableTimerBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.controller._duration,
    );

    _controller.addStatusListener(_handleAnimationStatus);

    widget.controller.setAnimationController(_controller);
    widget.controller.start();
    widget.controller.pause();
    widget.controller.addListener(_handleControllerChange);
  }

  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.dismissed) {
      widget.onTimerEnd?.call();
    }
  }

  void _handleControllerChange() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return CustomPaint(
              painter: ColorfulBarPainter(_controller.value),
              child: Container(height: 24),
            );
          },
        ),
        Positioned(
          right: 12,
          top: 0,
          bottom: 3,
          child: AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              final remainingTime = widget.controller.remainingTime;
              final minutes =
                  (remainingTime.inMinutes).toString().padLeft(2, '0');
              final seconds =
                  (remainingTime.inSeconds % 60).toString().padLeft(2, '0');

              return Center(
                child: Text(
                  remainingTime.inSeconds == 0
                      ? "Time’s up!"
                      : '$minutes:$seconds',
                  style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w700,
                    fontSize: 18,
                  ),
                ),
              );
            },
          ),
        ),
        Positioned(
          left: -13,
          bottom: -5,
          child: SvgPicture.asset(
            AppImages.watchSpeedIcon,
            height: 32,
            width: 32,
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    widget.controller.removeListener(_handleControllerChange);
    _controller.removeStatusListener(_handleAnimationStatus);
    _controller.dispose();
    super.dispose();
  }
}

class ColorfulBarPainter extends CustomPainter {
  final double progress;

  ColorfulBarPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    const padding = 4.0;

    final greyPaint = Paint()
      ..color = Colors.grey.shade100
      ..style = PaintingStyle.fill;

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        Radius.circular(size.height / 2),
      ),
      greyPaint,
    );

    // Create the gradient paint
    final gradientPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
        colors: [
          Color(0xFFFF0000),
          Color(0xFFFF7A00),
          Color(0xFFFFC700),
          Color(0xCC00FFE0),
        ],
        stops: [0.0, 0.145, 0.53, 1.0],
      ).createShader(Rect.fromLTWH(padding, padding, size.width - 2 * padding,
          size.height - 2 * padding));

    final overlayPaint = Paint()
      ..color = Colors.white.withOpacity(0.2)
      ..style = PaintingStyle.fill;

    final gradientRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(padding, padding, (size.width - 2 * padding) * progress,
          size.height - 2 * padding),
      Radius.circular((size.height - 2 * padding) / 2),
    );

    canvas.drawRRect(gradientRect, gradientPaint);

    canvas.drawRRect(gradientRect, overlayPaint);
  }

  @override
  bool shouldRepaint(ColorfulBarPainter oldDelegate) =>
      progress != oldDelegate.progress;
}
