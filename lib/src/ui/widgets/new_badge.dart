import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class NewBadge extends StatelessWidget {
  final Widget child;
  final double? positionTop;
  final double? positionBottom;
  final double? positionRight;
  final double? positionLeft;
  final double? badgeSize;
  final bool isShowing;

  const NewBadge({
    Key? key,
    required this.child,
    this.positionBottom,
    this.positionLeft,
    this.positionRight,
    this.positionTop,
    this.badgeSize,
    this.isShowing = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!isShowing) return child;
    return Stack(children: [
      child,
      Positioned(
        top: positionTop,
        right: positionRight,
        bottom: positionBottom,
        left: positionLeft,
        child: Container(
          padding: EdgeInsets.all(3),
          decoration: BoxDecoration(
              color: Colors.redAccent, borderRadius: BorderRadius.circular(5)),
          child: Shimmer.fromColors(
            baseColor: Colors.redAccent.shade700,
            highlightColor: Colors.white,
            child: Text(
              'NEW',
              style: TextStyle(
                fontSize: 10,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      )
    ]);
  }
}
