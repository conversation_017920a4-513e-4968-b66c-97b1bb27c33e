import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class SubscriptionLoader extends StatelessWidget {
  const SubscriptionLoader({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      child: Container(
        width: double.infinity,
        height: 200,
        decoration: BoxDecoration(
          color: Colors.grey,
          borderRadius: BorderRadius.circular(10),
        ),
      ),
      baseColor: Colors.grey.shade200,
      highlightColor: Colors.grey.shade400,
    );
  }
}
