import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class TopUserLoader extends StatelessWidget {
  final double size;
  const TopUserLoader({Key? key, required this.size}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      child: CircleAvatar(
        radius: size + 4,
        backgroundColor: Colors.grey,
      ),
      baseColor: Colors.grey.shade200,
      highlightColor: Colors.grey.shade400,
    );
  }
}
