import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class CommentLoader extends StatelessWidget {
  const CommentLoader({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      child: ListTile(
        leading: CircleAvatar(),
        title: SizedBox(
          child: Container(height: 10, width: 50, color: Colors.grey),
        ),
        subtitle: Container(height: 10, width: 200, color: Colors.grey),
      ),
      baseColor: Colors.grey.shade200,
      highlightColor: Colors.grey.shade400,
    );
  }
}
