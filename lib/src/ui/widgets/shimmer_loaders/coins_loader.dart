import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class CoinsLoader extends StatelessWidget {
  const CoinsLoader({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      shrinkWrap: true,
      childAspectRatio: 1 / 1.4,
      crossAxisCount: 3,
      crossAxisSpacing: 10,
      mainAxisSpacing: 10,
      children: List.generate(
        7,
        (index) => Shimmer.fromColors(
          child: Container(
            decoration: BoxDecoration(
              color: Colors.grey,
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          baseColor: Colors.grey.shade200,
          highlightColor: Colors.grey.shade400,
        ),
      ),
    );
  }
}
