import 'package:auto_size_text/auto_size_text.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';

import '../../core/constants/app_colors.dart';
import '../../core/constants/font_family.dart';

class ReusableDailyInProgressElement extends StatelessWidget {
  final int letterCount;
  final VoidCallback onTap;

  const ReusableDailyInProgressElement({
    super.key,
    required this.letterCount,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 2),
          child: DottedBorder(
            color: AppColors.brightYellow,
            strokeWidth: 3.5,
            radius: Radius.circular(24),
            borderType: BorderType.RRect,
            dashPattern: [3, 2],
            stackFit: StackFit.passthrough,
            padding: EdgeInsets.zero,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 10,
                vertical: 12,
              ),
              decoration: BoxDecoration(
                color: AppColors.realOrange,
                borderRadius: BorderRadius.circular(24),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    margin: EdgeInsets.only(right: 5),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      shape: BoxShape.circle,
                    ),
                    padding: EdgeInsets.all(6),
                    child: Container(
                      width: 5,
                      height: 5,
                      decoration: BoxDecoration(
                        color: AppColors.realOrange,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                  Expanded(
                    child: AutoSizeText(
                      "$letterCount letter",
                      maxLines: 1,
                      maxFontSize: 15,
                      minFontSize: 8,
                      style: TextStyle(
                        color: Colors.white,
                        fontFamily: FontFamily.urbanist,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
