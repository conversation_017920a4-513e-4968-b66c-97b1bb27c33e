import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/app_gradients.dart';
import 'package:wordle/src/ui/widgets/buttons/filled_button.dart' as ui;

class CoinsCard extends StatelessWidget {
  final String label;
  final String price;
  final String imagePath;
  final VoidCallback? onPurchase;
  final bool isFree;
  const CoinsCard({
    Key? key,
    required this.label,
    required this.price,
    required this.imagePath,
    this.onPurchase,
    this.isFree = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPurchase,
      child: Container(
        padding: EdgeInsets.only(top: 5, bottom: 0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          gradient: LinearGradient(
            colors: AppGradients.of(context).grownEarly,
          ),
        ),
        child: Column(
          children: [
            AutoSizeText(
              label,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w700,
              ),
              minFontSize: 10,
              maxFontSize: 16,
            ),
            Expanded(
              child: Image.asset(
                imagePath,
                width: 70,
                height: 70,
              ),
            ),
            SizedBox(height: 5),
            if (!isFree)
              ui.FilledButton(
                padding: EdgeInsets.symmetric(horizontal: 0),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(10),
                  bottomRight: Radius.circular(10),
                ),
                onPressed: onPurchase,
                child: AutoSizeText(
                  price,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                color: AppColors.of(context).saleGreen,
              )
            else
              ui.FilledButton(
                padding: EdgeInsets.symmetric(horizontal: 0),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(10),
                  bottomRight: Radius.circular(10),
                ),
                onPressed: onPurchase,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: const [
                    AutoSizeText(
                      "FREE",
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    FaIcon(FontAwesomeIcons.ad, color: Colors.white)
                  ],
                ),
                color: AppColors.of(context).badRed,
              )
          ],
        ),
      ),
    );
  }
}
