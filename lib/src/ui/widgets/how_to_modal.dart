import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/evaluation.dart';
import 'package:wordle/src/core/utils/env.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/models/game_row.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/ui/widgets/game_row_container.dart';

class HowToModal extends StatelessWidget {
  HowToModal({Key? key}) : super(key: key);
  final _localStorage = ServiceLocator.locate<LocalStorage>();

  final weary = GameRow();
  final pills = GameRow();
  final vague = GameRow();

  @override
  Widget build(BuildContext context) {
    weary.setRow('weary'.split(''));
    pills.setRow('pills'.split(''));
    vague.setRow('vague'.split(''));

    weary.letterAt(0)?.evaluation = Evaluation.correct;
    pills.letterAt(1)?.evaluation = Evaluation.present;
    vague.letterAt(3)?.evaluation = Evaluation.absent;

    return PopScope(
      onPopInvoked: (canPop) {
        _localStorage.saveBool(LSKey.isFirstLaunch, false);
      },
      child: AlertDialog(
        backgroundColor: AppColors.of(context).colorTone7,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5.0),
        ),
        title: const Text(
          'HOW TO PLAY',
          style: TextStyle(fontWeight: FontWeight.w600),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('''Guess the ${env(EnvKey.PLATFORM_APP_NAME)} in 6 tries.
      
      Each guess must be a valid 5 letter word. Hit the enter button to submit.
      
      After each guess, the color of the tiles will change to show how close your guess was to the word.
        '''),
              const Divider(),
              const Text(
                'Examples',
                style: TextStyle(fontWeight: FontWeight.w700),
              ),
              GameRowContainer(
                row: weary,
                rowCount: 1,
              ),
              const Text(
                  'The letter W is in the word and in the correct spot.'),
              GameRowContainer(
                row: pills,
                rowCount: 1,
              ),
              const Text('The letter I is in the word but in the wrong spot.'),
              GameRowContainer(
                row: vague,
                rowCount: 1,
              ),
              const Text('The letter U is not in the word in any spot.'),
              const Divider(),
              Text(
                'A new ${env(EnvKey.PLATFORM_APP_NAME)} will be available each day in daily mode. If you can\'t wait, try our unlimited mode!',
                style: const TextStyle(fontWeight: FontWeight.w700),
              ),
            ],
          ),
        ),
        actions: [
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                TextButton(
                  onPressed: () {
                    AppRouter.pop();
                  },
                  child: const Text(
                    'OK',
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
