import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/core/constants/font_family.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';
import 'package:wordle/src/ui/widgets/reusable_dialog.dart';

import '../theme/app_theme.dart';
import '../theme/custom_theme.dart';

class SpeedIntroDialog extends StatefulWidget {
  final void Function() onClick;

  const SpeedIntroDialog({
    Key? key,
    required this.onClick,
  }) : super(key: key);

  @override
  State<SpeedIntroDialog> createState() => _SpeedIntroDialogState();
}

class _SpeedIntroDialogState extends State<SpeedIntroDialog> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(builder: (context, setState) {
      return reusableDialog(
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 32),
        context: context,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 74,
              width: 74,
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.smBlue,
                shape: BoxShape.circle,
              ),
              child: SvgPicture.asset(
                AppImages.speedModeIcon,
              ),
            ),
            SizedBox(
              height: 8,
            ),
            Text(
              "Speed Mode",
              style: TextStyle(
                fontFamily: FontFamily.stymie,
                fontWeight: FontWeight.w900,
                fontSize: 24,
                color: AppColors.smBlue,
              ),
            ),
            SizedBox(
              height: 36,
            ),
            Text(
              "Speed Mode",
              style: CustomThemeText.urbanistTextBlack(
                context: context,
                fontSize: 24,
                fontWeight: FontWeight.w900,
              ),
            ),
            SizedBox(
              height: 18,
            ),
            SvgPicture.asset(
              AppImages.timerImage,
            ),
            SizedBox(
              height: 20,
            ),
            Text(
              "Solve the puzzle before time runs out! You will be scored not only on how many guesses you made, but also how fast you solved it.\nBut you have to think fast!\nIf time runs out, the game is over.",
              textAlign: TextAlign.center,
              style: CustomThemeText.urbanistTextBlack(
                context: context,
                height: 1.15,
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(
              height: 42,
            ),
            ElevatedButton(
              onPressed: widget.onClick,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.of(context).saleGreen,
                elevation: 0,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Let’s TRY'.toUpperCase(),
                    style: buttonTextStyle(context: context),
                  ),
                  SizedBox(
                    width: 6,
                  ),
                  Icon(
                    Icons.arrow_forward_ios_outlined,
                    size: 15,
                    color: CustomTheme.getWhiteIconColor(context),
                  ),
                ],
              ),
            )
          ],
        ),
      );
    });
  }
}
