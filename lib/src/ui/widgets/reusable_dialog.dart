import 'package:flutter/material.dart';

import '../../core/constants/app_colors.dart';
import '../../router/app_router.dart';
import '../theme/custom_theme.dart';
import '../views/entrance/entrance_view.dart';

Widget reusableDialog({
  required BuildContext context,
  required Widget child,
  bool shouldGoToHome = false,
  bool showCancelButton = true,
  EdgeInsetsGeometry? padding,
}) {
  return PopScope(
    canPop: showCancelButton,
    child: Dialog(
      backgroundColor: AppColors.of(context).getAlertBackgroundColor,
      insetPadding: EdgeInsets.symmetric(horizontal: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Stack(
        alignment: Alignment.topRight,
        children: [
          Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * .9,
            ),
            width: double.maxFinite,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: AppColors.of(context).getAlertBackgroundColor,
              border: Border.all(
                  color: AppColors.of(context).getAlertBorderColor, width: 1),
            ),
            padding: padding ??
                EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
            child: SingleChildScrollView(
              child: child,
            ),
          ),
          Visibility(
            visible: showCancelButton,
            child: IconButton(
              onPressed: () {
                AppRouter.pop();
                if (shouldGoToHome) {
                  AppRouter.pushAndRemoveUntil(
                    EntranceView.routeName,
                    (route) => false,
                  );
                }
              },
              icon: Icon(
                Icons.close,
                color: CustomTheme.getBlackIconColor(context),
              ),
            ),
          ),
        ],
      ),
    ),
  );
}
