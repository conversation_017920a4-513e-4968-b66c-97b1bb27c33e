import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';

class EntranceOption extends StatelessWidget {
  final List<Color> gradient;
  final Color borderColor;
  final String imagePath;
  final String title;
  final String subTitle;
  final VoidCallback? onPress;

  const EntranceOption({
    Key? key,
    required this.gradient,
    required this.borderColor,
    required this.imagePath,
    required this.title,
    required this.subTitle,
    this.onPress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPress,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
        decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: gradient,
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              width: 7,
              color: borderColor,
            )),
        child: Column(
          children: [
            Image.asset(imagePath, height: 70),
            AutoSizeText(
              title,
              style: TextStyle(
                fontSize: 25,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              minFontSize: 15,
              maxFontSize: 25,
              maxLines: 1,
              textScaleFactor: 1,
            ),
            AutoSizeText(
              subTitle,
              style: TextStyle(color: Colors.white),
              maxLines: 2,
              maxFontSize: 16,
              textScaleFactor: 1,
            )
          ],
        ),
      ),
    );
  }
}
