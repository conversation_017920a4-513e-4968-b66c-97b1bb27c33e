import 'package:flutter/material.dart';

import '../../core/constants/app_colors.dart';
import '../../core/constants/font_family.dart';
import '../theme/custom_theme.dart';
import '../theme/custom_theme_text.dart';

class ReusableUnlimitedLetterContainer extends StatelessWidget {
  final int numberOfLetter;
  final VoidCallback onPress;
  const ReusableUnlimitedLetterContainer({
    Key? key,
    required this.numberOfLetter,
    required this.onPress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPress,
      child: Container(
        height: 75,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: CustomTheme.getBlackBorderColor(context),
              width: 1,
            )),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Center(
              child: Text(
                numberOfLetter.toString(),
                style: TextStyle(
                  color: AppColors.green,
                  fontSize: 58,
                  fontFamily: FontFamily.urbanist,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            SizedBox(
              width: 12,
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "letter".toUpperCase(),
                  style: CustomThemeText.urbanistTextBlack(
                      context: context,
                      fontSize: 16,
                      fontWeight: FontWeight.w700),
                ),
                Text(
                  "Unlimited",
                  style: CustomThemeText.urbanistTextBlack(
                    context: context,
                    height: 1,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
