import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/app_gradients.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/ui/widgets/bullet_points.dart';
import 'package:wordle/src/ui/widgets/buttons/filled_button.dart' as ui;

class PremiumSubscriptionCard extends StatelessWidget {
  final VoidCallback? onPurchase;
  final String title;
  final String price;
  final List<String> bulletPoints;
  const PremiumSubscriptionCard({
    Key? key,
    required this.price,
    required this.title,
    this.onPurchase,
    this.bulletPoints = const [],
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPurchase,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          gradient: LinearGradient(
            colors: AppGradients.of(context).octoberSilence,
          ),
        ),
        child: Column(
          children: [
            Text(
              title,
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w700,
              ),
            ),
            Row(
              children: [
                BulletPoints(
                  textStyle: TextStyle(color: Colors.white, fontSize: 15),
                  points: bulletPoints,
                ),
                Image.asset(
                  AppImages.crownImage,
                  width: 100,
                  height: 100,
                )
              ],
            ),
            ui.FilledButton(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(10),
                bottomRight: Radius.circular(10),
              ),
              onPressed: onPurchase,
              child: Text(
                price,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                ),
              ),
              color: AppColors.of(context).saleGreen,
            ),
          ],
        ),
      ),
    );
  }
}
