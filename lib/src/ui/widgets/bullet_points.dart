import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

class BulletPoints extends StatelessWidget {
  final List<String> points;
  final TextStyle? textStyle;
  const BulletPoints({Key? key, required this.points, this.textStyle})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final _markDownData = points.map((x) => "- $x\n").reduce((x, y) => "$x$y");
    return Expanded(
      child: Markdown(
        physics: NeverScrollableScrollPhysics(),
        styleSheet: MarkdownStyleSheet(
          listBullet: textStyle,
          p: textStyle,
        ),
        data: _markDownData,
        shrinkWrap: true,
      ),
    );
  }
}
