import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/data/models/daily_comment.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/ui/widgets/app_avatar.dart';
import 'package:wordle/src/ui/widgets/dialogs/confirm_dialog.dart';

class CommentWidget extends StatelessWidget {
  final bool enabled;
  final DailyComment comment;
  final VoidCallback? onFlag;

  const CommentWidget({
    Key? key,
    this.enabled = true,
    required this.comment,
    this.onFlag,
  }) : super(key: key);

  void _confirmFlag(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showDialog(
        context: context,
        builder: (context) => ConfirmDialog(
          title: "Flag Comment",
          body:
              "Flagging this comment will report this comment and also hide it from your feed. Do you want to continue?",
          cancelText: 'No',
          confirmText: 'Yes',
          onConfirm: () {
            onFlag!();

            AppRouter.pop();
          },
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      enabled: enabled,
      leading: AppAvatar(
        size: 20,
        iconSize: 15,
        photoURL: comment.photoURL,
      ),
      // horizontalTitleGap: EdgeInsets.,
      title: Row(children: [
        Flexible(
          child: Text(
            comment.displayName ?? '',
            style: TextStyle(fontWeight: FontWeight.bold),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Text(' · ', style: TextStyle(color: Colors.grey)),
        Text(
          comment.relativeCreatedAt,
          style: TextStyle(color: Colors.grey),
        ),
      ]),
      subtitle: Text(
        comment.comment,
        style: TextStyle(
          color: enabled ? AppColors.of(context).colorTone1 : null,
        ),
      ),
      trailing: PopupMenuButton<String>(
        itemBuilder: (_) {
          return [
            PopupMenuItem(
              child: Text('Flag comment'),
              value: 'flag',
              onTap: onFlag == null
                  ? onFlag
                  : () {
                      _confirmFlag(context);
                    },
            )
          ];
        },
      ),
    );
  }
}
