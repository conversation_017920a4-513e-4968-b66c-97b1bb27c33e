import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/domain/multiplayer/stores/multiplayer_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/ui/theme/custom_theme.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';
import 'package:wordle/src/ui/views/multiplayer/match_making_view.dart';
import 'package:wordle/src/ui/widgets/app_avatar.dart';
import 'package:wordle/src/ui/widgets/reusable_dialog.dart';

import '../../core/constants/events.dart';
import '../../core/helpers/toast.dart';
import '../../domain/main/stores/main_store.dart';
import '../../services/events/event_logger_contract.dart';
import '../theme/app_theme.dart';
import 'bottom_sheets/auth_bottom_sheet.dart';
import 'dialogs/info_dialog.dart';

class MultiplayerStatsDialog extends StatelessWidget {
  MultiplayerStatsDialog({
    Key? key,
  }) : super(key: key);

  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final _authService = ServiceLocator.locate<AuthService>();
  final mainStore = ServiceLocator.locate<MainStore>();
  final multiplayerStore = ServiceLocator.locate<MultiplayerStore>();

  @override
  Widget build(BuildContext context) {
    // _multiplayerStore.calculatePoints();
    return reusableDialog(
      context: context,
      child: SingleChildScrollView(
        child: Observer(builder: (context) {
          return Column(
            children: [
              SizedBox(
                height: 12,
              ),
              Center(
                child: Text(
                  "STATS",
                  style: CustomThemeText.stymieTextBlack(
                    context: context,
                    fontSize: 32,
                    fontWeight: FontWeight.w900,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(
                height: 8,
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.darkYellow, width: 1),
                  borderRadius: BorderRadius.circular(11),
                ),
                child: Row(
                  children: [
                    Visibility(
                        visible: true,
                        child: Padding(
                          padding: const EdgeInsets.only(
                            right: 11,
                          ),
                          child: Container(
                            height: 30,
                            width: 30,
                            decoration: BoxDecoration(
                              color: AppColors.lightBlue,
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: Text(
                                ((multiplayerStore.multiPlayerStatsModel.rank ==
                                                0 ||
                                            multiplayerStore
                                                    .multiPlayerStatsModel
                                                    .rank ==
                                                null)
                                        ? "#"
                                        : multiplayerStore
                                            .multiPlayerStatsModel.rank)
                                    .toString(),
                                style: CustomThemeText.urbanistTextWhite(
                                  context: context,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                          ),
                        )),
                    AppAvatar(
                      size: 20,
                      iconSize: 14,
                      backgroundColor: AppColors.purple,
                      photoURL: _authService.user?.getPhotoURL(),
                    ),
                    SizedBox(
                      width: 11,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AutoSizeText(
                          "STATS",
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(
                          height: 2,
                        ),
                        AutoSizeText(
                          (multiplayerStore.multiPlayerStatsModel.points ?? "0")
                              .toString(),
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 16,
                            fontWeight: FontWeight.w300,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                    Spacer(),
                    SizedBox(
                      height: 40,
                      child: Material(
                        borderRadius: BorderRadius.circular(24),
                        child: ElevatedButton(
                          onPressed: () {
                            AppRouter.pop();
                            if (!_authService.isAuthenticated()) {
                              showModalBottomSheet(
                                context: context,
                                builder: (_) => AuthBottomSheet(
                                  onAuthenticate: () {
                                    String name =
                                        _authService.user?.getDisplayName() ??
                                            '';
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text('Signed in as: $name'),
                                      ),
                                    );
                                    if (mainStore.isAllowedToPlayMultiplayer) {
                                      AppRouter.push(MatchMakingView.routeName);
                                    } else {
                                      showDialog(
                                        context: context,
                                        builder: (_) => InfoDialog(
                                          title: 'Error',
                                          body:
                                              'Not enough coins to join a match. You need at least 10 coins to join a match.',
                                        ),
                                      );
                                    }
                                  },
                                ),
                              );
                            } else {
                              if (mainStore.isAllowedToPlayMultiplayer) {
                                AppRouter.push(MatchMakingView.routeName);
                              } else {
                                showDialog(
                                  context: context,
                                  builder: (_) => InfoDialog(
                                    title: 'Error',
                                    body:
                                        'Not enough coins to join a match. You need at least 10 coins to join a match.',
                                  ),
                                );
                              }
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.of(context).saleGreen,
                            elevation: 0,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'PLAY',
                                style: buttonTextStyle(context: context),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Padding(
                padding: const EdgeInsets.only(left: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      children: [
                        Row(
                          children: [
                            Text(
                              "Win Rate : ",
                              style: CustomThemeText.urbanistTextBlack(
                                context: context,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              "${(multiplayerStore.multiPlayerStatsModel.winRate ?? 0).toStringAsFixed(0)} %",
                              style: CustomThemeText.urbanistTextBlack(
                                context: context,
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                          width: 15,
                        ),
                        IntrinsicHeight(
                          child: Row(
                            children: [
                              Row(
                                children: [
                                  Text(
                                    "${multiplayerStore.multiPlayerStatsModel.gamesWon ?? 0} ",
                                    style: CustomThemeText.urbanistTextBlack(
                                      context: context,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                  Text(
                                    "Win",
                                    style: CustomThemeText.urbanistTextBlack(
                                      context: context,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 12,
                                child: VerticalDivider(
                                  color: Colors.black,
                                  thickness: 1.5,
                                ),
                              ),
                              Row(
                                children: [
                                  Text(
                                    "${multiplayerStore.multiPlayerStatsModel.gameLost ?? 0} ",
                                    style: CustomThemeText.urbanistTextBlack(
                                      context: context,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                  Text(
                                    "Losses",
                                    style: CustomThemeText.urbanistTextBlack(
                                      context: context,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 8,
                    ),
                    Row(
                      children: [
                        Text(
                          "Win Streak : ",
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          (multiplayerStore.multiPlayerStatsModel.winStreak ??
                                  0)
                              .toString(),
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 8,
                    ),
                    Row(
                      children: [
                        Text(
                          "Best Win Streak : ",
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          (multiplayerStore
                                      .multiPlayerStatsModel.bestWinStreak ??
                                  0)
                              .toString(),
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 8,
                    ),
                    Row(
                      children: [
                        Text(
                          "Win rate when you go first : ",
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          "${(multiplayerStore.multiPlayerStatsModel.winRateWhenPlayedFirst ?? 0).toStringAsFixed(0)}%",
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 8,
                    ),
                    Row(
                      children: [
                        Text(
                          "Win rate when you go second : ",
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          "${(multiplayerStore.multiPlayerStatsModel.winRateWhenPlayedSecond ?? 0).toStringAsFixed(0)}%",
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 36,
              ),
              ElevatedButton.icon(
                onPressed: () {
                  _eventLogger.log(Events.shareClicked);
                  Clipboard.setData(
                    ClipboardData(
                      text:
                          "Win Rate :${(multiplayerStore.multiPlayerStatsModel.winRate ?? 0).toStringAsFixed(0)} %  ${multiplayerStore.multiPlayerStatsModel.gamesWon ?? 0} Wins | ${multiplayerStore.multiPlayerStatsModel.gameLost ?? 0} Losses \nWin Streak :${multiplayerStore.multiPlayerStatsModel.winStreak ?? 0}\nBest Win Streak :${multiplayerStore.multiPlayerStatsModel.bestWinStreak ?? 0}\nWin rate when you go first :${(multiplayerStore.multiPlayerStatsModel.winRateWhenPlayedFirst ?? 0).toStringAsFixed(0)}%\nWin rate when you go second :${(multiplayerStore.multiPlayerStatsModel.winRateWhenPlayedSecond ?? 0).toStringAsFixed(0)} %",
                    ),
                  );
                  toast(context: context, msg: 'Copied results to clipboard.');
                },
                icon: Icon(
                  Icons.share,
                  color: CustomTheme.getBlackIconColor(context),
                  size: 20,
                ),
                label: Text(
                  'SHARE',
                  style: CustomThemeText.urbanistTextBlack(
                    context: context,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                style: OutlinedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  side: BorderSide(
                    width: 1.0,
                    color: CustomTheme.getBlackBorderColor(context),
                  ),
                  elevation: 0,
                ),
              ),
              SizedBox(
                height: 12,
              ),
            ],
          );
        }),
      ),
    );
  }
}
