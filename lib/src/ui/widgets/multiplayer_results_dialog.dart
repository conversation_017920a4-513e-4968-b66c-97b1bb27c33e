import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/font_family.dart';
import 'package:wordle/src/core/constants/game_result.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/domain/multiplayer/stores/multiplayer_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/ui/theme/custom_theme.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';
import 'package:wordle/src/ui/views/entrance/entrance_view.dart';
import 'package:wordle/src/ui/views/multiplayer/match_making_view.dart';
import 'package:wordle/src/ui/widgets/app_avatar.dart';
import 'package:wordle/src/ui/widgets/reusable_dialog.dart';
import 'package:wordle/src/ui/widgets/reusable_solution_box.dart';

import '../../core/constants/app_images.dart';
import '../../domain/main/stores/main_store.dart';
import '../theme/app_theme.dart';
import 'bottom_sheets/auth_bottom_sheet.dart';
import 'dialogs/info_dialog.dart';

class MultiplayerResultsDialog extends StatelessWidget {
  final GameResult result;
  MultiplayerResultsDialog({
    Key? key,
    required this.result,
  }) : super(key: key);
  final _multiplayerStore = ServiceLocator.locate<MultiplayerStore>();
  final _authService = ServiceLocator.locate<AuthService>();
  final mainStore = ServiceLocator.locate<MainStore>();
  final multiplayerStore = ServiceLocator.locate<MultiplayerStore>();

  String getMessage() {
    return result.getMessage;
  }

  BorderSide getBorderSide(BuildContext context) {
    switch (result) {
      case GameResult.won:
        return BorderSide(
          color: AppColors.of(context).correct,
          width: 3,
        );
      case GameResult.lost:
        return BorderSide(
          color: AppColors.of(context).badRed,
          width: 3,
        );
      case GameResult.draw:
        return BorderSide(
          color: AppColors.of(context).colorTone2,
          width: 3,
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    _multiplayerStore.calculatePoints();
    return reusableDialog(
      context: context,
      shouldGoToHome: true,
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(
              height: 12,
            ),
            Center(
              child: Text(
                getMessage(),
                style: CustomThemeText.stymieTextBlack(
                  context: context,
                  fontSize: 32,
                  fontWeight: FontWeight.w900,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(
              height: 12,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Opacity(
                      opacity: result == GameResult.won ? .5 : 1,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Stack(
                            alignment: Alignment.topCenter,
                            clipBehavior: Clip.none,
                            children: [
                              AppAvatar(
                                size: 35,
                                iconSize: 25,
                                photoURL: _multiplayerStore.opponent.photoURL,
                              ),
                              Positioned(
                                top: -10,
                                child: Visibility(
                                  visible: result == GameResult.lost ||
                                      result == GameResult.draw,
                                  child: SvgPicture.asset(
                                    AppImages.winnerIcon,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 4,
                          ),
                          AutoSizeText(
                            _multiplayerStore.opponent.displayName ?? '',
                            style: CustomThemeText.urbanistTextBlack(
                              context: context,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            softWrap: false,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                Spacer(),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.star_sharp,
                      color: Color(0xffd0a530),
                      size: 15,
                    ),
                    SizedBox(
                      height: 2,
                    ),
                    Text(
                      'VS',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 17,
                        color: Color(0xffd0a530),
                        fontFamily: FontFamily.stymie,
                      ),
                    ),
                  ],
                ),
                Spacer(),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Column(
                          children: [
                            Visibility(
                                visible: result != GameResult.lost,
                                child: SizedBox(
                                  height: 24,
                                )),
                            Stack(
                              alignment: Alignment.topCenter,
                              clipBehavior: Clip.none,
                              children: [
                                AppAvatar(
                                  size: 35,
                                  iconSize: 25,
                                  photoURL: _authService.user?.getPhotoURL(),
                                ),
                                Positioned(
                                  top: -10,
                                  child: Visibility(
                                    visible: result == GameResult.won ||
                                        result == GameResult.draw,
                                    child: SvgPicture.asset(
                                      AppImages.winnerIcon,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(
                              height: 4,
                            ),
                            Column(
                              children: [
                                AutoSizeText(
                                  'You',
                                  style: CustomThemeText.urbanistTextBlack(
                                    context: context,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                  softWrap: false,
                                ),
                                Visibility(
                                  visible: result != GameResult.lost,
                                  child: Column(
                                    children: [
                                      SizedBox(
                                        height: 4,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          AutoSizeText(
                                            "+" +
                                                _multiplayerStore.pointsWon
                                                    .toString(),
                                            style: CustomThemeText
                                                .urbanistTextBlack(
                                              context: context,
                                              fontSize: 23,
                                              fontWeight: FontWeight.w700,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 1,
                                            softWrap: false,
                                          ),
                                          SizedBox(width: 5),
                                          Tooltip(
                                            message:
                                                "Points are rewarded for the row that you won in. The sooner you win the more points you receive. Drawing a match rewards a small amount of points.",
                                            triggerMode: TooltipTriggerMode.tap,
                                            textAlign: TextAlign.center,
                                            textStyle: TextStyle(
                                              color: Colors.white,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                              fontFamily: FontFamily.urbanist,
                                            ),
                                            verticalOffset: 20,
                                            margin: EdgeInsets.symmetric(
                                                horizontal: 28),
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 24, vertical: 16),
                                            showDuration: Duration(seconds: 60),
                                            decoration: BoxDecoration(
                                                color: Colors.black
                                                    .withOpacity(.8),
                                                borderRadius:
                                                    BorderRadius.circular(10)),
                                            child: FaIcon(
                                              FontAwesomeIcons.questionCircle,
                                              size: 22,
                                              color:
                                                  CustomTheme.getBlackIconColor(
                                                      context),
                                            ),
                                          ),
                                        ],
                                      )
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(
              height: 36,
            ),
            Text(
              "Answer".toUpperCase(),
              style: CustomThemeText.urbanistTextBlack(
                context: context,
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(
              height: 6,
            ),
            ReusableSolutionBox(
                solution:
                    _multiplayerStore.wordOfTheSession?.toUpperCase() ?? '',
                boxSize: 28,
                fontSize: 20),
            SizedBox(
              height: 32,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  children: [
                    Row(
                      children: [
                        Text(
                          "Win Rate : ",
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          "${(multiplayerStore.multiPlayerStatsModel.winRate ?? 0).toStringAsFixed(0)} %",
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      width: 15,
                    ),
                    IntrinsicHeight(
                      child: Row(
                        children: [
                          Row(
                            children: [
                              Text(
                                "${multiplayerStore.multiPlayerStatsModel.gamesWon ?? 0} ",
                                style: CustomThemeText.urbanistTextBlack(
                                  context: context,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              Text(
                                "Win",
                                style: CustomThemeText.urbanistTextBlack(
                                  context: context,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 12,
                            child: VerticalDivider(
                              color: Colors.black,
                              thickness: 1.5,
                            ),
                          ),
                          Row(
                            children: [
                              Text(
                                "${multiplayerStore.multiPlayerStatsModel.gameLost ?? 0} ",
                                style: CustomThemeText.urbanistTextBlack(
                                  context: context,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              Text(
                                "Losses",
                                style: CustomThemeText.urbanistTextBlack(
                                  context: context,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 8,
                ),
                Row(
                  children: [
                    Text(
                      "Win Streak : ",
                      style: CustomThemeText.urbanistTextBlack(
                        context: context,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      (multiplayerStore.multiPlayerStatsModel.winStreak ?? 0)
                          .toString(),
                      style: CustomThemeText.urbanistTextBlack(
                        context: context,
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 8,
                ),
                Row(
                  children: [
                    Text(
                      "Best Win Streak : ",
                      style: CustomThemeText.urbanistTextBlack(
                        context: context,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      (multiplayerStore.multiPlayerStatsModel.bestWinStreak ??
                              0)
                          .toString(),
                      style: CustomThemeText.urbanistTextBlack(
                        context: context,
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 8,
                ),
                Row(
                  children: [
                    Text(
                      "Win rate when you go first : ",
                      style: CustomThemeText.urbanistTextBlack(
                        context: context,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      "${(multiplayerStore.multiPlayerStatsModel.winRateWhenPlayedFirst ?? 0).toStringAsFixed(0)}%",
                      style: CustomThemeText.urbanistTextBlack(
                        context: context,
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 8,
                ),
                Row(
                  children: [
                    Text(
                      "Win rate when you go second : ",
                      style: CustomThemeText.urbanistTextBlack(
                        context: context,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      "${(multiplayerStore.multiPlayerStatsModel.winRateWhenPlayedSecond ?? 0).toStringAsFixed(0)}%",
                      style: CustomThemeText.urbanistTextBlack(
                        context: context,
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(
              height: 36,
            ),
            ElevatedButton(
              onPressed: () {
                AppRouter.pop();
                if (!_authService.isAuthenticated()) {
                  showModalBottomSheet(
                    context: context,
                    builder: (_) => AuthBottomSheet(
                      onAuthenticate: () {
                        String name = _authService.user?.getDisplayName() ?? '';

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Signed in as: $name'),
                          ),
                        );
                        if (mainStore.isAllowedToPlayMultiplayer) {
                          AppRouter.pushReplacement(MatchMakingView.routeName);
                        } else {
                          AppRouter.pushAndRemoveUntil(
                            EntranceView.routeName,
                            (route) => false,
                          ).then((value) => showDialog(
                                context: context,
                                builder: (_) => InfoDialog(
                                  title: 'Error',
                                  body:
                                      'Not enough coins to join a match. You need at least 10 coins to join a match.',
                                ),
                              ));
                        }
                      },
                    ),
                  );
                } else {
                  if (mainStore.isAllowedToPlayMultiplayer) {
                    AppRouter.pushReplacement(MatchMakingView.routeName);
                  } else {
                    AppRouter.pushAndRemoveUntil(
                      EntranceView.routeName,
                      (route) => false,
                    ).then((value) => showDialog(
                          context: context,
                          builder: (_) => InfoDialog(
                            title: 'Error',
                            body:
                                'Not enough coins to join a match. You need at least 10 coins to join a match.',
                          ),
                        ));
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.of(context).saleGreen,
                elevation: 0,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'NEW GAME',
                    style: buttonTextStyle(context: context),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 12,
            ),
          ],
        ),
      ),
    );
  }
}
