import 'dart:math' as math;

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_conditional_rendering/conditional_switch.dart';
import 'package:sizer/sizer.dart';
import 'package:wordle/src/core/constants/animation_constants.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/evaluation.dart';
import 'package:wordle/src/data/models/letter_node.dart';
import 'package:wordle/src/ui/widgets/game_tile_colored.dart';
import 'package:wordle/src/ui/widgets/game_tile_tbd.dart';

import '../../core/utils/service_locator.dart';
import '../../settings/settings_controller.dart';

class GameTile extends StatefulWidget {
  final LetterNode? node;
  static const double tileSize = 70;

  // This tells the tiles to wait before flipping
  final int delayIndex;
  final bool enableAnimation;
  final LetterNode? hintNode;

  const GameTile({
    Key? key,
    this.node,
    required this.delayIndex,
    this.enableAnimation = true,
    this.hintNode,
  }) : super(key: key);

  @override
  State<GameTile> createState() => _GameTileState();
}

class _GameTileState extends State<GameTile> {
  final defaultEvaluation = Evaluation.empty;
  final settingsController = ServiceLocator.locate<SettingsController>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: AspectRatio(
        aspectRatio: 1,
        child: Container(
          margin: const EdgeInsets.all(1.5),
          child: ConditionalSwitch.single<Evaluation>(
            valueBuilder: (context) =>
                widget.node?.evaluation ?? defaultEvaluation,
            context: context,
            caseBuilders: {
              Evaluation.empty: (_) => Container(
                    width: GameTile.tileSize,
                    height: GameTile.tileSize,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppColors.boardBoxBorderColor,
                        width: 1,
                      ),
                      color: hintAvailable
                          ? AppColors.of(context).correct.withOpacity(0.5)
                          : null,
                    ),
                    child: AnimatedOpacity(
                      opacity: 0.5,
                      duration: Duration(milliseconds: 350),
                      child: Center(
                        child: AutoSizeText(
                          widget.hintNode?.letter.toUpperCase() ?? '',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            fontSize: 40.sp,
                          ),
                          maxFontSize: 40,
                        ),
                      ),
                    ),
                  ),
              Evaluation.tbd: (_) =>
                  GameTileTbd(size: GameTile.tileSize, node: widget.node),
            },
            fallbackBuilder: (_) {
              if (widget.enableAnimation) {
                return FutureBuilder(
                    future: Future.delayed(
                      Duration(
                        milliseconds:
                            AnimationConstants.delayTileTimeInMilliseconds *
                                widget.delayIndex,
                      ),
                    ),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.done) {
                        return TweenAnimationBuilder<double>(
                            tween: Tween<double>(begin: 0, end: math.pi),
                            duration: const Duration(milliseconds: 500),
                            builder: (_, rotation, __) {
                              return Transform(
                                // 180 degrees
                                transform: Matrix4.identity()
                                  ..setEntry(3, 2, 0.001)
                                  ..rotateX(rotation),
                                alignment: FractionalOffset.center,
                                child: rotation <= math.pi / 2
                                    ? GameTileTbd(
                                        size: GameTile.tileSize,
                                        node: widget.node,
                                        enableAnimation: false,
                                      )
                                    : Transform(
                                        transform: Matrix4.rotationX(math.pi),
                                        alignment: FractionalOffset.center,
                                        child: GameTileColored(
                                          size: GameTile.tileSize,
                                          node: widget.node,
                                        ),
                                      ),
                              );
                            });
                      } else {
                        return GameTileTbd(
                          size: GameTile.tileSize,
                          node: widget.node,
                          enableAnimation: false,
                        );
                      }
                    });
              } else {
                return GameTileColored(
                  size: GameTile.tileSize,
                  node: widget.node,
                );
              }
            },
          ),
        ),
      ),
    );
  }

  bool get hintAvailable => widget.hintNode != null;
}
