// ignore_for_file: deprecated_member_use

import 'package:auto_size_text/auto_size_text.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sizer/sizer.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/core/constants/evaluation.dart';
import 'package:wordle/src/core/constants/keyboard_keys.dart';
import 'package:wordle/src/core/constants/keyboard_settings.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/models/letter_node.dart';
import 'package:wordle/src/settings/settings_controller.dart';

class GameKeyBoard extends StatefulWidget {
  final void Function(String key)? onKeyPressed;
  final void Function()? onDelete;
  final void Function()? onEnter;
  final Set<LetterNode> lettersUsed;
  final Set<LetterNode> oldLettersUsed;
  final bool enabled;
  final bool isEnterButtonEnabled;

  /// Time to wait before showing letters are used
  final int delayInMilliseconds;

  const GameKeyBoard({
    Key? key,
    this.onKeyPressed,
    this.onDelete,
    this.onEnter,
    required this.lettersUsed,
    this.oldLettersUsed = const {},
    this.enabled = true,
    this.isEnterButtonEnabled = true,
    this.delayInMilliseconds = 0,
  }) : super(key: key);

  @override
  State<GameKeyBoard> createState() => _GameKeyBoardState();
}

class _GameKeyBoardState extends State<GameKeyBoard> {
  final settingsController = ServiceLocator.locate<SettingsController>();

  Color getKeyColor(BuildContext context, String key) {
    final node = widget.lettersUsed.firstWhereOrNull((element) {
      return element.letter == key;
    });

    return getNodeColor(node);
  }

  Color getOldKeyColor(BuildContext context, String key) {
    final node = widget.oldLettersUsed.firstWhereOrNull((element) {
      return element.letter == key;
    });

    return getNodeColor(node);
  }

  Color getNodeColor(LetterNode? node) {
    switch (node?.evaluation) {
      case Evaluation.absent:
        return AppColors.of(context).absent;
      case Evaluation.correct:
        return AppColors.of(context).correct;
      case Evaluation.present:
        return AppColors.of(context).present;
      default:
        return AppColors.of(context).keyBg;
    }
  }

  @override
  Widget build(BuildContext context) {
    final keys = KeyboardSettings.getLayout(settingsController.keyboardLayout);
    return Container(
      margin: EdgeInsets.only(top: 5),
      child: RawKeyboardListener(
        autofocus: true,
        focusNode: FocusNode(),
        onKey: (event) {
          final lettersRegex = RegExp(r'[a-zA-Z]');
          if (event.isKeyPressed(LogicalKeyboardKey.enter)) {
            widget.onEnter?.call();
          } else if (event.isKeyPressed(LogicalKeyboardKey.backspace)) {
            widget.onDelete?.call();
          } else if (lettersRegex.hasMatch(event.character ?? '')) {
            widget.onKeyPressed?.call(event.character!);
          }
        },
        child: Column(
          children: [
            for (var keyRow in keys)
              IntrinsicHeight(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    for (var key in keyRow)
                      if (key != KeyboardKeys.enter) ...[
                        if (key != KeyboardKeys.delete)
                          FutureBuilder(
                              future: Future.delayed(Duration(
                                  milliseconds: widget.delayInMilliseconds)),
                              builder: (context, snapshot) {
                                if (snapshot.connectionState ==
                                    ConnectionState.done) {
                                  return LetterKey(
                                    onKeyPressed: widget.enabled
                                        ? widget.onKeyPressed
                                        : null,
                                    letter: key,
                                    color: getKeyColor(context, key),
                                    enabled: widget.enabled,
                                  );
                                } else {
                                  return LetterKey(
                                    onKeyPressed: widget.enabled
                                        ? widget.onKeyPressed
                                        : null,
                                    letter: key,
                                    color: getOldKeyColor(context, key),
                                    enabled: widget.enabled,
                                  );
                                }
                              })
                        else
                          DeleteKey(
                            onDelete: widget.enabled ? widget.onDelete : null,
                            enabled: widget.enabled,
                          )
                      ] else ...[
                        EnterKey(
                          letter: key,
                          onEnter: widget.enabled ? widget.onEnter : null,
                          enabled:
                              widget.isEnterButtonEnabled && widget.enabled,
                        )
                      ]
                  ],
                ),
              )
          ],
        ),
      ),
    );
  }
}

class LetterKey extends StatefulWidget {
  final String letter;
  final void Function(String key)? onKeyPressed;
  final Color color;
  final bool enabled;

  const LetterKey({
    Key? key,
    required this.letter,
    required this.color,
    this.onKeyPressed,
    this.enabled = true,
  }) : super(key: key);

  @override
  _LetterKeyState createState() => _LetterKeyState();
}

class _LetterKeyState extends State<LetterKey> {
  double opacity = 1;

  @override
  Widget build(BuildContext context) {
    double height = MediaQuery.of(context).size.height;

    return Flexible(
      child: AspectRatio(
        aspectRatio: 43.59 / (height * .07),
        child: GestureDetector(
          onTapDown: (_) => setState(() {
            opacity = 0.4;
          }),
          onTapUp: (_) => setState(() {
            opacity = 1;
          }),
          onTapCancel: () => setState(() {
            opacity = 1;
          }),
          onTap: () {
            if (widget.onKeyPressed != null) {
              Feedback.forTap(context);
              HapticFeedback.heavyImpact();
              widget.onKeyPressed!(widget.letter);
            }
          },
          child: Builder(builder: (context) {
            Color? textColor;
            if (widget.color != AppColors.of(context).colorTone4) {
              textColor = Colors.white;
            }

            return AnimatedOpacity(
              duration: Duration(milliseconds: 200),
              opacity: widget.enabled ? opacity : 0.4,
              child: Container(
                margin: const EdgeInsets.all(1),
                decoration: BoxDecoration(
                  color: widget.color,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Center(
                    child: AutoSizeText(
                  widget.letter.toUpperCase(),
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    color: textColor,
                    fontSize: 14.sp,
                  ),
                )),
              ),
            );
          }),
        ),
      ),
    );
  }
}

class EnterKey extends StatefulWidget {
  final String letter;
  final void Function()? onEnter;
  final bool enabled;

  const EnterKey({
    Key? key,
    required this.letter,
    this.onEnter,
    this.enabled = true,
  }) : super(key: key);

  @override
  _EnterKeyState createState() => _EnterKeyState();
}

class _EnterKeyState extends State<EnterKey> {
  double opacity = 1;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (widget.onEnter != null) {
          Feedback.forTap(context);
          HapticFeedback.heavyImpact();
          widget.onEnter!();
        }
      },
      onTapDown: (_) => setState(() {
        opacity = 0.4;
      }),
      onTapCancel: () => setState(() {
        opacity = 1;
      }),
      onTapUp: (_) => setState(() {
        opacity = 1;
      }),
      child: AnimatedOpacity(
        duration: Duration(milliseconds: 200),
        opacity: widget.enabled ? opacity : 0.4,
        child: Container(
          height: double.infinity,
          margin: const EdgeInsets.all(1),
          padding: EdgeInsets.symmetric(horizontal: 10),
          decoration: BoxDecoration(
            color: AppColors.of(context).infoBlue,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Center(
            child: Text(
              widget.letter.toUpperCase(),
              style: TextStyle(
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                  fontSize: 11.sp),
            ),
          ),
        ),
      ),
    );
  }
}

class DeleteKey extends StatefulWidget {
  final void Function()? onDelete;
  final bool enabled;

  const DeleteKey({
    Key? key,
    this.onDelete,
    this.enabled = true,
  }) : super(key: key);

  @override
  _DeleteKeyState createState() => _DeleteKeyState();
}

class _DeleteKeyState extends State<DeleteKey> {
  double opacity = 1;
  bool isLongPressed = false;

  @override
  Widget build(BuildContext context) {
    double height = MediaQuery.of(context).size.height;

    return Flexible(
      child: AspectRatio(
        aspectRatio: 43.59 / (height * .07),
        child: GestureDetector(
          onLongPressStart: (_) async {
            isLongPressed = true;
            opacity = 0.4;
            do {
              if (widget.onDelete != null) {
                Feedback.forTap(context);
                HapticFeedback.heavyImpact();
                widget.onDelete!();
              }
              await Future.delayed(Duration(milliseconds: 100));
            } while (isLongPressed);
          },
          onLongPressEnd: (_) => setState(() {
            opacity = 1;
            isLongPressed = false;
          }),
          onTapDown: (_) => setState(() {
            opacity = 0.4;
          }),
          onTapUp: (_) => setState(() {
            opacity = 1;
          }),
          onTapCancel: () => setState(() {
            opacity = 1;
          }),
          onTap: () {
            if (widget.onDelete != null) {
              Feedback.forTap(context);
              HapticFeedback.heavyImpact();
              widget.onDelete!();
            }
          },
          child: AnimatedOpacity(
            duration: Duration(milliseconds: 200),
            opacity: widget.enabled ? opacity : 0.4,
            child: Container(
              margin: const EdgeInsets.all(1),
              padding: EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: AppColors.of(context).badRed,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Center(
                child: SvgPicture.asset(
                  AppImages.deleteIcon,
                  color: Colors.white,
                  width: 15.sp,
                  height: 15.sp,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
