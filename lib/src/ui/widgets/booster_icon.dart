import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../theme/custom_theme.dart';

class BoosterIcon extends StatelessWidget {
  final String iconPath;
  final EdgeInsetsGeometry padding;

  const BoosterIcon({Key? key, required this.iconPath, required this.padding})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      width: 40,
      padding: padding,
      decoration: BoxDecoration(
        border: Border.all(
          color: Color(0xffB9B9B9),
          width: 1.5,
        ),
        shape: BoxShape.circle,
      ),
      child: SvgPicture.asset(
        iconPath,
        color: CustomTheme.getBlackIconColor(context),
      ),
    );
  }
}
