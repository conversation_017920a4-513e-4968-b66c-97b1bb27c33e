import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/app_gradients.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/ui/widgets/buttons/filled_button.dart' as ui;

class SubscribedCard extends StatelessWidget {
  const SubscribedCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        gradient: LinearGradient(
          colors: AppGradients.of(context).octoberSilence,
        ),
      ),
      child: Column(
        children: [
          Text(
            'PREMIUM',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w700,
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                AppImages.crownImage,
                width: 150,
                height: 150,
              )
            ],
          ),
          ui.FilledButton(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(10),
              bottomRight: Radius.circular(10),
            ),
            onPressed: null,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: const [
                Icon(Icons.check, color: Colors.white),
                SizedBox(width: 5),
                Text(
                  'SUBSCRIBED',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
            color: AppColors.of(context).saleGreen,
          ),
        ],
      ),
    );
  }
}
