import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/core/constants/firestore_keys.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/domain/main/stores/main_store.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';

class CoinsDisplay extends StatefulWidget {
  const CoinsDisplay({Key? key, this.style, this.iconSize}) : super(key: key);

  final TextStyle? style;
  final double? iconSize;

  @override
  State<CoinsDisplay> createState() => _CoinsDisplayState();
}

class _CoinsDisplayState extends State<CoinsDisplay> {
  final userMetaRef =
      FirebaseFirestore.instance.collection(FirestoreKeys.userMeta);
  final _authService = ServiceLocator.locate<AuthService>();
  final _mainStore = ServiceLocator.locate<MainStore>();
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? userMetaStreamSub;

  @override
  void initState() {
    super.initState();

    if (_authService.isAuthenticated()) {
      final userMeta = userMetaRef.doc(_authService.user!.uid).snapshots();
      userMetaStreamSub = userMeta.listen((snapshot) {
        _mainStore.coins = snapshot.data()?['coins'] ?? 0;
      });
    }
  }

  @override
  void dispose() {
    userMetaStreamSub?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            AppImages.singleCoinNormalIcon,
            width: widget.iconSize ?? 30,
            height: widget.iconSize ?? 30,
          ),
          SizedBox(width: 6),
          Observer(
            builder: (context) => Text(
              NumberFormat.compact().format(_mainStore.coins),
              style: widget.style ??
                  CustomThemeText.urbanistTextBlack(
                    context: context,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
