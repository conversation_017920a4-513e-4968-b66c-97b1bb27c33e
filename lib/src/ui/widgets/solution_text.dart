import 'package:flutter/material.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';
import 'package:wordle/src/ui/widgets/reusable_solution_box.dart';

class SolutionText extends StatefulWidget {
  final String text;
  const SolutionText({
    Key? key,
    required this.text,
  }) : super(key: key);

  @override
  _SolutionTextState createState() => _SolutionTextState();
}

class _SolutionTextState extends State<SolutionText>
    with SingleTickerProviderStateMixin {
  late final _animationController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 1000),
  );

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController.forward();
    });

    return SlideTransition(
      position: Tween<Offset>(begin: const Offset(2, 0.0), end: Offset.zero)
          .animate(CurvedAnimation(
              parent: _animationController, curve: Curves.elasticOut)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            'Answer ',
            style: CustomThemeText.urbanistTextBlack(
              context: context,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(
            width: 8,
          ),
          ReusableSolutionBox(
            solution: widget.text,
            boxSize: 20,
            fontSize: 15,
          ),
        ],
      ),
    );
  }
}
