import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/evaluation.dart';
import 'package:wordle/src/data/models/letter_node.dart';

class GameTileColored extends StatelessWidget {
  final LetterNode? node;
  final double size;
  const GameTileColored({
    Key? key,
    this.node,
    required this.size,
  }) : super(key: key);

  Color? getColor(BuildContext context) {
    if (node?.evaluation == Evaluation.absent) {
      return AppColors.of(context).absent;
    } else if (node?.evaluation == Evaluation.present) {
      return AppColors.of(context).present;
    } else if (node?.evaluation == Evaluation.correct) {
      return AppColors.of(context).correct;
    } else {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      color: getColor(context),
      child: Center(
        child: AutoSizeText(
          node?.letter.toUpperCase() ?? '',
          style: TextStyle(
            fontWeight: FontWeight.w700,
            fontSize: 40.sp,
            color: Colors.white,
          ),
          maxFontSize: 40,
        ),
      ),
    );
  }
}
