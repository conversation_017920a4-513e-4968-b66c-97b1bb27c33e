import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/data/models/difficulty_config.dart';
import 'package:wordle/src/data/models/game_row.dart';
import 'package:wordle/src/ui/widgets/game_row_container.dart';
import 'package:wordle/src/ui/widgets/reusable_timer_bar.dart';

import '../../core/utils/service_locator.dart';
import '../../domain/main/stores/main_store.dart';
import '../../settings/settings_controller.dart';

class GameBoard extends StatefulWidget {
  final DifficultyConfig difficultyConfig;
  final List<GameRow> submittedRows;
  final Widget Function() currentRowBuilder;
  final TimerController? timerController;
  final VoidCallback? onTimerEnd;
  final bool? showTimer;

  const GameBoard({
    Key? key,
    required this.difficultyConfig,
    this.submittedRows = const [],
    required this.currentRowBuilder,
    this.timerController,
    this.onTimerEnd,
    this.showTimer,
  }) : super(key: key);

  @override
  State<GameBoard> createState() => GameBoardState();
}

class GameBoardState extends State<GameBoard> {
  final ScrollController _scrollController = ScrollController();
  final settingsController = ServiceLocator.locate<SettingsController>();
  final mainStore = ServiceLocator.locate<MainStore>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToTop();
    });
  }

  @override
  void didUpdateWidget(GameBoard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.submittedRows.length > 2) {
      WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToBottom());
    }
  }

  void _scrollToTop() {
    if (_scrollController.hasClients) {
      _scrollController.jumpTo(0);
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: Duration(milliseconds: 1000),
        curve: Curves.easeOut,
      );
    }
  }

  void scrollToTop() {
    _scrollToTop();
  }

  @override
  Widget build(BuildContext context) {
    double height = MediaQuery.of(context).size.height -
        MediaQuery.of(context).padding.bottom -
        MediaQuery.of(context).padding.top;
    double fullScreenHeight = settingsController.isFullScreen ? 0 : 45;
    double usableHeight = height - (height * .35) - fullScreenHeight - 110;
    return SizedBox(
      height: usableHeight + 25,
      width: double.infinity,
      child: RawScrollbar(
        trackVisibility: true,
        thumbVisibility: true,
        thickness: 8,
        thumbColor: AppColors.green,
        trackColor: AppColors.nonActiveGrey,
        controller: _scrollController,
        child: SizedBox(
          height: usableHeight,
          width: double.infinity,
          child: SingleChildScrollView(
            controller: _scrollController,
            child: Column(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    for (var i = 0; i < widget.submittedRows.length - 1; i++)
                      GameRowContainer(
                        row: widget.submittedRows[i],
                        maxNodes: widget.difficultyConfig.lettersCount,
                        enableAnimation: false,
                        rowCount: widget.difficultyConfig.rowCount,
                      ),
                    if (widget.submittedRows.isNotEmpty)
                      GameRowContainer(
                        row: widget.submittedRows.last,
                        maxNodes: widget.difficultyConfig.lettersCount,
                        rowCount: widget.difficultyConfig.rowCount,
                      ),
                    if (!isBoardCompleted) widget.currentRowBuilder(),
                    for (var i = 0; i < emptyRows; i++)
                      GameRowContainer(
                        row: null,
                        maxNodes: widget.difficultyConfig.lettersCount,
                        rowCount: widget.difficultyConfig.rowCount,
                      ),
                  ],
                ),
                Observer(builder: (context) {
                  return Visibility(
                    visible:
                        mainStore.isSpeedMode && (widget.showTimer ?? false),
                    child: widget.timerController == null
                        ? const SizedBox()
                        : Padding(
                            padding: const EdgeInsets.only(
                                top: 10, right: 14, left: 24),
                            child: ReusableTimerBar(
                              controller: widget.timerController!,
                              onTimerEnd: widget.onTimerEnd,
                            ),
                          ),
                  );
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  int get emptyRows {
    int empty =
        widget.difficultyConfig.rowCount - (widget.submittedRows.length + 1);
    return widget.submittedRows.length == widget.difficultyConfig.rowCount
        ? 0
        : empty;
  }

  bool get isBoardCompleted =>
      widget.submittedRows.length == widget.difficultyConfig.rowCount;
}
