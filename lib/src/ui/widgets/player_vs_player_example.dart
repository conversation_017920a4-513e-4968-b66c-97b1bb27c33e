import 'package:flutter/material.dart';
import 'package:wordle/src/ui/widgets/app_avatar.dart';
import 'package:wordle/src/ui/widgets/match_timer.dart';

class PlayerVsPlayerExample extends StatelessWidget {
  const PlayerVsPlayerExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FittedBox(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: const [
              AppAvatar(
                size: 20,
                iconSize: 15,
                photoURL: null,
              ),
              SizedBox(
                width: 10,
              ),
              MatchTimer(
                time: '2:00',
                active: false,
              ),
            ],
          ),
          SizedBox(width: 5),
          Text(
            'VS',
            style: TextStyle(fontWeight: FontWeight.w600),
          ),
          SizedBox(width: 5),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: const [
              MatchTimer(
                time: '2:00',
                active: true,
              ),
              Sized<PERSON>ox(
                width: 10,
              ),
              AppAvatar(
                size: 20,
                iconSize: 15,
                photoURL: null,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
