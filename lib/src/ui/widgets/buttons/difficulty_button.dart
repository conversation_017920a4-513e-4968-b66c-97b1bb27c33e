import 'package:flutter/material.dart';

class DifficultyButton extends StatelessWidget {
  final String title;
  final String subtitle;
  final Color? color;
  final VoidCallback? onPressed;
  const DifficultyButton({
    Key? key,
    required this.title,
    required this.subtitle,
    this.color,
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10),
        width: double.infinity,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
            ),
            Text(
              subtitle,
              style: TextStyle(),
            ),
          ],
        ),
      ),
    );
  }
}
