import 'package:badges/badges.dart' as badges;
import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';
import 'package:wordle/src/ui/widgets/booster_icon.dart';

class SkipButton extends StatefulWidget {
  final String iconPath;
  final void Function()? onPressed;
  final bool isAdFree;

  const SkipButton({
    Key? key,
    required this.iconPath,
    this.onPressed,
    this.isAdFree = false,
  }) : super(key: key);

  @override
  State<SkipButton> createState() => _SkipButtonState();
}

class _SkipButtonState extends State<SkipButton> {
  double opacity = 1;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: isEnabled
          ? (_) => setState(() {
                opacity = 0.4;
              })
          : null,
      onTapUp: isEnabled
          ? (_) => setState(() {
                opacity = 1;
              })
          : null,
      onTapCancel: isEnabled
          ? () => setState(() {
                opacity = 1;
              })
          : null,
      onTap: isEnabled ? widget.onPressed : null,
      child: badges.Badge(
        badgeContent: !widget.isAdFree
            ? Container(
                padding: EdgeInsets.symmetric(horizontal: 5, vertical: 4),
                decoration: BoxDecoration(
                    color: isEnabled ? AppColors.orange : AppColors.mutedGrey,
                    borderRadius: BorderRadius.circular(12)),
                child: Text(
                  "Ad",
                  style: CustomThemeText.urbanistTextWhite(
                    context: context,
                    fontSize: 10,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              )
            : null,
        position: badges.BadgePosition.bottomEnd(bottom: -10, end: -14),
        showBadge: !widget.isAdFree,
        badgeAnimation: badges.BadgeAnimation.fade(),
        badgeStyle: badges.BadgeStyle(
          badgeColor: widget.isAdFree
              ? (isEnabled ? AppColors.orange : AppColors.mutedGrey)
              : Colors.transparent,
          shape: badges.BadgeShape.circle,
          padding: EdgeInsets.symmetric(horizontal: 5, vertical: 2),
        ),
        child: AnimatedOpacity(
          duration: Duration(milliseconds: 200),
          opacity: isEnabled ? opacity : 0.4,
          child: BoosterIcon(
            iconPath: widget.iconPath,
            padding: EdgeInsets.only(left: 10, right: 7, top: 4, bottom: 4),
          ),
        ),
      ),
    );
  }

  bool get isEnabled => widget.onPressed != null;
}
