import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

class HidingFAB extends StatefulWidget {
  final ScrollController controller;
  final VoidCallback? onPressed;
  final Widget? child;
  final Color? backgroundColor;
  const HidingFAB({
    Key? key,
    required this.controller,
    this.onPressed,
    this.child,
    this.backgroundColor,
  }) : super(key: key);

  @override
  State<HidingFAB> createState() => _HidingFABState();
}

class _HidingFABState extends State<HidingFAB> {
  bool isVisible = true;

  @override
  void initState() {
    super.initState();

    widget.controller.addListener(() {
      if (widget.controller.position.userScrollDirection ==
          ScrollDirection.reverse) {
        if (isVisible) {
          setState(() {
            isVisible = false;
          });
        }
      } else {
        if (!isVisible) {
          setState(() {
            isVisible = true;
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: isVisible,
      child: FloatingActionButton(
        isExtended: isVisible,
        backgroundColor: widget.backgroundColor,
        onPressed: widget.onPressed,
        child: widget.child,
      ),
    );
  }
}
