import 'package:flutter/cupertino.dart';

class FilledButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final Color? color;
  final Color disabledColor;
  final double? minSize;
  final double? pressedOpacity;
  final BorderRadius? borderRadius;
  final AlignmentGeometry alignment;
  final double width;
  final bool enabled;

  const FilledButton({
    Key? key,
    required this.onPressed,
    required this.child,
    this.padding,
    this.color,
    this.disabledColor = CupertinoColors.quaternarySystemFill,
    this.minSize = kMinInteractiveDimensionCupertino,
    this.pressedOpacity = 0.4,
    this.borderRadius = const BorderRadius.all(Radius.circular(5.0)),
    this.alignment = Alignment.center,
    this.width = double.infinity,
    this.enabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      child: CupertinoButton(
        color: color,
        alignment: alignment,
        borderRadius: borderRadius,
        disabledColor: disabledColor,
        key: key,
        minSize: minSize,
        padding: padding,
        pressedOpacity: pressedOpacity,
        child: child,
        onPressed: enabled ? onPressed : null,
      ),
    );
  }
}
