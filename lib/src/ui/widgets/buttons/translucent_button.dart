import 'package:flutter/material.dart';

class TranslucentButton extends StatelessWidget {
  const TranslucentButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      child: Text(
        'text',
        style: TextStyle(color: Color(0xff2196f3)),
      ),
      onPressed: () {},
      style: ElevatedButton.styleFrom(
        elevation: 0.0,
        backgroundColor: Color(0xff2196f3).withOpacity(0.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(5)),
          side: BorderSide(color: Color(0xff2196f3)),
        ),
      ),
    );
  }
}
