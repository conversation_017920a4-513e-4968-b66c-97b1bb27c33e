import 'package:badges/badges.dart' as badges;
import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/ui/widgets/booster_icon.dart';

import '../../theme/custom_theme_text.dart';

class BoosterButton extends StatefulWidget {
  final String iconPath;
  final int count;
  final bool isAdFree;
  final void Function()? onPressed;

  const BoosterButton({
    Key? key,
    required this.iconPath,
    this.count = 0,
    this.onPressed,
    required this.isAdFree,
  }) : super(key: key);

  @override
  State<BoosterButton> createState() => _BoosterButtonState();
}

class _BoosterButtonState extends State<BoosterButton> {
  double opacity = 1;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: isEnabled
          ? (_) => setState(() {
                opacity = 0.4;
              })
          : null,
      onTapUp: isEnabled
          ? (_) => setState(() {
                opacity = 1;
              })
          : null,
      onTapCancel: isEnabled
          ? () => setState(() {
                opacity = 1;
              })
          : null,
      onTap: isEnabled ? widget.onPressed : null,
      child: badges.Badge(
        badgeContent: Center(
          child: isEnabled
              ? (widget.isAdFree
                  ? Padding(
                      padding: const EdgeInsets.all(2),
                      child: Text(
                        '${widget.count}',
                        style: CustomThemeText.urbanistTextWhite(
                          context: context,
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    )
                  : Container(
                      padding: EdgeInsets.symmetric(horizontal: 5, vertical: 4),
                      decoration: BoxDecoration(
                          color: AppColors.orange,
                          borderRadius: BorderRadius.circular(12)),
                      child: Text(
                        "Ad",
                        style: CustomThemeText.urbanistTextWhite(
                          context: context,
                          fontSize: 10,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ))
              : Padding(
                  padding: const EdgeInsets.all(1),
                  child: Text(
                    '${widget.count}',
                    style: CustomThemeText.urbanistTextWhite(
                      context: context,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
        ),
        badgeAnimation: badges.BadgeAnimation.fade(),
        position: badges.BadgePosition.bottomEnd(
          bottom: isEnabled ? (widget.isAdFree ? -8 : -12) : -10,
          end: isEnabled ? (widget.isAdFree ? -4 : -14) : -7,
        ),
        badgeStyle: badges.BadgeStyle(
          badgeColor: isEnabled
              ? (widget.isAdFree ? AppColors.orange : Colors.transparent)
              : AppColors.mutedGrey,
          shape: badges.BadgeShape.circle,
          borderRadius: BorderRadius.circular(widget.isAdFree ? 50 : 12),
          padding: EdgeInsets.symmetric(
              horizontal: 5, vertical: widget.isAdFree ? 2 : 4),
        ),
        child: AnimatedOpacity(
          duration: Duration(milliseconds: 200),
          opacity: isEnabled ? opacity : 0.4,
          child: BoosterIcon(
            iconPath: widget.iconPath,
            padding: EdgeInsets.only(left: 8, right: 4, top: 6, bottom: 6),
          ),
        ),
      ),
    );
  }

  bool get isEnabled => widget.onPressed != null;
}
