import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/app_images.dart';

class PushDownButton extends StatefulWidget {
  final void Function()? onPressed;
  const PushDownButton({Key? key, this.onPressed}) : super(key: key);

  @override
  State<PushDownButton> createState() => _PushDownButtonState();
}

class _PushDownButtonState extends State<PushDownButton> {
  double borderWidth = 4;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (details) => setState(() {
        borderWidth = 1;
      }),
      onTapUp: (details) => setState(() {
        borderWidth = 4;
      }),
      onTap: widget.onPressed,
      child: ClipPath(
        clipper: ShapeBorderClipper(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(5)),
          ),
        ),
        child: Container(
          decoration: BoxDecoration(
              color: AppColors.of(context).deepOrange,
              border: Border(
                bottom: BorderSide(
                  width: borderWidth,
                  color: Colors.deepOrange.shade900,
                ),
              )),
          width: 38,
          height: 38,
          child: Image.asset(
            AppImages.bulbIcon,
            width: 35,
            height: 35,
          ),
        ),
      ),
    );
  }
}
