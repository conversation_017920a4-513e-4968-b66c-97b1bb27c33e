import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/font_family.dart';

class MatchTimer extends StatelessWidget {
  final String time;
  final bool active;
  const MatchTimer({
    Key? key,
    required this.time,
    this.active = false,
  }) : super(key: key);

  // ignore: unused_element
  Color _getTimerColor(BuildContext context) {
    int minutes = int.parse(time.split(':').first);
    int seconds = int.parse(time.split(':').last);
    if (active) {
      if (minutes == 0 && seconds <= 20) {
        return AppColors.of(context).badRed;
      } else {
        return AppColors.of(context).correct;
      }
    } else {
      return AppColors.of(context).keyBg;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      time,
      style: TextStyle(
        color: active ? Colors.white : Colors.black,
        fontWeight: FontWeight.w500,
        fontSize: 14,
        fontFamily: FontFamily.urbanist,
      ),
    );
  }
}
