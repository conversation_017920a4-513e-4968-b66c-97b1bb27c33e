import 'package:flutter/foundation.dart' show kIsWeb, defaultTargetPlatform;
import 'package:flutter/material.dart';
import 'package:flutter_overlay_loader/flutter_overlay_loader.dart';
import 'package:flutter_signin_button/flutter_signin_button.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/domain/auth/store/auth_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/settings/settings_controller.dart';

import '../../../domain/shop/store/shop_store.dart';
import '../../../services/billing/billing_service.dart';

class AuthBottomSheet extends StatefulWidget {
  final VoidCallback onAuthenticate;
  final bool linkGuest;

  const AuthBottomSheet(
      {Key? key, required this.onAuthenticate, this.linkGuest = false})
      : super(key: key);

  @override
  State<AuthBottomSheet> createState() => _AuthBottomSheetState();
}

class _AuthBottomSheetState extends State<AuthBottomSheet> {
  final settingsController = ServiceLocator.locate<SettingsController>();
  final _authStore = ServiceLocator.locate<AuthStore>();
  final List<ReactionDisposer> _reactionDisposers = [];
  final _billingService = ServiceLocator.locate<BillingService>();
  final shopStore = ServiceLocator.locate<ShopStore>();

  Future<void> setPurchaseListener() async {
    if (!kIsWeb) {
      shopStore.purchaseListStream = _billingService.purchaseStream.listen(
        shopStore.onPurchaseUpdate,
      );
    }
  }

  @override
  void initState() {
    super.initState();

    _reactionDisposers.add(
      reaction<Status?>((_) => _authStore.loginStatus, (status) {
        _handlePending(context, status);
        _handleError(context, status);
        _handleDone(context, status);
      }),
    );
    setPurchaseListener();
  }

  @override
  void dispose() {
    super.dispose();

    for (var reactionDisposer in _reactionDisposers) {
      reactionDisposer();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 10),
                Text(
                  "SIGN IN",
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                SizedBox(height: 30),
                if (TargetPlatform.iOS == defaultTargetPlatform)
                  SignInButton(
                    settingsController.isDarkMode
                        ? Buttons.Apple
                        : Buttons.AppleDark,
                    elevation: 0,
                    onPressed: () {
                      _authStore.signInWithApple(
                          linkGuest: widget.linkGuest, context: context);
                    },
                  ),
                SignInButton(
                  Buttons.GoogleDark,
                  elevation: 0,
                  onPressed: () {
                    _authStore.signInWithGoogle(
                        linkGuest: widget.linkGuest, context: context);
                  },
                ),
                SignInButton(
                  Buttons.Facebook,
                  elevation: 0,
                  onPressed: () {
                    _authStore.signInWithFacebook(
                        linkGuest: widget.linkGuest, context: context);
                  },
                ),
                if (!kIsWeb)
                  SignInButton(
                    Buttons.Twitter,
                    elevation: 0,
                    onPressed: () {
                      _authStore.signInWithTwitter(
                          linkGuest: widget.linkGuest, context: context);
                    },
                  ),
                SizedBox(height: 50),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _handlePending(BuildContext context, Status? status) {
    if (status == Status.pending) {
      Loader.show(
        context,
        progressIndicator: const CircularProgressIndicator.adaptive(),
      );
    } else {
      Loader.hide();
    }
  }

  void _handleError(BuildContext context, Status? status) {
    if (status == Status.error) {
      AppRouter.pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(_authStore.errorMessage)),
      );
    }
  }

  void _handleDone(BuildContext context, Status? status) {
    if (status == Status.done) {
      AppRouter.pop();
      widget.onAuthenticate();
    }
  }
}
