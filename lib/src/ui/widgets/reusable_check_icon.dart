import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../core/constants/app_colors.dart';
import '../../core/constants/app_images.dart';

class ReusableCheckIcon extends StatelessWidget {
  final double padding;
  final double height;
  final Color? iconColor;
  final Color? backgroundColor;

  const ReusableCheckIcon({
    super.key,
    this.padding = 5,
    this.height = 9,
    this.iconColor,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 5),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.white,
        shape: BoxShape.circle,
      ),
      padding: EdgeInsets.all(padding),
      child: SvgPicture.asset(
        AppImages.checkIcon,
        color: iconColor ?? AppColors.green,
        height: height,
      ),
    );
  }
}
