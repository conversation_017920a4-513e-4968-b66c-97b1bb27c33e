import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';

Future<void> showLoading(BuildContext context, [String message = ""]) async {
  showDialog(
    context: context,
    barrierDismissible: false,
    barrierColor: Colors.grey.withOpacity(.2),
    builder: (BuildContext context) {
      return Dialog(
        insetPadding:
            const EdgeInsets.symmetric(horizontal: 120.0, vertical: 20.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.0),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CupertinoActivityIndicator(
                color: AppColors.warmGray,
              ),
              Visibility(
                visible: message != "",
                child: Center(
                  child: Text(
                    message,
                    style: CustomThemeText.urbanistTextBlack(
                      context: context,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      );
    },
  );
}

Future<void> hideLoading(BuildContext context) async {
  AppRouter.pop();
}
