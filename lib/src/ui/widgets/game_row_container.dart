import 'package:flutter/material.dart';
import 'package:wordle/src/data/models/game_row.dart';
import 'package:wordle/src/data/models/letter_node.dart';
import 'package:wordle/src/ui/widgets/game_tile.dart';

import '../../core/utils/service_locator.dart';
import '../../settings/settings_controller.dart';

class GameRowContainer extends StatelessWidget {
  final GameRow? row;
  final int maxNodes;
  final int rowCount;
  final bool enableAnimation;
  final Map<int, LetterNode?> hints;

  GameRowContainer({
    Key? key,
    required this.row,
    this.maxNodes = 5,
    this.enableAnimation = true,
    this.hints = const {},
    required this.rowCount,
  }) : super(key: key);

  final settingsController = ServiceLocator.locate<SettingsController>();

  @override
  Widget build(BuildContext context) {
    double height = MediaQuery.of(context).size.height -
        MediaQuery.of(context).padding.bottom -
        MediaQuery.of(context).padding.top;
    double fullScreenHeight = settingsController.isFullScreen ? 0 : 45;
    double usableHeight = height - (height * .38) - fullScreenHeight - 100;
    double width = MediaQuery.of(context).size.width;

    int rowSize = maxNodes == 5
        ? 6
        : maxNodes == 6
            ? 6
            : 7;

    return Flexible(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: width - 48,
          maxHeight: (usableHeight / rowSize),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            for (var i = 0; i < maxNodes; i++)
              GameTile(
                hintNode: hints[i],
                node: row?.letterAt(i),
                delayIndex: i,
                enableAnimation: enableAnimation,
              ),
          ],
        ),
      ),
    );
  }
}
