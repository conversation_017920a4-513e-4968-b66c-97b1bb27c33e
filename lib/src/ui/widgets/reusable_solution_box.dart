import 'package:flutter/material.dart';

import '../../core/constants/app_colors.dart';
import '../../core/constants/font_family.dart';

class ReusableSolutionBox extends StatelessWidget {
  const ReusableSolutionBox({
    Key? key,
    required this.solution,
    required this.boxSize,
    required this.fontSize,
  }) : super(key: key);

  final String solution;
  final double boxSize;
  final double fontSize;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: List.generate(
        solution.length,
        (index) => Container(
          height: boxSize,
          width: boxSize,
          margin: EdgeInsets.only(right: 2),
          decoration: BoxDecoration(
            color: AppColors.orange,
          ),
          child: Center(
            child: Text(
              solution[index],
              textAlign: TextAlign.center,
              style: TextStyle(
                color: AppColors.white,
                fontFamily: FontFamily.urbanist,
                fontSize: fontSize,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
