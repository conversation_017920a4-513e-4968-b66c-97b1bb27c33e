import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/data/models/letter_node.dart';
import 'package:sizer/sizer.dart';

class GameTileTbd extends StatefulWidget {
  final LetterNode? node;
  final double size;
  final bool enableAnimation;
  const GameTileTbd({
    Key? key,
    this.node,
    required this.size,
    this.enableAnimation = true,
  }) : super(key: key);

  @override
  _GameTileTbdState createState() => _GameTileTbdState();
}

class _GameTileTbdState extends State<GameTileTbd>
    with SingleTickerProviderStateMixin {
  late final _animationController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 100),
  );

  late final Animation<double> popAnimation;

  @override
  void initState() {
    super.initState();

    popAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween(begin: 0, end: 1.1),
        weight: 40,
      ),
      TweenSequenceItem(
        tween: Tween(begin: 1.1, end: 1),
        weight: 60,
      ),
    ]).animate(_animationController);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
        animation: _animationController,
        builder: (_, __) {
          return AnimatedScale(
            duration: const Duration(milliseconds: 100),
            scale: widget.enableAnimation ? _animationController.value : 1,
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppColors.of(context).colorTone3,
                  width: 2,
                ),
              ),
              child: Center(
                child: AutoSizeText(
                  widget.node?.letter.toUpperCase() ?? '',
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    fontSize: 40.sp,
                  ),
                  maxFontSize: 40,
                ),
              ),
            ),
          );
        });
  }
}
