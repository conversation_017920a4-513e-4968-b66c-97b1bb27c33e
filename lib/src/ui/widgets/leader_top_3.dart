import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/ui/widgets/app_avatar.dart';

class LeaderTop3 extends StatelessWidget {
  final String? playerName;
  final int score;
  final int place;
  final String? avatarUrl;
  LeaderTop3({
    Key? key,
    this.playerName,
    required this.score,
    required this.place,
    this.avatarUrl,
  }) : super(key: key);

  final List<double> standSize = [47.73, 31.23, 26.01];

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.leaderboardTopThreeBorderColor,
            ),
            padding: EdgeInsets.all(4),
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
              ),
              child: AppAvatar(
                size: 32,
                iconSize: 32 - 7,
                photoURL: avatarUrl,
              ),
            ),
          ),
          SizedBox(
            height: 10,
          ),
          AutoSizeText(
            playerName ?? '',
            style: TextStyle(
              color: AppColors.of(context).colorTone1,
              fontWeight: FontWeight.w700,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            NumberFormat.compact().format(score),
            style: TextStyle(
              color: AppColors.of(context).colorTone1,
            ),
          ),
          SizedBox(
            height: 20,
          ),
          Stack(
            alignment: Alignment.topCenter,
            clipBehavior: Clip.none,
            children: [
              Container(
                height: standSize[place - 1],
                width: 62,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(4)),
                    shape: BoxShape.rectangle,
                    color: AppColors.leaderboardTopThreePlaceStandBgColor
                        .withOpacity(.5)),
              ),
              Positioned(
                top: -18,
                child: Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColors.leaderboardTopThreePlaceBgColor),
                  child: Center(
                    child: Text(
                      place.toString(),
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}
