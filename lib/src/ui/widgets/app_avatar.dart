import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:wordle/src/ui/widgets/dynamic_badge.dart';

class AppAvatar extends StatelessWidget {
  final double size;
  final double iconSize;
  final String? photoURL;
  final bool? isOnline;
  final Color? backgroundColor;
  const AppAvatar({
    Key? key,
    required this.size,
    required this.iconSize,
    this.photoURL,
    this.isOnline,
    this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DynamicBadge(
      badgeSize: isOnline == null ? 0 : 12,
      positionBottom: 0,
      positionLeft: 0,
      color: isOnline == true ? Colors.greenAccent : Colors.orange,
      child: CircleAvatar(
        radius: size,
        backgroundColor: backgroundColor ?? Color(0xff757474),
        backgroundImage: photoURL != null && photoURL!.isNotEmpty
            ? NetworkImage(photoURL!)
            : null,
        onBackgroundImageError: photoURL != null && photoURL!.isNotEmpty
            ? (_, __) {
                log('Error Loading image');
              }
            : null,
        child: (photoURL != null && photoURL!.isNotEmpty)
            ? null
            : FaIcon(
                FontAwesomeIcons.userAlt,
                color: Colors.white,
                size: iconSize,
              ),
      ),
    );
  }
}
