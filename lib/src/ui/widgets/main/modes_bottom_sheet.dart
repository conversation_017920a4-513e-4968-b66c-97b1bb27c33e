import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/domain/main/stores/main_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/ui/views/daily_challenge/daily_challenge_view.dart';
import 'package:wordle/src/ui/views/unlimited_challenge/unlimited_challenge_view.dart';
import 'package:wordle/src/ui/widgets/main/mode_option.dart';

class ModesBottomSheet extends StatelessWidget {
  ModesBottomSheet({Key? key}) : super(key: key);
  final _mainStore = ServiceLocator.locate<MainStore>();

  bool isActive(String routeName) => _mainStore.currentRouteName == routeName;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 10),
                Text(
                  'GAME MODES',
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 30),
                Observer(builder: (context) {
                  return Column(
                    children: [
                      ModeOption(
                        isActive: isActive(DailyChallengeView.routeName),
                        label: 'DAILY',
                        onTap: () {
                          _mainStore.currentRouteName =
                              DailyChallengeView.routeName;
                          AppRouter.pop();
                          AppRouter.pushReplacement(
                              DailyChallengeView.routeName);
                        },
                      ),
                      ModeOption(
                        isActive: isActive(UnlimitedChallengeView.routeName),
                        label: 'UNLIMITED',
                        onTap: () {
                          _mainStore.currentRouteName =
                              UnlimitedChallengeView.routeName;
                          AppRouter.pop();
                          AppRouter.pushReplacement(
                            UnlimitedChallengeView.routeName,
                          );
                        },
                      ),
                    ],
                  );
                }),
                const SizedBox(height: 50),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
