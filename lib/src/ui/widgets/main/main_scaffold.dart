import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/utils/env.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';
import 'package:wordle/src/ui/theme/custom_theme.dart';
import 'package:wordle/src/ui/widgets/how_to_modal.dart';

class MainScaffold extends StatelessWidget {
  final Widget? title;
  final Widget? body;
  final void Function()? onStatsPressed;
  final List<Widget> actions;
  final Widget? floatingActionButton;

  MainScaffold({
    Key? key,
    this.title,
    this.body,
    this.onStatsPressed,
    this.actions = const [],
    this.floatingActionButton,
  }) : super(key: key);

  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();

  void showHowToModal(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => HowToModal(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.of(context).colorTone7,
      appBar: AppBar(
        centerTitle: false,
        titleSpacing: -10,
        leading: IconButton(
          onPressed: () {
            AppRouter.pop();
          },
          icon: Icon(
            Icons.arrow_back_ios_new_outlined,
            size: 19,
            color: CustomTheme.getBlackIconColor(context),
          ),
        ),
        iconTheme: IconThemeData(color: AppColors.of(context).colorTone2),
        title: title ??
            AutoSizeText(
              env(EnvKey.PLATFORM_APP_NAME),
              style: TextStyle(
                color: AppColors.of(context).colorTone1,
              ),
              maxLines: 1,
              minFontSize: 10,
            ),
        actions: [
          IconButton(
            visualDensity: VisualDensity(
              horizontal: -4.0,
            ),
            padding: EdgeInsets.zero,
            tooltip: 'How To Play',
            icon: Container(
              height: 24,
              width: 24,
              padding: EdgeInsets.all(4),
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppColors.boardBoxBorderColor,
                ),
                shape: BoxShape.circle,
              ),
              child: SvgPicture.asset(
                AppImages.helpFilledIcon,
                color: CustomTheme.getBlackIconColor(context),
              ),
            ),
            onPressed: () {
              _eventLogger.log(Events.helpClicked);
              showHowToModal(context);
            },
          ),
          Padding(
            padding: const EdgeInsets.only(right: 4),
            child: IconButton(
              tooltip: 'Statistics',
              visualDensity: VisualDensity(
                horizontal: -2.0,
              ),
              padding: EdgeInsets.symmetric(horizontal: 2),
              icon: Container(
                height: 24,
                width: 24,
                padding: EdgeInsets.all(4),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.boardBoxBorderColor,
                  ),
                  shape: BoxShape.circle,
                ),
                child: SvgPicture.asset(
                  AppImages.statsFilledIcon,
                  color: CustomTheme.getBlackIconColor(context),
                ),
              ),
              onPressed: onStatsPressed,
            ),
          ),
          ...actions
        ],
      ),
      body: body,
      floatingActionButton: floatingActionButton,
    );
  }
}
