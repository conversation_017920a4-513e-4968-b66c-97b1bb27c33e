import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';

class ModeOption extends StatelessWidget {
  final bool isActive;
  final String label;
  final void Function()? onTap;
  const ModeOption({
    Key? key,
    required this.isActive,
    required this.label,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(10),
      child: InkWell(
        onTap: !isActive ? onTap : null,
        // splashColor: AppColors.of(context).colorTone1.withOpacity(0.2),
        child: Ink(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: Container(
                  height: 66,
                  decoration: isActive
                      ? BoxDecoration(
                          color: AppColors.of(context).correct,
                          borderRadius: BorderRadius.circular(4),
                        )
                      : BoxDecoration(
                          border: Border.all(
                            color: AppColors.of(context).colorTone4,
                            width: 2,
                          ),
                          borderRadius: BorderRadius.circular(4),
                        ),
                  child: Center(
                    child: Text(
                      label,
                      textAlign: TextAlign.center,
                      style: isActive
                          ? TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.w700,
                            )
                          : TextStyle(
                              color: AppColors.of(context).colorTone1,
                              fontSize: 20,
                              fontWeight: FontWeight.w700,
                            ),
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
