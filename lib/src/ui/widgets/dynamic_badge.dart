import 'package:flutter/material.dart';

class DynamicBadge extends StatelessWidget {
  final Widget child;
  final double? positionTop;
  final double? positionBottom;
  final double? positionRight;
  final double? positionLeft;
  final double? badgeSize;
  final Color? color;

  const DynamicBadge({
    Key? key,
    required this.child,
    this.positionBottom,
    this.positionLeft,
    this.positionRight,
    this.positionTop,
    this.badgeSize,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      child,
      Positioned(
        // draw a red marble
        top: positionTop,
        right: positionRight,
        bottom: positionBottom,
        left: positionLeft,
        child: badgeSize == 0
            ? SizedBox.shrink()
            : Icon(
                Icons.brightness_1,
                size: badgeSize,
                color: color,
              ),
      )
    ]);
  }
}
