import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/domain/main/stores/main_store.dart';

class EntranceBannerAd extends StatefulWidget {
  const EntranceBannerAd({Key? key}) : super(key: key);

  @override
  State<EntranceBannerAd> createState() => _EntranceBannerAdState();
}

class _EntranceBannerAdState extends State<EntranceBannerAd> {
  late BannerAd _ad;
  bool _isLoaded = false;
  final _mainStore = ServiceLocator.locate<MainStore>();
  final List<ReactionDisposer> reactionDisposers = [];

  @override
  void initState() {
    super.initState();
    reactionDisposers.add(
      reaction<bool>((_) => (_mainStore.isSubscribed || _mainStore.isPlayPass),
          (isSubscribed) {
        if (!isSubscribed) {
          _loadAd();
        } else {
          setState(() {
            _isLoaded = false;
          });
        }
      }),
    );
  }

  @override
  void dispose() {
    for (var reactionDisposer in reactionDisposers) {
      reactionDisposer();
    }

    _ad.dispose();

    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_mainStore.isSubscribed && !_mainStore.isPlayPass) {
      _loadAd();
    } else {
      setState(() {
        _isLoaded = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoaded) {
      return SafeArea(
        child: SizedBox(
          height: _ad.size.height.toDouble(),
          width: _ad.size.width.toDouble(),
          child: AdWidget(ad: _ad),
        ),
      );
    } else {
      return SizedBox.shrink();
    }
  }

  String _getAdUnitId() {
    return "ca-app-pub-3577719288227636/9175999821";
  }

  Future<void> _loadAd() async {
    // Get an AnchoredAdaptiveBannerAdSize before loading the ad.
    final AnchoredAdaptiveBannerAdSize? size =
        await AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(
            MediaQuery.of(context).size.width.truncate());

    if (size == null) {
      log('Unable to get height of anchored banner.');
      return;
    }

    _ad = BannerAd(
      adUnitId: _getAdUnitId(),
      size: size,
      request: AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (Ad ad) {
          // log('$ad loaded: ${ad.responseInfo}');
          setState(() {
            // When the ad is loaded, get the ad size and use it to set
            // the height of the ad container.
            _ad = ad as BannerAd;
            _isLoaded = true;
          });
        },
        onAdFailedToLoad: (Ad ad, LoadAdError error) {
          log('Anchored adaptive banner failedToLoad: $error');
          ad.dispose();
        },
        onPaidEvent: (ad, valueMicros, precision, currencyCode) {
          MethodChannel platform =
              MethodChannel('com.chinloyal.wordle/channel');
          platform.invokeMethod('logRevenueEventInSingularAndFirebase', {
            'value': valueMicros / 1000000,
            'precision': precision.name,
            'currency': currencyCode,
            'adSource':
                ad.responseInfo?.loadedAdapterResponseInfo?.adSourceName ?? "",
            'adSourceInstance': ad.responseInfo?.loadedAdapterResponseInfo
                    ?.adSourceInstanceName ??
                "",
            'adUnit': "banner",
          });
        },
      ),
    );
    return _ad.load();
  }
}
