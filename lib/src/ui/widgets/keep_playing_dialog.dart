import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wordle/src/core/strings/app_string.dart';
import 'package:wordle/src/ui/widgets/reusable_dialog.dart';

import '../../core/constants/app_colors.dart';
import '../../core/constants/app_images.dart';
import '../../core/utils/service_locator.dart';
import '../../domain/unlimited_challenge/stores/unlimited_challenge_store.dart';
import '../../services/stats_service.dart';
import '../theme/app_theme.dart';
import '../theme/custom_theme.dart';
import '../theme/custom_theme_text.dart';

class KeepPlayingDialog extends StatelessWidget {
  KeepPlayingDialog(
      {super.key,
      required this.shouldShowKeepStreak,
      required this.parentContext});

  final bool shouldShowKeepStreak;
  final BuildContext parentContext;

  final unlimitedChallengeStore =
      ServiceLocator.locate<UnlimitedChallengeStore>();
  final statsService = ServiceLocator.locate<StatsService>();

  @override
  Widget build(BuildContext context) {
    return reusableDialog(
      context: context,
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 24),
      showCancelButton: false,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 8,
          ),
          AutoSizeText(
            !shouldShowKeepStreak
                ? AppStrings.outOfTriesText
                : AppStrings.keepStreakMessage,
            maxLines: 1,
            style: CustomThemeText.stymieTextBlack(
              context: context,
              fontSize: 32,
              fontWeight: FontWeight.w900,
            ),
          ),
          !shouldShowKeepStreak
              ? Column(
                  children: [
                    SizedBox(
                      height: 8,
                    ),
                    Text(
                      AppStrings.getOneMoreTryText,
                      style: CustomThemeText.urbanistTextBlack(
                        context: context,
                        fontSize: 20,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                )
              : SizedBox(
                  height: 20,
                ),
          SizedBox(
            height: 24,
          ),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () {
                    unlimitedChallengeStore.onGiveUpClick(
                        parentContext: parentContext);
                  },
                  child: Container(
                    height: 176,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppColors.boardBoxBorderColor,
                        width: 4,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          height: 10,
                        ),
                        Text(
                          AppStrings.giveUpText,
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Spacer(),
                        SvgPicture.asset(
                          CustomTheme.isLightMode(context)
                              ? AppImages.giveUpIcon
                              : AppImages.giveUpBlackThemeIcon,
                          height: 42,
                        ),
                        Spacer(),
                        SizedBox(
                          height: 40,
                          width: 96,
                          child: ElevatedButton(
                            onPressed: () {
                              unlimitedChallengeStore.onGiveUpClick(
                                  parentContext: parentContext);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.of(context).saleGreen,
                              padding: EdgeInsets.only(left: 20, right: 12),
                              elevation: 0,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  AppStrings.nextText,
                                  style: buttonTextStyle(context: context),
                                ),
                                SizedBox(
                                  width: 6,
                                ),
                                Icon(
                                  Icons.arrow_forward_ios_outlined,
                                  size: 15,
                                  color: CustomTheme.getWhiteIconColor(context),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 12,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(
                width: 12,
              ),
              Expanded(
                child: InkWell(
                  onTap: !shouldShowKeepStreak
                      ? () {
                          unlimitedChallengeStore.onUnlockClick(
                              context: context);
                        }
                      : () {
                          unlimitedChallengeStore.onKeepItStreakClick(
                              context: parentContext);
                        },
                  child: Container(
                    height: 176,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppColors.boardBoxBorderColor,
                        width: 4,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: !shouldShowKeepStreak
                        ? Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                AppStrings.unlockText,
                                style: CustomThemeText.urbanistTextBlack(
                                  context: context,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              AutoSizeText(
                                AppStrings.oneMoreGuessText,
                                maxLines: 1,
                                style: CustomThemeText.urbanistTextBlack(
                                  context: context,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(
                                height: 12,
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                    left: MediaQuery.of(context).size.width *
                                        .04),
                                child: SvgPicture.asset(
                                  CustomTheme.isLightMode(context)
                                      ? AppImages.oneMoreGuessIcon
                                      : AppImages.oneMoreGuessBlackThemeIcon,
                                  height: 62,
                                ),
                              ),
                              SizedBox(
                                height: 40,
                                width: 115,
                                child: ElevatedButton(
                                  onPressed: () {
                                    unlimitedChallengeStore.onUnlockClick(
                                        context: context);
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        AppColors.of(context).saleGreen,
                                    elevation: 0,
                                    padding: EdgeInsets.only(left: 3, right: 0),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Visibility(
                                        visible:
                                            unlimitedChallengeStore.canShowAd,
                                        //  maintainSize: true,
                                        child: Row(
                                          children: [
                                            SvgPicture.asset(
                                              AppImages.unlockAdIcon,
                                              height: 30,
                                            ),
                                            SizedBox(
                                              width: 4,
                                            )
                                          ],
                                        ),
                                      ),
                                      Text(
                                        AppStrings.unlockText,
                                        maxLines: 1,
                                        style:
                                            buttonTextStyle(context: context),
                                      ),
                                      Visibility(
                                        visible:
                                            unlimitedChallengeStore.canShowAd,
                                        child: SizedBox(
                                          width: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          )
                        : Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                AppStrings.keepYourText,
                                style: CustomThemeText.urbanistTextBlack(
                                  context: context,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              SizedBox(
                                height: 4,
                              ),
                              AutoSizeText(
                                "${AppStrings.keepYourText} ${unlimitedChallengeStore.currentStreak}\n${AppStrings.winStreakText}",
                                textAlign: TextAlign.center,
                                style: CustomThemeText.urbanistTextBlack(
                                  context: context,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              SizedBox(
                                height: 8,
                              ),
                              SvgPicture.asset(
                                CustomTheme.isLightMode(context)
                                    ? AppImages.keepSteakIcon
                                    : AppImages.keepSteakBlackThemeIcon,
                                height: 41,
                              ),
                              SizedBox(
                                height: 11,
                              ),
                              SizedBox(
                                height: 40,
                                width: 115,
                                child: ElevatedButton(
                                  onPressed: () {
                                    unlimitedChallengeStore.onKeepItStreakClick(
                                        context: parentContext);
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        AppColors.of(context).saleGreen,
                                    elevation: 0,
                                    padding: EdgeInsets.only(left: 3, right: 0),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Visibility(
                                        visible:
                                            unlimitedChallengeStore.canShowAd,
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            SvgPicture.asset(
                                              AppImages.unlockAdIcon,
                                              height: 32,
                                            ),
                                            SizedBox(
                                              width: 8,
                                            ),
                                          ],
                                        ),
                                      ),
                                      Text(
                                        AppStrings.keepItText.toUpperCase(),
                                        maxLines: 1,
                                        style:
                                            buttonTextStyle(context: context),
                                      ),
                                      Visibility(
                                        visible:
                                            unlimitedChallengeStore.canShowAd,
                                        child: SizedBox(
                                          width: 16,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 6,
          ),
        ],
      ),
    );
  }
}
