import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';

import '../../core/constants/font_family.dart';

class ReusableDailyNotStartedElement extends StatelessWidget {
  final int letterCount;
  final VoidCallback onTap;

  const ReusableDailyNotStartedElement({
    super.key,
    required this.letterCount,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 17,
            vertical: 14,
          ),
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(26),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AutoSizeText(
                "$letterCount letter",
                maxLines: 1,
                maxFontSize: 15,
                minFontSize: 10,
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: FontFamily.urbanist,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
