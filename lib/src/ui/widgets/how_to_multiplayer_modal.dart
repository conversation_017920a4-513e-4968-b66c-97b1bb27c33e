import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/utils/env.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/ui/widgets/player_vs_player_example.dart';

class HowToMultiplayerModal extends StatelessWidget {
  HowToMultiplayerModal({Key? key}) : super(key: key);
  final _localStorage = ServiceLocator.locate<LocalStorage>();

  @override
  Widget build(BuildContext context) {
    _localStorage.saveBool(LSKey.isFirstMultiplayerLaunch, false);

    return AlertDialog(
      scrollable: true,
      backgroundColor: AppColors.of(context).colorTone7,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(5.0),
      ),
      title: const Text(
        'HOW TO PLAY',
        style: TextStyle(fontWeight: FontWeight.w600),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      content: Column(
        children: [
          Text(
              '''Guess the ${env(EnvKey.PLATFORM_APP_NAME)} before your opponent.

You and your opponent will receive the same puzzle with the same solution. The first person
to guess the word wins.
      '''),
          const Divider(),
          Text('''You both will receive 2 minutes to make a guess

'''),
          PlayerVsPlayerExample(),
          SizedBox(height: 10),
          Text('The timer will hightlight to show whose turn it is.'),
          SizedBox(height: 10),
          Row(
            children: const [
              Text('Each match will cost '),
              Text(
                '10 coins.',
                style: TextStyle(fontWeight: FontWeight.w700),
              )
            ],
          ),
        ],
      ),
      actions: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              TextButton(
                onPressed: () {
                  AppRouter.pop();
                },
                child: const Text(
                  'OK',
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }
}
