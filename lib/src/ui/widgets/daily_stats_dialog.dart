import 'package:auto_size_text/auto_size_text.dart';
import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/constants/game_result.dart';
import 'package:wordle/src/core/helpers/toast.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/models/difficulty_config.dart';
import 'package:wordle/src/data/models/ordinal_guess.dart';
import 'package:wordle/src/domain/daily_challenge/stores/daily_challenge_store.dart';
import 'package:wordle/src/domain/daily_challenge/stores/timer_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';
import 'package:wordle/src/services/stats_service.dart';
import 'package:wordle/src/ui/theme/app_theme.dart';
import 'package:wordle/src/ui/theme/custom_theme.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';
import 'package:wordle/src/ui/views/unlimited_challenge/unlimited_challenge_view.dart';
import 'package:wordle/src/ui/widgets/reusable_check_icon.dart';
import 'package:wordle/src/ui/widgets/reusable_dialog.dart';
import 'package:wordle/src/ui/widgets/show_loading.dart';

import '../../core/constants/app_images.dart';
import '../../core/constants/font_family.dart';
import '../../core/constants/map_const.dart';
import '../../core/utils/env.dart';
import '../../data/models/difficulty_item_model.dart';
import '../../domain/main/stores/main_store.dart';
import 'dialogs/confirm_dialog.dart';

class DailyStatsDialog extends StatefulWidget {
  const DailyStatsDialog({
    Key? key,
    required this.heading,
    required this.parentContext,
    required this.letterCount,
    this.isGameCompleted = false,
  }) : super(key: key);

  final String heading;
  final BuildContext parentContext;
  final int letterCount;
  final bool isGameCompleted;

  @override
  State<DailyStatsDialog> createState() => _DailyStatsDialogState();
}

class _DailyStatsDialogState extends State<DailyStatsDialog>
    with SingleTickerProviderStateMixin {
  final statsService = ServiceLocator.locate<StatsService>();
  final timerStore = ServiceLocator.locate<TimerStore>();
  final dailyChallengeStore = ServiceLocator.locate<DailyChallengeStore>();
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final _mainStore = ServiceLocator.locate<MainStore>();

  final double scoreSize = 20;
  double avg = 0;
  double batterThanPercentage = 0;
  late TabController tabController;
  late DifficultyConfig selectedDifficultyConfig;

  final String moreGameSummaryIcon = '';

  Future<void> loadStatsAndCalculateScore() async {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      showLoading(widget.parentContext);

      await statsService.loadStats(
        parentContext: widget.parentContext,
        gameType: selectedDifficultyConfig.dailyStatsType.gameType,
        gameName: selectedDifficultyConfig.dailyStatsType.gameName,
      );

      if (widget.isGameCompleted) {
        await statsService.getGameSummaryNetworkCall(
          context: widget.parentContext,
          gameType: selectedDifficultyConfig.dailyStatsType.gameType,
          gameName: selectedDifficultyConfig.dailyStatsType.gameName,
        );
      }
      calculateAvgScore();
      hideLoading(widget.parentContext);
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void initState() {
    super.initState();
    selectedDifficultyConfig =
        difficultyItemsMap[widget.letterCount]!.difficultyConfig;
    tabController = TabController(length: 2, vsync: this);
    loadStatsAndCalculateScore();
  }

  List<charts.Series<OrdinalPercentage, String>> createGameSummaryData() {
    final ordinalPercentage =
        statsService.stats.gameSummary.entries.map<OrdinalPercentage>((e) {
      return OrdinalPercentage(e.key, e.value);
    }).toList();
    return [
      charts.Series<OrdinalPercentage, String>(
        id: 'dailyGameSummary',
        data: ordinalPercentage,
        domainFn: (ordinalGuess, _) {
          if (ordinalGuess.row == ordinalPercentage.length) {
            return moreGameSummaryIcon;
          } else {
            return ordinalGuess.row.toString();
          }
        },
        measureFn: (ordinalPercentage, _) => ordinalPercentage.percentage,
        labelAccessorFn: (ordinalPercentage, _) =>
            '${ordinalPercentage.percentage.toStringAsFixed(0)}%',
        // Use string interpolation for label
        colorFn: (ordinalGuess, __) {
          if (dailyChallengeStore.submittedRows.length == ordinalGuess.row &&
                  dailyChallengeStore.gameResult == GameResult.won ||
              (ordinalGuess.row == statsService.stats.gameSummary.length &&
                  dailyChallengeStore.gameResult == GameResult.lost)) {
            return AppColors.brightness == Brightness.light
                ? charts.Color(a: 255, r: 106, g: 170, b: 100)
                : charts.Color(a: 255, r: 83, g: 141, b: 78);
          } else {
            return charts.Color.fromHex(code: "#434142");
          }
        },
        outsideLabelStyleAccessorFn: (_, __) {
          final color = AppColors.brightness == Brightness.light
              ? charts.Color.fromHex(code: '#1a1a1b')
              : charts.Color.fromHex(code: '#d7dadc');

          return charts.TextStyleSpec(
            color: color,
            fontSize: 15,
            fontWeight: "BOLD",
            fontFamily: FontFamily.urbanist,
          );
        },
        insideLabelStyleAccessorFn: (_, __) {
          return charts.TextStyleSpec(
            color: charts.MaterialPalette.white,
            fontSize: 15,
            fontWeight: "BOLD",
            fontFamily: FontFamily.urbanist,
          );
        },
      )
    ];
  }

  List<charts.Series<OrdinalGuess, String>> createGuessDistributionData() {
    final ordinalGuesses =
        statsService.guessDistribution.entries.map<OrdinalGuess>((e) {
      return OrdinalGuess(e.key, e.value);
    }).toList();

    return [
      charts.Series<OrdinalGuess, String>(
        id: 'dailyGuessDistribution',
        data: ordinalGuesses,
        domainFn: (ordinalGuess, _) => ordinalGuess.row.toString(),
        measureFn: (ordinalGuess, _) => ordinalGuess.guesses,
        labelAccessorFn: (ordinalGuess, _) => ordinalGuess.guesses.toString(),
        colorFn: (ordinalGuess, __) {
          if (dailyChallengeStore.submittedRows.length == ordinalGuess.row &&
              dailyChallengeStore.gameResult == GameResult.won) {
            return AppColors.brightness == Brightness.light
                ? charts.Color(a: 255, r: 106, g: 170, b: 100)
                : charts.Color(a: 255, r: 83, g: 141, b: 78);
          } else {
            return charts.Color.fromHex(code: "#434142");
          }
        },
        outsideLabelStyleAccessorFn: (_, __) {
          final color = AppColors.brightness == Brightness.light
              ? charts.Color.fromHex(code: '#1a1a1b')
              : charts.Color.fromHex(code: '#d7dadc');

          return charts.TextStyleSpec(
            color: color,
            fontSize: 15,
            fontWeight: "BOLD",
            fontFamily: FontFamily.urbanist,
          );
        },
        insideLabelStyleAccessorFn: (_, __) {
          return charts.TextStyleSpec(
            color: charts.MaterialPalette.white,
            fontSize: 15,
            fontWeight: "BOLD",
            fontFamily: FontFamily.urbanist,
          );
        },
      )
    ];
  }

  charts.Color getYAxisColor() {
    return AppColors.brightness == Brightness.light
        ? charts.Color.fromHex(code: '#1a1a1b')
        : charts.Color.fromHex(code: '#d7dadc');
  }

  void calculateAvgScore() {
    int length = 0;
    double sum = 0;

    for (var element in statsService.guessDistribution.entries) {
      if (element.value != 0) {
        length = length + element.value;
      }
      sum = sum + (element.value * element.key);
    }
    avg = length > 0 ? sum / length : 0;

    if (widget.isGameCompleted) {
      int myGuessedRowNumber = dailyChallengeStore.submittedRows.length;
      if (myGuessedRowNumber != 0 &&
          dailyChallengeStore.gameResult == GameResult.won) {
        statsService.stats.gameSummary.forEach(
          (key, value) {
            if (key > myGuessedRowNumber) {
              batterThanPercentage = batterThanPercentage + value;
            }
          },
        );
      } else {
        batterThanPercentage = 0;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(builder: (context, setState) {
      return reusableDialog(
        context: context,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: 28,
            ),
            Center(
              child: AutoSizeText(
                widget.heading,
                maxLines: 1,
                style: CustomThemeText.stymieTextBlack(
                    context: context,
                    fontSize: 32,
                    fontWeight: FontWeight.w900),
              ),
            ),
            Visibility(
              visible: widget.isGameCompleted,
              child: Column(
                children: [
                  SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ReusableCheckIcon(
                        backgroundColor: AppColors.green,
                        iconColor: AppColors.white,
                      ),
                      Text(
                        'Completed ${widget.letterCount} letter!',
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: 20,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(height: MediaQuery.of(context).size.height * .03),
            Text(
              'STATISTICS',
              style: CustomThemeText.urbanistTextBlack(
                context: context,
                fontSize: 14,
                fontWeight: FontWeight.w700,
              ),
            ),
            SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Flexible(
                  child: Column(
                    children: [
                      AutoSizeText(
                        statsService.gamesPlayed,
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: scoreSize,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      Text(
                        'Played',
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Flexible(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AutoSizeText(
                        statsService.winPercentage,
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: scoreSize,
                          fontWeight: FontWeight.w700,
                        ),
                        maxLines: 1,
                      ),
                      Text(
                        'Win %',
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Flexible(
                  child: Column(
                    children: [
                      AutoSizeText(
                        statsService.currentStreak,
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: scoreSize,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      Text(
                        'Current Streak',
                        softWrap: true,
                        maxLines: 2,
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                Flexible(
                  child: Column(
                    children: [
                      AutoSizeText(
                        statsService.maxStreak,
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: scoreSize,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      Text(
                        'Max Streak',
                        softWrap: true,
                        maxLines: 2,
                        style: CustomThemeText.urbanistTextBlack(
                          context: context,
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: MediaQuery.of(context).size.height * .03),
            widget.isGameCompleted
                ? tabBarWidget(context, setState)
                : singleGuessDistributionWidget(context),
            if (dailyChallengeStore.hasGameEnded) ...[
              SizedBox(height: MediaQuery.of(context).size.height * .025),
              Visibility(
                visible: !widget.isGameCompleted,
                child: Center(
                  child: Observer(builder: (_) {
                    return Column(
                      children: [
                        Text('NEXT ${env(EnvKey.PLATFORM_APP_NAME)} IN',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: CustomTheme.isLightMode(context)
                                  ? AppColors.disabled
                                  : AppColors.white,
                              fontFamily: FontFamily.urbanist,
                            )),
                        SizedBox(
                          height: 4,
                        ),
                        Text(
                          timerStore.timeToNextWord,
                          style: CustomThemeText.urbanistTextBlack(
                            context: context,
                            fontSize: 24,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        SizedBox(
                          height: 20,
                        ),
                      ],
                    );
                  }),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      _eventLogger.log(Events.shareClicked);
                      Clipboard.setData(
                        ClipboardData(
                          text: statsService.getShareableResults(),
                        ),
                      );
                      toast(
                          context: context,
                          msg: 'Copied results to clipboard.');
                    },
                    icon: Icon(
                      Icons.share,
                      color: CustomTheme.getBlackIconColor(context),
                      size: 20,
                    ),
                    label: Text(
                      'SHARE',
                      style: CustomThemeText.urbanistTextBlack(
                        context: context,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: OutlinedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      side: BorderSide(
                        width: 1.0,
                        color: CustomTheme.getBlackBorderColor(context),
                      ),
                      elevation: 0,
                    ),
                  ),
                  SizedBox(width: 8),
                  SizedBox(
                    height: 40,
                    width: 124,
                    child: Material(
                      borderRadius: BorderRadius.circular(24),
                      child: ElevatedButton(
                        onPressed: () {
                          AppRouter.pop();
                          AppRouter.push(
                            UnlimitedChallengeView.routeName,
                            arguments: UnlimitedChallengeArgs(
                              letter: selectedDifficultyConfig.lettersCount,
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.of(context).saleGreen,
                          elevation: 0,
                          padding: EdgeInsets.zero,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '${widget.letterCount} letter'.toUpperCase(),
                              style: buttonTextStyle(context: context),
                            ),
                            SizedBox(
                              width: 6,
                            ),
                            Icon(
                              Icons.arrow_forward_ios_outlined,
                              size: 15,
                              color: CustomTheme.getWhiteIconColor(context),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 16,
              ),
            ],
          ],
        ),
      );
    });
  }

  Column singleGuessDistributionWidget(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 16,
        ),
        Padding(
          padding: const EdgeInsets.only(left: 4),
          child: Row(
            children: [
              AutoSizeText(
                'GUESS DISTRIBUTION',
                style: CustomThemeText.urbanistTextBlack(
                  context: context,
                  fontSize: 14,
                  fontWeight: FontWeight.w700,
                ),
              ),
              SizedBox(width: 6),
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(
                      color: CustomTheme.getBlackBorderColor(context)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Text(
                      'Avg.Score',
                      style: CustomThemeText.urbanistTextBlack(
                        context: context,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(width: 4),
                    Text(
                      avg.toStringAsFixed(2),
                      style: CustomThemeText.urbanistTextBlack(
                        context: context,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: MediaQuery.of(context).size.height * .25,
          child: charts.BarChart(
            createGuessDistributionData(),
            vertical: false,
            barRendererDecorator: charts.BarLabelDecorator<String>(
              insideLabelStyleSpec: charts.TextStyleSpec(
                fontSize: 12,
                color: charts.MaterialPalette.black,
              ),
              outsideLabelStyleSpec: charts.TextStyleSpec(
                fontSize: 12,
                color: charts.MaterialPalette.black,
              ),
              labelPosition: charts.BarLabelPosition.auto,
              labelAnchor: charts.BarLabelAnchor.end,
            ),
            primaryMeasureAxis: charts.NumericAxisSpec(
              renderSpec: charts.NoneRenderSpec(),
            ),
            domainAxis: charts.OrdinalAxisSpec(
              renderSpec: charts.SmallTickRendererSpec(
                labelStyle: charts.TextStyleSpec(
                  fontSize: 15,
                  fontWeight: "BOLD",
                  fontFamily: FontFamily.urbanist,
                  color: getYAxisColor(),
                ),
                lineStyle: charts.LineStyleSpec(
                  color:
                      charts.ColorUtil.fromDartColor(AppColors.nonActiveGrey),
                  thickness: 1,
                ),
              ),
            ),
          ),
        ),
        avgScoreBox(context),
      ],
    );
  }

  Column tabBarWidget(BuildContext context, StateSetter setState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TabBar(
          dividerColor: AppColors.filledGrey,
          isScrollable: true,
          physics: NeverScrollableScrollPhysics(),
          labelPadding: EdgeInsets.only(right: 16),
          tabAlignment: TabAlignment.start,
          dividerHeight: 1,
          indicatorWeight: 4,
          indicatorSize: TabBarIndicatorSize.label,
          indicatorPadding: EdgeInsets.zero,
          controller: tabController,
          padding: EdgeInsets.zero,
          unselectedLabelColor:
              CustomTheme.getBlackIconColor(context).withOpacity(.55),
          labelStyle: CustomThemeText.urbanistTextBlack(
            context: context,
            fontSize: 15,
            fontWeight: FontWeight.w700,
          ),
          unselectedLabelStyle: CustomThemeText.urbanistTextBlack(
            context: context,
            fontSize: 15,
            fontWeight: FontWeight.w700,
          ),
          tabs: [
            Tab(
              text: 'Game Summary',
            ),
            Tab(
              text: 'Guess Distribution',
            ),
          ],
        ),
        SizedBox(
          height: MediaQuery.of(context).size.height * .25 +
              (batterThanPercentage > 0 ? 75 : 50),
          child: Column(
            children: [
              Flexible(
                child: TabBarView(
                  controller: tabController,
                  children: [
                    Column(
                      children: [
                        SizedBox(
                          height: 14,
                        ),
                        Text.rich(
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          TextSpan(
                              children: batterThanPercentage > 0
                                  ? [
                                      TextSpan(
                                        text: 'Well done! You did better than ',
                                        style:
                                            CustomThemeText.urbanistTextBlack(
                                          context: context,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      TextSpan(
                                        text:
                                            '${batterThanPercentage.toStringAsFixed(0)}%',
                                        style: TextStyle(
                                          color: AppColors.green,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w800,
                                          fontFamily: FontFamily.urbanist,
                                        ),
                                      ),
                                      TextSpan(
                                        text: ' of all players! ',
                                        style:
                                            CustomThemeText.urbanistTextBlack(
                                          context: context,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      TextSpan(
                                        text:
                                            'This is how you compare to others playing with this word:',
                                        style:
                                            CustomThemeText.urbanistTextBlack(
                                          context: context,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ]
                                  : [
                                      TextSpan(
                                        text:
                                            'This is how you compare to others playing with this word:',
                                        style:
                                            CustomThemeText.urbanistTextBlack(
                                          context: context,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ]),
                        ),
                        SizedBox(
                          height: MediaQuery.of(context).size.height * .25,
                          child: Stack(
                            alignment: Alignment.topCenter,
                            children: [
                              charts.BarChart(
                                createGameSummaryData(),
                                vertical: false,
                                barRendererDecorator:
                                    charts.BarLabelDecorator<String>(
                                  insideLabelStyleSpec: charts.TextStyleSpec(
                                    fontSize: 12,
                                    color: charts.MaterialPalette.black,
                                  ),
                                  outsideLabelStyleSpec: charts.TextStyleSpec(
                                    fontSize: 12,
                                    color: charts.MaterialPalette.black,
                                  ),
                                  labelPosition: charts.BarLabelPosition.auto,
                                  labelAnchor: charts.BarLabelAnchor.end,
                                ),
                                primaryMeasureAxis: charts.NumericAxisSpec(
                                  renderSpec: charts.NoneRenderSpec(),
                                ),
                                domainAxis: charts.OrdinalAxisSpec(
                                  renderSpec: charts.SmallTickRendererSpec(
                                    labelStyle: charts.TextStyleSpec(
                                      fontSize: 15,
                                      fontWeight: "BOLD",
                                      fontFamily: FontFamily.urbanist,
                                      color: getYAxisColor(),
                                    ),
                                    lineStyle: charts.LineStyleSpec(
                                      color: charts.ColorUtil.fromDartColor(
                                          AppColors.nonActiveGrey),
                                      thickness: 1,
                                    ),
                                  ),
                                ),
                              ),
                              Positioned(
                                bottom:
                                    MediaQuery.of(context).size.height * .0275,
                                left: 8,
                                child: SvgPicture.asset(
                                  AppImages.downMoreIcon,
                                  height: 15,
                                  color: CustomTheme.getBlackIconColor(context),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          height: MediaQuery.of(context).size.height * .25,
                          child: charts.BarChart(
                            createGuessDistributionData(),
                            vertical: false,
                            barRendererDecorator:
                                charts.BarLabelDecorator<String>(
                              insideLabelStyleSpec: charts.TextStyleSpec(
                                fontSize: 12,
                                color: charts.MaterialPalette.black,
                              ),
                              outsideLabelStyleSpec: charts.TextStyleSpec(
                                fontSize: 12,
                                color: charts.MaterialPalette.black,
                              ),
                              labelPosition: charts.BarLabelPosition.auto,
                              labelAnchor: charts.BarLabelAnchor.end,
                            ),
                            primaryMeasureAxis: charts.NumericAxisSpec(
                              renderSpec: charts.NoneRenderSpec(),
                            ),
                            domainAxis: charts.OrdinalAxisSpec(
                              renderSpec: charts.SmallTickRendererSpec(
                                labelStyle: charts.TextStyleSpec(
                                  fontSize: 15,
                                  fontWeight: "BOLD",
                                  fontFamily: FontFamily.urbanist,
                                  color: getYAxisColor(),
                                ),
                                lineStyle: charts.LineStyleSpec(
                                  color: charts.ColorUtil.fromDartColor(
                                      AppColors.nonActiveGrey),
                                  thickness: 1,
                                ),
                              ),
                            ),
                          ),
                        ),
                        avgScoreBox(context),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Row avgScoreBox(BuildContext context) {
    return Row(
      children: [
        Visibility(
          visible: widget.isGameCompleted,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            margin: EdgeInsets.only(left: 8),
            decoration: BoxDecoration(
              color: AppColors.filledGrey,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Text(
                  'Avg.Score',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                    fontFamily: FontFamily.urbanist,
                  ),
                ),
                SizedBox(width: 4),
                Text(
                  avg.toStringAsFixed(2),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                    fontFamily: FontFamily.urbanist,
                  ),
                ),
              ],
            ),
          ),
        ),
        TextButton(
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => ConfirmDialog(
                title: 'Reset Stats',
                body:
                    '''Are you sure you want to reset your stats?\nYou  will lose your streak for this difficulty daily mode, and start from zero.''',
                onConfirm: () async {
                  showLoading(widget.parentContext);
                  await statsService.resetStats(
                    parentContext: widget.parentContext,
                    gameType: selectedDifficultyConfig.dailyStatsType.gameType,
                    gameName: selectedDifficultyConfig.dailyStatsType.gameName,
                  );
                  hideLoading(widget.parentContext);
                  dailyChallengeStore.reset(hardReset: true);
                  await dailyChallengeStore.initialise(
                    difficultyConfig: selectedDifficultyConfig,
                    shouldSyncDailyWord: true,
                  );
                  setState(() {});
                  AppRouter.pop();
                  AppRouter.pop();
                  toast(context: context, msg: 'Daily stats reset.');
                },
              ),
            );
          },
          style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 10),
              minimumSize: Size(50, 0),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              alignment: Alignment.centerLeft),
          child: Text(
            'RESET STATS',
            style: CustomThemeText.urbanistTextBlack(
              context: context,
              fontSize: 12,
              fontWeight: FontWeight.w500,
              textDecoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }
}
