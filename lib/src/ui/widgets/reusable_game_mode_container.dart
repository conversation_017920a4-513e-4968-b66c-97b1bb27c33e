import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wordle/src/core/constants/app_colors.dart';
import 'package:wordle/src/core/constants/game_result.dart';
import 'package:wordle/src/data/models/difficulty_item_model.dart';
import 'package:wordle/src/domain/daily_challenge/stores/daily_challenge_store.dart';
import 'package:wordle/src/ui/views/daily_challenge/daily_challenge_view.dart';
import 'package:wordle/src/ui/widgets/reusable_daily_completed_element.dart';
import 'package:wordle/src/ui/widgets/reusable_daily_inprogress_elment.dart';
import 'package:wordle/src/ui/widgets/reusable_daily_pending_element.dart';

import '../../core/constants/app_images.dart';
import '../../core/utils/service_locator.dart';
import '../../domain/main/stores/main_store.dart';
import '../../router/app_router.dart';
import '../theme/custom_theme.dart';
import '../theme/custom_theme_text.dart';

class ReusableGameModeContainer extends StatelessWidget {
  final VoidCallback? onPress;
  final String title;
  final String description;
  final String icon;
  final List<Widget> extraItems;
  final Color color;
  final bool isDaily;

  ReusableGameModeContainer({
    Key? key,
    this.onPress,
    required this.title,
    required this.description,
    required this.extraItems,
    required this.icon,
    required this.color,
    this.isDaily = false,
  }) : super(key: key);

  final _dailyChallengeStore = ServiceLocator.locate<DailyChallengeStore>();
  final mainStore = ServiceLocator.locate<MainStore>();

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Material(
          color: color,
          borderRadius: BorderRadius.circular(15),
          child: InkWell(
            onTap: onPress,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 18,
              ),
              height: isDaily ? 175 : 115,
              width: double.infinity,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Stack(
                        alignment: Alignment.bottomRight,
                        clipBehavior: Clip.none,
                        children: [
                          SvgPicture.asset(
                            icon,
                            color: CustomTheme.getWhiteIconColor(context),
                          ),
                          Positioned(
                            right: -8,
                            bottom: -4,
                            child: Observer(builder: (context) {
                              return Visibility(
                                visible: mainStore.isSpeedMode,
                                child: SvgPicture.asset(
                                  AppImages.watchSpeedIcon,
                                  height: 32,
                                ),
                              );
                            }),
                          ),
                        ],
                      ),
                      SizedBox(
                        width: 16,
                      ),
                      Expanded(
                          child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AutoSizeText(
                            title,
                            maxLines: 1,
                            style: CustomThemeText.stymieTextWhite(
                                context: context,
                                fontSize: 32,
                                fontWeight: FontWeight.w900),
                          ),
                          AutoSizeText(
                            description,
                            maxLines: 2,
                            style: CustomThemeText.urbanistTextWhite(
                                context: context,
                                fontSize: 15,
                                height: 1.15,
                                fontWeight: FontWeight.w500),
                          ),
                        ],
                      )),
                      SizedBox(
                        width: 30,
                      ),
                    ],
                  ),
                  Visibility(
                    visible: isDaily,
                    child: SizedBox(
                      height: 16,
                    ),
                  ),
                  Observer(builder: (context) {
                    return Visibility(
                      visible: isDaily,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          getElement(
                            letterCount: 5,
                            onTap: () {
                              AppRouter.push(
                                DailyChallengeView.routeName,
                                arguments: DailyChallengeArgs(letter: 5),
                              );
                            },
                            status: _dailyChallengeStore.gameStatus5,
                          ),
                          SizedBox(
                            width: 14,
                            child: Divider(
                              color: AppColors.dailyGameTypeBorderColor,
                              thickness: 3,
                            ),
                          ),
                          getElement(
                            letterCount: 6,
                            onTap: () {
                              AppRouter.push(
                                DailyChallengeView.routeName,
                                arguments: DailyChallengeArgs(letter: 6),
                              );
                            },
                            status: _dailyChallengeStore.gameStatus6,
                          ),
                          SizedBox(
                            width: 14,
                            child: Divider(
                              color: AppColors.dailyGameTypeBorderColor,
                              thickness: 3,
                            ),
                          ),
                          getElement(
                            letterCount: 7,
                            onTap: () {
                              AppRouter.push(
                                DailyChallengeView.routeName,
                                arguments: DailyChallengeArgs(letter: 7),
                              );
                            },
                            status: _dailyChallengeStore.gameStatus7,
                          ),
                        ],
                      ),
                    );
                  })
                ],
              ),
            ),
          ),
        ),
        Positioned(
          right: 0,
          top: 0,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: extraItems,
          ),
        )
      ],
    );
  }

  Widget getElement({
    required int letterCount,
    required VoidCallback onTap,
    required GameStatus status,
  }) {
    Widget selectedWidget = ReusableDailyCompletedElement(
      letterCount: letterCount,
      onTap: onTap,
    );

    switch (status) {
      case GameStatus.notStarted:
        selectedWidget = ReusableDailyNotStartedElement(
          letterCount: letterCount,
          onTap: onTap,
        );

        break;
      case GameStatus.inProgress:
        selectedWidget = ReusableDailyInProgressElement(
          letterCount: letterCount,
          onTap: onTap,
        );
        break;
      default:
        selectedWidget = ReusableDailyCompletedElement(
          letterCount: letterCount,
          onTap: onTap,
        );
        break;
    }

    return selectedWidget;
  }
}
