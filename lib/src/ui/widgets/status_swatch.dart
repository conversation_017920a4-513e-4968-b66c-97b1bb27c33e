import 'package:flutter/material.dart';
import 'package:wordle/src/core/http/status.dart';

class StatusSwatch extends StatelessWidget {
  final Status _defaultStatus = Status.pending;
  final Status? status;
  final Widget Function(BuildContext context) pendingBuilder;
  final Widget Function(BuildContext context) doneBuilder;
  final Widget Function(BuildContext context) errorBuilder;
  final Widget Function(BuildContext context) noDataBuilder;

  const StatusSwatch({
    Key? key,
    required this.status,
    required this.pendingBuilder,
    required this.doneBuilder,
    required this.errorBuilder,
    required this.noDataBuilder,
  }) : super(key: key);
  @override
  Widget build(BuildContext context) {
    switch (status ?? _defaultStatus) {
      case Status.pending:
        return pendingBuilder(context);
      case Status.error:
        return errorBuilder(context);
      case Status.done:
        return doneBuilder(context);
      case Status.noData:
        return noDataBuilder(context);
    }
  }
}
