import 'package:flutter/material.dart';

class AppIconButton extends StatelessWidget {
  final Color? backgroundColor;
  final Color? borderColor;
  final Icon icon;
  final VoidCallback? onPress;
  const AppIconButton({
    Key? key,
    this.backgroundColor,
    this.borderColor,
    required this.icon,
    this.onPress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPress,
      child: Container(
        padding: EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: backgroundColor ?? Color(0xFF9E9E9E).withOpacity(0.3),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            width: 7,
            color: borderColor ?? Color(0xFFC8C8C8),
          ),
        ),
        child: icon,
      ),
    );
  }
}
