class GetLeaderBoardModel {
  List<Leaderboard>? leaderboard;

  GetLeaderBoardModel({this.leaderboard});

  GetLeaderBoardModel.fromJson(Map<String, dynamic> json) {
    if (json['leaderboard'] != null) {
      leaderboard = <Leaderboard>[];

      json['leaderboard'].forEach((v) {
        leaderboard!.add(Leaderboard.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (leaderboard != null) {
      data['leaderboard'] = leaderboard!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Leaderboard {
  String? uid;
  int? gamesPlayed;
  String? playerName;
  String? avatarUrl;
  int? rank;
  int? points;
  int? gamesWon;

  Leaderboard({
    this.uid,
    this.gamesPlayed,
    this.playerName,
    this.avatarUrl,
    this.rank,
    this.points,
    this.gamesWon,
  });

  Leaderboard.fromJson(Map<dynamic, dynamic> json) {
    uid = json['uid'];
    gamesPlayed = json['gamesPlayed'];
    playerName = json['playerName'];
    avatarUrl = json['avatarUrl'];
    rank = json['rank'];
    points = json['points'];
    gamesWon = json['gamesWon'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['uid'] = uid;
    data['gamesPlayed'] = gamesPlayed;
    data['playerName'] = playerName;
    data['avatarUrl'] = avatarUrl;
    data['rank'] = rank;
    data['points'] = points;
    data['gamesWon'] = gamesWon;
    return data;
  }
}
