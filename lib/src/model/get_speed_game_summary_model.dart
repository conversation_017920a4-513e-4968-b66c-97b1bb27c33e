class GetSpeedGameSummaryModel {
  String? name;
  String? type;
  int? minPoints;
  int? maxPoints;
  num? betterPercentage;
  Map<String, int>? summary;

  GetSpeedGameSummaryModel({
    this.name,
    this.type,
    this.minPoints,
    this.maxPoints,
    this.summary,
  });

  GetSpeedGameSummaryModel.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    type = json['type'];
    minPoints = json['minPoints'];
    maxPoints = json['maxPoints'];
    betterPercentage = json['betterPercentage'];
    if (json['summary'] != null) {
      summary = Map<String, int>.from(json['summary']);
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['name'] = name;
    data['type'] = type;
    data['minPoints'] = minPoints;
    data['maxPoints'] = maxPoints;
    data['betterPercentage'] = betterPercentage;
    if (summary != null) {
      data['summary'] = summary;
    }
    return data;
  }

  List<ChartData> get chartData {
    return summary?.entries.map((e) => ChartData(e.key, e.value)).toList() ??
        [];
  }
}

class ChartData {
  final String group;
  final int count;

  ChartData(this.group, this.count);
}
