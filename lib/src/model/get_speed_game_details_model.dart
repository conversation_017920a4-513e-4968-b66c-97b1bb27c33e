class GetSpeedGameDetailsModel {
  String? result;
  int? linePoints;
  int? gussesLength;
  int? secondsPoints;
  String? name;
  int? remainingSeconds;
  int? totalPoints;
  String? type;
  int? usedSeconds;

  GetSpeedGameDetailsModel(
      {this.result,
      this.linePoints,
      this.gussesLength,
      this.secondsPoints,
      this.name,
      this.remainingSeconds,
      this.totalPoints,
      this.type,
      this.usedSeconds});

  GetSpeedGameDetailsModel.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    linePoints = json['linePoints'];
    gussesLength = json['gussesLength'];
    secondsPoints = json['secondsPoints'];
    name = json['name'];
    remainingSeconds = json['remainingSeconds'];
    totalPoints = json['totalPoints'];
    type = json['type'];
    usedSeconds = json['usedSeconds'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['result'] = result;
    data['linePoints'] = linePoints;
    data['gussesLength'] = gussesLength;
    data['secondsPoints'] = secondsPoints;
    data['name'] = name;
    data['remainingSeconds'] = remainingSeconds;
    data['totalPoints'] = totalPoints;
    data['type'] = type;
    data['usedSeconds'] = usedSeconds;
    return data;
  }

  @override
  String toString() {
    return 'GetSpeedGameDetailsModel(result: $result, linePoints: $linePoints, gussesLength: $gussesLength, '
        'secondsPoints: $secondsPoints, name: $name, remainingSeconds: $remainingSeconds, '
        'totalPoints: $totalPoints, type: $type, usedSeconds: $usedSeconds)';
  }
}
