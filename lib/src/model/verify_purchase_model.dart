class VerifyPurchaseModel {
  Data? data;
  String? message;

  VerifyPurchaseModel({this.data, this.message});

  VerifyPurchaseModel.fromJson(Map<String, dynamic> json) {
    data = (json['data'] as Map<Object?, Object?>?) != null
        ? Data.fromJson(
            Map<String, dynamic>.from(json['data'] as Map<Object?, Object?>))
        : null;
    message = json['message'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['message'] = message;
    return data;
  }
}

class Data {
  bool isVerified;
  String? userId;
  String? email;
  String? loginProvider;

  Data({
    required this.isVerified,
    this.userId,
    this.email,
    this.loginProvider,
  });

  Data.fromJson(Map<String, dynamic> json)
      : isVerified = json['isVerified'] as bool,
        userId = json['userId'] as String?,
        email = json['email'] as String?,
        loginProvider = json['loginProvider'] as String?;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['isVerified'] = isVerified;
    data['userId'] = userId;
    data['email'] = email;
    data['loginProvider'] = loginProvider;
    return data;
  }
}
