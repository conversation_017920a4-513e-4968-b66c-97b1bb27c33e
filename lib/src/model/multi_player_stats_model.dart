class MultiPlayerStatsModel {
  int? gamesWon;
  dynamic winRateWhenPlayedFirst;
  int? bestWinStreak;
  dynamic winRateWhenPlayedSecond;
  int? winStreak;
  dynamic winRate;
  int? gameLost;
  int? rank;
  int? points = 0;
  int? gamesPlayed;

  MultiPlayerStatsModel(
      {this.gamesWon,
      this.winRateWhenPlayedFirst,
      this.bestWinStreak,
      this.winRateWhenPlayedSecond,
      this.winStreak,
      this.winRate,
      this.rank,
      this.gamesPlayed,
      this.points,
      this.gameLost});

  MultiPlayerStatsModel.fromJson(Map<String, dynamic> json) {
    gamesWon = json['gamesWon'] ?? 0;
    winRateWhenPlayedFirst = json['winRateWhenPlayedFirst'] ?? 0;
    bestWinStreak = json['bestWinStreak'] ?? 0;
    winRateWhenPlayedSecond = json['winRateWhenPlayedSecond'] ?? 0;
    winStreak = json['winStreak'] ?? 0;
    winRate = json['winRate'] ?? 0;
    rank = json['rank'] ?? 0;
    gamesPlayed = json["gamesPlayed"] ?? 0;
    gameLost = json['gamesLost'] ?? 0;
    points = json['points'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['gamesWon'] = gamesWon;
    data['winRateWhenPlayedFirst'] = winRateWhenPlayedFirst;
    data['bestWinStreak'] = bestWinStreak;
    data['winRateWhenPlayedSecond'] = winRateWhenPlayedSecond;
    data['winStreak'] = winStreak;
    data['winRate'] = winRate;
    data['gamesLost'] = gameLost;
    data['points'] = points;
    data['rank'] = rank;
    data['gamesPlayed'] = gamesPlayed;
    return data;
  }
}
