import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:wordle/src/core/constants/keyboard_layout.dart';
import 'package:wordle/src/core/constants/user_prop.dart';
import 'package:wordle/src/core/helpers/users.dart';

import 'settings_service.dart';

/// A class that many Widgets can interact with to read user settings, update
/// user settings, or listen to user settings changes.
///
/// Controllers glue Data Services to Flutter Widgets. The SettingsController
/// uses the SettingsService to store and retrieve user settings.
class SettingsController with ChangeNotifier {
  SettingsController(this._settingsService);

  // Make SettingsService a private variable so it is not used directly.
  final SettingsService _settingsService;

  // Make ThemeMode a private variable so it is not updated directly without
  // also persisting the changes with the SettingsService.
  late ThemeMode _themeMode;

  // Allow Widgets to read the user's preferred ThemeMode.
  ThemeMode get themeMode => _themeMode;

  bool? _darkMode;
  bool get isDarkMode => _darkMode ?? false;

  bool _enterOnLeft = true;
  bool get isEnterOnLeft => _enterOnLeft;

  KeyboardLayout _keyboardLayout = KeyboardLayout.qwerty;
  KeyboardLayout get keyboardLayout => _keyboardLayout;

  bool _highContrast = false;
  bool get isHighContrast => _highContrast;

  bool _fullScreen = true;
  bool get isFullScreen => _fullScreen;

  void determineDarkMode(BuildContext context) {
    if (_themeMode != ThemeMode.dark && _themeMode != ThemeMode.light) {
      // ignore: deprecated_member_use
      _darkMode ??= (SchedulerBinding.instance.window.platformBrightness ==
          Brightness.dark);
    } else {
      _darkMode = (_themeMode == ThemeMode.dark);
    }
  }

  /// Load the user's settings from the SettingsService. It may load from a
  /// local database or the internet. The controller only knows it can load the
  /// settings from the service.
  Future<void> loadSettings() async {
    _themeMode = await _settingsService.themeMode();
    _enterOnLeft = await _settingsService.enterOnLeft();
    _keyboardLayout = await _settingsService.loadKeyboardLayout();
    _highContrast = await _settingsService.loadHighContrast();
    _fullScreen = await _settingsService.loadFullScreen();

    await setUserProp(
      key: UserProp.appTheme,
      value: _themeMode.toString().split('.').last,
    );
    await setUserProp(
      key: UserProp.enterKeyOnLeft,
      value: _enterOnLeft.toString(),
    );
    await setUserProp(
      key: UserProp.highContrastMode,
      value: _highContrast.toString(),
    );
    await setUserProp(
      key: UserProp.fullScreen,
      value: _fullScreen.toString(),
    );
    await setUserProp(
      key: UserProp.keyboardLayout,
      value: _keyboardLayout.getName(),
    );
    // Important! Inform listeners a change has occurred.
    notifyListeners();
  }

  /// Update and persist the ThemeMode based on the user's selection.
  Future<void> updateThemeMode() async {
    _darkMode = !_darkMode!;
    _themeMode = _darkMode ?? false ? ThemeMode.dark : ThemeMode.light;

    // Important! Inform listeners a change has occurred.
    notifyListeners();

    // Persist the changes to a local database or the internet using the
    // SettingService.
    await _settingsService.updateThemeMode(_themeMode);
  }

  /// Update and persist the _enterOnLeft based on the user's selection.
  Future<void> toggleEnterOnLeft() async {
    _enterOnLeft = !_enterOnLeft;

    // Important! Inform listeners a change has occurred.
    notifyListeners();

    // Persist the changes to a local database or the internet using the
    // SettingService.
    await _settingsService.updateEnterOnLeft(_enterOnLeft);
  }

  Future<void> updateKeyboardLayout(KeyboardLayout layout) async {
    _keyboardLayout = layout;

    notifyListeners();

    await _settingsService.updateKeyboardLayout(_keyboardLayout);
  }

  Future<void> toggleHighContrast() async {
    _highContrast = !_highContrast;

    // Important! Inform listeners a change has occurred.
    notifyListeners();

    // Persist the changes to a local database or the internet using the
    // SettingService.
    await _settingsService.updateHighContrast(_highContrast);
  }

  Future<void> toggleFullScreen() async {
    _fullScreen = !_fullScreen;

    // Important! Inform listeners a change has occurred.
    notifyListeners();

    if (_fullScreen) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    } else {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    }

    // Persist the changes to a local database or the internet using the
    // SettingService.
    await _settingsService.updateFullScreen(_fullScreen);
  }
}
