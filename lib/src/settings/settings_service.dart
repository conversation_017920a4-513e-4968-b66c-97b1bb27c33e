import 'package:flutter/material.dart';
import 'package:wordle/src/core/constants/keyboard_layout.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';

/// A service that stores and retrieves user settings.
///
/// By default, this class does not persist user settings. If you'd like to
/// persist the user settings locally, use the shared_preferences package. If
/// you'd like to store settings on a web server, use the http package.
class SettingsService {
  final _localStorage = ServiceLocator.locate<LocalStorage>();

  /// Loads the User's preferred ThemeMode from local or remote storage.
  Future<ThemeMode> themeMode() async {
    final themeString = _localStorage.retrieve(LSKey.themeMode);

    switch (themeString) {
      case 'ThemeMode.dark':
        return ThemeMode.dark;
      case 'ThemeMode.light':
        return ThemeMode.light;
      default:
        return ThemeMode.system;
    }
  }

  Future<bool> enterOnLeft() async {
    return _localStorage.retrieveBool(LSKey.isEnterOnLeft) ?? true;
  }

  Future<KeyboardLayout> loadKeyboardLayout() async {
    final layoutString = _localStorage.retrieve(LSKey.keyboardLayout);

    switch (layoutString) {
      case 'KeyboardLayout.qwerty':
        return KeyboardLayout.qwerty;
      case 'KeyboardLayout.qwertz':
        return KeyboardLayout.qwertz;
      case 'KeyboardLayout.azerty':
        return KeyboardLayout.azerty;
      default:
        return KeyboardLayout.qwerty;
    }
  }

  Future<bool> loadHighContrast() async {
    return _localStorage.retrieveBool(LSKey.isHighContrast) ?? false;
  }

  Future<bool> loadFullScreen() async {
    return _localStorage.retrieveBool(LSKey.isFullScreen) ?? true;
  }

  /// Persists the user's preferred ThemeMode to local or remote storage.
  Future<void> updateThemeMode(ThemeMode theme) async {
    // Use the shared_preferences package to persist settings locally or the
    // http package to persist settings over the network.
    _localStorage.save(LSKey.themeMode, theme.toString());
  }

  Future<void> updateEnterOnLeft(bool enterOnLeft) async {
    _localStorage.saveBool(LSKey.isEnterOnLeft, enterOnLeft);
  }

  Future<void> updateKeyboardLayout(KeyboardLayout layout) async {
    _localStorage.save(LSKey.keyboardLayout, layout.toString());
  }

  Future<void> updateHighContrast(bool highContrast) async {
    _localStorage.saveBool(LSKey.isHighContrast, highContrast);
  }

  Future<void> updateFullScreen(bool fullScreen) async {
    _localStorage.saveBool(LSKey.isFullScreen, fullScreen);
  }
}
