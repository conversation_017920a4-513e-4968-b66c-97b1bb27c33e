import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wordle/src/core/constants/config_param.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/utils/deeplink_handler.dart';
import 'package:wordle/src/core/utils/word_list.dart';
import 'package:wordle/src/router/start_up_controller.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';
import 'package:wordle/src/services/fcm/fcm_service.dart';

import 'firebase_options.dart';
import 'src/app.dart';
import 'src/core/utils/env.dart';
import 'src/core/utils/service_locator.dart';
import 'src/settings/settings_controller.dart';

Stopwatch stopwatch = Stopwatch();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  stopwatch.reset();
  stopwatch.start();

  await Firebase.initializeApp(
    name: kIsWeb ? null : 'worde',
    options: DefaultFirebaseOptions.currentPlatform,
  );

  const _env = String.fromEnvironment('ENV');
  await loadEnv(type: _env);
  _requestEUConsent();
  FirebaseMessaging.onBackgroundMessage(_backgroundNotificationHandler);
  await ServiceLocator.register();

  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  _eventLogger.log(
    Events.appLaunched,
    params: AppStartupParam().param,
  );

  final fcmService = ServiceLocator.locate<FcmService>();
  await fcmService.initialise();

  await FirebaseRemoteConfig.instance.setConfigSettings(RemoteConfigSettings(
    fetchTimeout: const Duration(seconds: 30),
    minimumFetchInterval: const Duration(hours: 1),
  ));

  _eventLogger.log(
    Events.remoteConfigCalled,
    params: AppStartupParam().param,
  );

  await callGetSetting();
  final startup = StartUpController();

  await DeepLinkHandler.setupDeepLinkHandler();

  _eventLogger.log(
    Events.deepLinkCalled,
    params: AppStartupParam().param,
  );

  String fcm = await fcmService.getFcmToken() ?? "";
  debugPrint("fcm : " + fcm);

  // Run the app and pass in the startup. The app listens to the
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]).then(
    (value) => runApp(
      App(
        startUpController: startup,
      ),
    ),
  );
}

Future<void> setUpConfigDefaults() async {
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final wordlist = await rootBundle.loadString(WordList.dailyWordListPath);
  await FirebaseRemoteConfig.instance.setDefaults({
    ConfigParam.enWordlist: wordlist,
    ConfigParam.enableUnlimitedMode: true,
    ConfigParam.enableDailyMode: true,
    ConfigParam.enableMultiplayerMode: true,
    ConfigParam.enableDailyModeComments: true,
    ConfigParam.forceUpdate: false,
    ConfigParam.forceUpdateMessage: '''
Please update to the latest version of this game to get important security and bug fixes.
''',
    ConfigParam.forceUpdateLink: env(EnvKey.PLAYSTORE_LINK),
    ConfigParam.shareableResultsTemplate: '''
{title} {puzzleNumber} {guesses}

{results}''',
    ConfigParam.leaderboardLimit: 100,
    ConfigParam.interstitialAdFrequency: 3,
    ConfigParam.dailyCommentAdPlacement: "[2, 11]",

    //normal_count
    ConfigParam.hints5Hand: ConfigDefaults.hints5HandCount,
    ConfigParam.hints5LightBulb: ConfigDefaults.hints5LightBulbCount,
    ConfigParam.hints6Hand: ConfigDefaults.hints6HandCount,
    ConfigParam.hints6LightBulb: ConfigDefaults.hints6LightBulbCount,
    ConfigParam.hints7Hand: ConfigDefaults.hints7HandCount,
    ConfigParam.hints7LightBulb: ConfigDefaults.hints7LightBulbCount,

    //play_pass_count
    ConfigParam.hints5HandPlayPass: ConfigDefaults.hints5HandPlayPassCount,
    ConfigParam.hints5LightBulbPlayPass:
        ConfigDefaults.hints5LightBulbPlayPassCount,
    ConfigParam.hints6HandPlayPass: ConfigDefaults.hints6HandPlayPassCount,
    ConfigParam.hints6LightBulbPlayPass:
        ConfigDefaults.hints6LightBulbPlayPassCount,
    ConfigParam.hints7HandPlayPass: ConfigDefaults.hints7HandPlayPassCount,
    ConfigParam.hints7LightBulbPlayPass:
        ConfigDefaults.hints7LightBulbPlayPassCount,

    //sub_count
    ConfigParam.hints5HandSub: ConfigDefaults.hints5HandSubCount,
    ConfigParam.hints5LightBulbSub: ConfigDefaults.hints5LightBulbSubCount,
    ConfigParam.hints6HandSub: ConfigDefaults.hints6HandSubCount,
    ConfigParam.hints6LightBulbSub: ConfigDefaults.hints6LightBulbSubCount,
    ConfigParam.hints7HandSub: ConfigDefaults.hints7HandSubCount,
    ConfigParam.hints7LightBulbSub: ConfigDefaults.hints7LightBulbSubCount,

    //ads_count
    ConfigParam.hints5HandAds: ConfigDefaults.hints5HandAdsCount,
    ConfigParam.hints5LightBulbAds: ConfigDefaults.hints5LightBulbAdsCount,
    ConfigParam.hints6HandAds: ConfigDefaults.hints6HandAdsCount,
    ConfigParam.hints6LightBulbAds: ConfigDefaults.hints6LightBulbAdsCount,
    ConfigParam.hints7HandAds: ConfigDefaults.hints7HandAdsCount,
    ConfigParam.hints7LightBulbAds: ConfigDefaults.hints7LightBulbAdsCount,

    //speed-mode
  });

  _eventLogger.log(
    Events.setUpConfigDefaultsCalled,
    params: AppStartupParam().param,
  );
}

/// This method has to be outside the main() according to the official docs:
/// https://firebase.flutter.dev/docs/messaging/usage/#background-messages
/// on how background messages work
///
///

@pragma('vm:entry-point')
Future<void> _backgroundNotificationHandler(RemoteMessage remoteMessage) async {
  await Firebase.initializeApp();
}

void _requestEUConsent() {
  final params = ConsentRequestParameters();
  ConsentInformation.instance.requestConsentInfoUpdate(
    params,
    () async {
      if (await ConsentInformation.instance.isConsentFormAvailable()) {
        loadForm();
      }
    },
    (FormError error) {
      // Handle the error
    },
  );
}

void loadForm() {
  ConsentForm.loadConsentForm(
    (ConsentForm consentForm) async {
      var status = await ConsentInformation.instance.getConsentStatus();
      if (status == ConsentStatus.required) {
        consentForm.show((FormError? formError) {
          // Handle dismissal by reloading form
          loadForm();
        });
      }
    },
    (FormError formError) {
      // Handle the error
    },
  );
}

Future<void> callGetSetting() async {
  // Set up the SettingsController, which will glue user settings to multiple
  // Flutter Widgets.
  final settingsController = ServiceLocator.locate<SettingsController>();

  // Load the user's preferred theme while the splash screen is displayed.
  // This prevents a sudden theme change when the app is first displayed.
  await settingsController.loadSettings();

  if (settingsController.isFullScreen) {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  } else {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }
}

Future<void> askNotificationPermission() async {
  var status = await Permission.notification.status;
  if (status.isDenied) {
    await Permission.notification.request();
  } else if (status.isPermanentlyDenied) {
    openAppSettings();
  }
}

extension StringCasingExtension on String {
  String toCapitalized() =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';

  String toTitleCase() => replaceAll(RegExp(' +'), ' ')
      .split(' ')
      .map((str) => str.toCapitalized())
      .join(' ');
}
