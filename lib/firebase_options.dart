// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    // ignore: missing_enum_constant_in_switch
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
    }

    throw UnsupportedError(
      'DefaultFirebaseOptions are not supported for this platform.',
    );
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAQMT2fw-mo2NkLCVDG5qUd989eLTFsOCE',
    appId: '1:1058370193563:web:77a75db86fe20a8bffde3e',
    messagingSenderId: '1058370193563',
    projectId: 'wordle-7b910',
    authDomain: 'wordle-7b910.firebaseapp.com',
    databaseURL: 'https://wordle-7b910-default-rtdb.firebaseio.com',
    storageBucket: 'wordle-7b910.appspot.com',
    measurementId: 'G-XBB5P1KW71',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCA32FOm11xdC4BI05WIlnpN9yAEQjRQis',
    appId: '1:1058370193563:android:7aafe38d02567c00ffde3e',
    messagingSenderId: '1058370193563',
    projectId: 'wordle-7b910',
    databaseURL: 'https://wordle-7b910-default-rtdb.firebaseio.com',
    storageBucket: 'wordle-7b910.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBs3-Aja9WpEJM9JVi83cjtku98bCaj1Eo',
    appId: '1:1058370193563:ios:6ef1019bbd216524ffde3e',
    messagingSenderId: '1058370193563',
    projectId: 'wordle-7b910',
    databaseURL: 'https://wordle-7b910-default-rtdb.firebaseio.com',
    storageBucket: 'wordle-7b910.appspot.com',
    androidClientId:
        '1058370193563-q347k22s7atc1ipgpkt1cfg5h33hstvs.apps.googleusercontent.com',
    iosClientId:
        '1058370193563-lokhu5nbbvns64odq3htanh5ad17au1n.apps.googleusercontent.com',
    iosBundleId: 'com.chinloyal.wordle',
  );
}
