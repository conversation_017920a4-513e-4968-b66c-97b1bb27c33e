.macos_buildcloud_runners:
  tags:
    - shared-macos-amd64

image: cirrusci/flutter:3.3.8

variables:
  FLUTTER_VERSION: 3.3.8
  ANDROID_KEYSTORE_FILE: key.keystore
  ANDROID_KEYSTORE_PASSWORD: $SECRET_ANDROID_KEYSTORE_PASSWORD
  ANDROID_KEYSTORE_ALIAS: $SECRET_ANDROID_KEYSTORE_ALIAS
  ANDROID_KEY_PASSWORD: $SECRET_ANDROID_KEY_PASSWORD
  APPLE_CERTIFICATE_FILE: Cert.p12
  APPLE_PROVISIONING_PROFILE_FILE: WordleProfile.mobileprovision
  XCODE_VERSION: '12.4'

stages:
  - Testing
  - Build
  - Deploy

.before_script_template: &build_test-integration
  before_script:
    - export BUILD_NUMBER=$(date +%Y%m%d)${CI_PIPELINE_IID:(-2)}
    - cp config/.env.example config/.env
    - cp config/.env.example config/prod.env;
    - flutter pub get
    - flutter packages pub run build_runner build --delete-conflicting-outputs
    - flutter gen-l10n
    # - sudo xcode-select --switch "/Applications/Xcode_$XCODE_VERSION.app/Contents/Developer"


# linting:
#   # extends: .macos_buildcloud_runners
#   stage: Testing
#   <<: *build_test-integration
#   script: |
#     flutter analyze

# tests:
#   # extends: .macos_buildcloud_runners
#   stage: Testing
#   <<: *build_test-integration
#   script: |
#     flutter test --coverage
#     curl -O https://raw.githubusercontent.com/eriwen/lcov-to-cobertura-xml/master/lcov_cobertura/lcov_cobertura.py
#     sudo apt-get install software-properties-common
#     sudo add-apt-repository ppa:deadsnakes/ppa
#     sudo apt-get update -y
#     sudo apt-get install -y python3.8 python3-distutils
#     python3 lcov_cobertura.py coverage/lcov.info --output coverage/coverage.xml
#   artifacts:
#     when: always
#     reports:
#       cobertura:
#         - coverage/coverage.xml

android_prod_build:
  # extends: .macos_buildcloud_runners
  stage: Build
  <<: *build_test-integration
  only:
    - "master"
  # dependencies:
  #   - linting
  #   - tests
  script: |
    base64 -d $ANDROID_KEYSTORE > android/$ANDROID_KEYSTORE_FILE
    sed -i "s/BUILD_NUMBER=.*/BUILD_NUMBER=$BUILD_NUMBER/" config/prod.env
    flutter build appbundle --obfuscate --flavor prod --build-number $BUILD_NUMBER --dart-define ENV=prod --split-debug-info=build/prod_map_aab --verbose
    flutter build apk --obfuscate --flavor prod --build-number $BUILD_NUMBER --dart-define ENV=prod --split-debug-info=build/prod_map_apk --verbose
  artifacts:
    name: android
    paths:
      - build/app/outputs/bundle/prodRelease/*.aab
      - build/app/outputs/flutter-apk/*.apk
      - build/prod_map_aab/*
      - build/prod_map_apk/*

web_prod_build:
  stage: Build
  <<: *build_test-integration
  only:
    - "master"
  script: |
    flutter build web --dart-define ENV=prod --base-href /
  artifacts:
    name: web
    paths:
      - build/web/*


basic_android_build:
  # extends: .macos_buildcloud_runners
  stage: Build
  <<: *build_test-integration
  except:
    - "develop"
    - "master"
    - "main"
  # dependencies:
  #   - linting
  #   - tests
  script: |
    flutter build apk --debug --flavor prod --build-number $BUILD_NUMBER --dart-define ENV=prod --verbose

# ios_prod_build:
#   extends: .macos_buildcloud_runners
#   stage: Build
#   dependencies:
#     - linting
#     - tests
#   script: |
#     echo 'Decode apple certificate'
#     base64 -d $APPLE_CERTIFICATE > $APPLE_CERTIFICATE_FILE

#     echo 'Decode apple provisioning profile'
#     base64 -d $APPLE_PROVISIONING_PROFILE > $APPLE_PROVISIONING_PROFILE_FILE

#     echo 'Add build number to env file'
#     sed -i '' "s/BUILD_NUMBER=.*/BUILD_NUMBER=$BUILD_NUMBER/" config/.env

#     echo 'Build ios app'
#     flutter build ios --build-number $BUILD_NUMBER --no-codesign --verbose

#     echo 'Install apple cerificate'
#     security import $APPLE_CERTIFICATE_FILE -P $APPLE_CERTIFICATE_PASSWORD

#     echo 'Install apple provisioning profile'
#     UUID=`/usr/libexec/PlistBuddy -c 'Print :UUID' /dev/stdin <<< $(security cms -D -i $APPLE_PROVISIONING_PROFILE_FILE)`
#     cp $APPLE_PROVISIONING_PROFILE_FILE ~/Library/MobileDevice/Provisioning\ Profiles/$UUID.mobileprovision
    
#     echo 'Archive production ipa'
#     xcodebuild archive -sdk iphoneos -configuration Release -workspace ios/Runner.xcworkspace \
#     -allowProvisioningUpdates -verbose -archivePath ios/build/Runner.xcarchive \
#     -scheme Runner CODE_SIGN_STYLE=Manual

#     echo 'Export production ipa'
#     xcodebuild -exportArchive -archivePath ios/build/Runner.xcarchive \
#     -exportPath ios/build \
#     -exportOptionsPlist ios/Runner/ExportOptions.plist \
#     -verbose -allowProvisioningUpdates
#   artifacts:
#     name: ios
#     paths:
#       - ios/build/*.ipa
    
android_prod_app_tester_apk:
  # extends: .macos_buildcloud_runners
  stage: Deploy
  only:
    - "master"
  dependencies:
    - "android_prod_build"
  script: |
    curl -sL https://firebase.tools | bash
    firebase appdistribution:distribute build/app/outputs/flutter-apk/app-prod-release.apk \
    --app $FIREBASE_ANDROID_APP_ID --token $FIREBASE_DEPLOY_TOKEN

android_prod_app_tester_aab:
  # extends: .macos_buildcloud_runners
  stage: Deploy
  only:
    - "master"
  dependencies:
    - "android_prod_build"
  script: |
    curl -sL https://firebase.tools | bash
    firebase appdistribution:distribute build/app/outputs/bundle/prodRelease/app-prod-release.aab \
    --app $FIREBASE_ANDROID_APP_ID --token $FIREBASE_DEPLOY_TOKEN

# ios_testflight:
#   extends: .macos_buildcloud_runners
#   stage: Deploy
#   dependencies:
#     - ios_prod_build
#   script: |
#     xcrun altool --validate-app -f ios/build/*.ipa -t ios --apiKey <api_key> --apiIssuer <issuer_id>
#     xcrun altool --upload-app -f ios/build/*.ipa -t ios --apiKey <api_key> --apiIssuer <issuer_id>

