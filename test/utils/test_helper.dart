import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:flutter_test/flutter_test.dart';
import 'package:wordle/src/core/utils/env.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'models/phone.dart';

class TestHelper {
  static Future<void> setup() async {
    TestWidgetsFlutterBinding.ensureInitialized();
    SharedPreferences.setMockInitialValues({});
    await loadEnv();
    await ServiceLocator.register();
    ServiceLocator.locate.allowReassignment = true;
    HttpOverrides.global = null;
  }

  static String getFixturesContents(String fileName) {
    File file;

    // This fails based on where the test is called from but this resolves the issue:
    // https://stackoverflow.com/questions/55185557/flutter-test-resources-in-codemagic
    try {
      file = File('test/unit/fixtures/$fileName');
      final string = file.readAsStringSync();
      return string;
    } on FileSystemException {
      file = File('../test/unit/fixtures/$fileName');
      final string = file.readAsStringSync();
      return string;
    }
  }

  static List<Phone> getPhones() {
    var phones = <Phone>[];

    phones.add((Phone('iPhone X', 375, 812)));
    phones.add((Phone('iPhone 7 Plus', 414, 736)));
    phones.add((Phone('iPhone 6', 375, 667)));
    phones.add((Phone('iPhone SE', 320, 568)));
    phones.add(Phone('Galaxy S5', 360, 540));

    return phones;
  }

  static String generateRandomString({int length = 1000}) {
    var random = Random.secure();
    var values = List<int>.generate(length, (i) => random.nextInt(255));
    return base64Encode(values);
  }

  static int generateRandomNumber({int max = 10000}) {
    return Random().nextInt(max) + (max + 1);
  }
}
