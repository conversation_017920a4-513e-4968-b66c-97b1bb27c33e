import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_test/flutter_test.dart';

import 'test_helper.dart';

class WidgetOverflowTester {
  static Widget makeTesteableWidget(Widget child,
      {double? width, double? height}) {
    return MaterialApp(
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        home: Scaffold(
          // body: SizedBox(
          // width: width,
          // height: height,
          body: child,
          // )),
        ));
  }

  static Future<void> testForOverFlow(
      WidgetTester tester, Widget widget) async {
    final phones = TestHelper.getPhones();
    for (var phone in phones) {
      // ignore: deprecated_member_use
      tester.binding.window.physicalSizeTestValue =
          Size(phone.width, phone.height);
      log('Screen height: ${phone.height} width: ${phone.width}');

      await tester.pumpWidget(
        makeTesteableWidget(
          widget,
          width: phone.width,
          height: phone.height,
        ),
      );
      log('pumping ${widget.toString()}');
      await tester.pumpAndSettle();
    }
  }
}
