package com.chinloyal.wordle

import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import android.os.Build
import androidx.core.view.WindowCompat
import androidx.annotation.NonNull
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import androidx.multidex.BuildConfig
import com.ironsource.mediationsdk.ISBannerSize
import com.ironsource.mediationsdk.IronSource
import com.ironsource.mediationsdk.IronSourceBannerLayout
import com.ironsource.mediationsdk.integration.IntegrationHelper
import com.ironsource.mediationsdk.model.Placement
import com.ironsource.mediationsdk.utils.IronSourceUtils
import com.singular.sdk.*
import com.google.firebase.analytics.FirebaseAnalytics

private const val APP_KEY = "1e00f4a35";

class MainActivity : FlutterActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupIronSourceSdk()
    }

    private val CHANNEL = "com.chinloyal.wordle/channel"

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).apply {
            setMethodCallHandler { call, result ->
                // Handle Method Calls from Flutter
                if (call.method == "showRewardAd") {
                    IronSource.setLevelPlayRewardedVideoListener(RewardedVideoAdListener(result))
                    if (IronSource.isRewardedVideoAvailable()) {
                        IronSource.showRewardedVideo(null)
                    } else {
                        result.error("Not Found Error", "Not Found error", null);
                    }

                } else if (call.method == "showRewardAdWithPlacement") {
                    val user = call.arguments as? Map<String, Any>
                    val placement: String = (user?.get("placement") as? String)!!
                    val user_id: String = (user?.get("user_id") as? String)!!

                    IronSource.setLevelPlayRewardedVideoListener(RewardedVideoAdListener(result))

                    if (IronSource.isRewardedVideoAvailable()) {
                        IronSource.setDynamicUserId(user_id)
                        IronSource.showRewardedVideo(placement)
                    } else {
                        result.error("Not Found Error", "Not Found error", null);
                    }

                } else if (call.method == "loadInterstitialAd") {
                    IronSource.setLevelPlayInterstitialListener(InterstitialAdListener(result))
                    IronSource.loadInterstitial()
                } else if (call.method == "showInterstitialAd") {
                    IronSource.setLevelPlayInterstitialListener(InterstitialAdListener(result))
                    if (IronSource.isInterstitialReady()) {
                        IronSource.showInterstitial()
                    } else {
                        result.error("Not Found Error", "Not Found error", null);
                    }
                } else if (call.method == "logPlayPassReveueEventInSingular") {
                    val args = call.arguments as? Map<String, Any>
                    val revenue: Double = (args?.get("value") as? Double)!!
                    Singular.customRevenue("playpass_revenue_usd", "USD", revenue);
                } else if (call.method == "logRevenueEventInSingularAndFirebase") {
                    val args = call.arguments as? Map<String, Any>
                    val revenue: Double = (args?.get("value") as? Double)!!
                    val precision: String = (args?.get("precision") as? String)!!
                    val currency: String = (args?.get("currency") as? String)!!
                    val adSource: String = (args?.get("adSource") as? String)!!
                    val adSourceInstance: String = (args?.get("adSourceInstance") as? String)!!
                    val adUnit: String = (args?.get("adUnit") as? String)!!

                    var attributes =  mapOf("adUnit" to adUnit, "network" to adSource,  "precision" to precision, "adSource" to adSource, "adSourceInstance" to adSourceInstance);
                    if( currency == "USD" ) {
                        Singular.customRevenue("ad_revenue_usd", currency, revenue, attributes);
                        val bundle = Bundle().apply {
                            putString(FirebaseAnalytics.Param.AD_PLATFORM, "google")
                            putString(FirebaseAnalytics.Param.AD_SOURCE, adSource)
                            putString(FirebaseAnalytics.Param.AD_FORMAT, adUnit)
                            putString(FirebaseAnalytics.Param.AD_UNIT_NAME, adSourceInstance)
                            putString(FirebaseAnalytics.Param.CURRENCY, "USD")
                            putDouble(FirebaseAnalytics.Param.VALUE, revenue)
                        }
                        Analytics.getInstance().adRevenueEvent(bundle)
                    } else {
                        Singular.customRevenue("ad_revenue_xxx", currency, revenue, attributes);
                    }

                }
                else {
                    result.notImplemented()
                }
            }

        }
    }

    private fun setupIronSourceSdk() {
        if (BuildConfig.DEBUG) {
            IntegrationHelper.validateIntegration(this)
        }
        IronSource.init(this, APP_KEY, AdInitializationListener(), IronSource.AD_UNIT.REWARDED_VIDEO, IronSource.AD_UNIT.INTERSTITIAL);
        IronSource.addImpressionDataListener(AdImpressionDataListener());
        var singularConfig = getSingularConfig();
        Singular.init(applicationContext, singularConfig);
        Analytics.init(this);
    }

    private fun getSingularConfig(): SingularConfig {
        val config = SingularConfig(getString(R.string.singular_api_key), getString(R.string.singular_secret))
                .withLoggingEnabled()
                .withLogLevel(1);
        return config;
    }

}

