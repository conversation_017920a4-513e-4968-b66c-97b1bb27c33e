package com.chinloyal.wordle
import android.os.Handler
import android.os.Looper
import com.ironsource.mediationsdk.adunit.adapter.utility.AdInfo
import com.ironsource.mediationsdk.logger.IronSourceError
import com.ironsource.mediationsdk.model.Placement
import com.ironsource.mediationsdk.sdk.LevelPlayRewardedVideoListener
import io.flutter.plugin.common.MethodChannel

class RewardedVideoAdListener(private val result: MethodChannel.Result)  : LevelPlayRewardedVideoListener {

    private var isResultUsed = false
    
    private val mainHandler = Handler(Looper.getMainLooper())

    private fun runOnMainThread(action: () -> Unit) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            action()
        } else {
            mainHandler.post { action() }
        }
    }

    private fun sendResult(action: () -> Unit) {
        runOnMainThread {
            synchronized(this) {
                if (!isResultUsed) {
                    try {
                        action()
                    } catch (e: IllegalStateException) {
                        e.printStackTrace()
                    } finally {
                        isResultUsed = true
                    }
                }
            }
        }
    }

    /**
    Called after a rewarded video has changed its availability to true.
    @param adInfo The info of the ad.
     */
    override fun onAdAvailable(adInfo: AdInfo) {
    }

    /**
    Called after a rewarded video has changed its availability to false.
     */
    override fun onAdUnavailable() {        
    }

    /**
    Called after a rewarded video has been opened.
    @param adInfo The info of the ad.
     */
    override fun onAdOpened(adInfo: AdInfo) {
    }

    /**
    Called after a rewarded video has attempted to show but failed.
    @param ironSourceError The reason for the error.
    @param adInfo The info of the ad.
     */
    override fun onAdShowFailed(ironSourceError: IronSourceError, adInfo: AdInfo) {
        sendResult {
            result.error("Not Found Error", "Not Found error", null)
        }
    }

    /**
    Called after a rewarded video has been clicked.
    This callback is not supported by all networks, and we recommend using it
    only if it's supported by all networks you included in your build
    @param placement An object that contains the placement's reward name and amount.
    @param adInfo The info of the ad.
     */
    override fun onAdClicked(placement: Placement?, adInfo: AdInfo) {
    }

    /**
    Called after a rewarded video has been viewed completely and the user is eligible for a reward.
    @param placement An object that contains the placement's reward name and amount.
    @param adInfo The info of the ad.
     */
    override fun onAdRewarded(placement: Placement, adInfo: AdInfo) {
        sendResult {
            result.success("DONE")
        }
    }

    /**
    Called after a rewarded video has been dismissed.
    @param adInfo The info of the ad.
     */
    override fun onAdClosed(adInfo: AdInfo) {
        sendResult {
            result.error("adClosed", "adClosed",null);          
        }  
    }
}