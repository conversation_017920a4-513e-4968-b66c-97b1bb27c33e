package com.chinloyal.wordle
import android.os.Handler
import android.os.Looper
import com.ironsource.mediationsdk.adunit.adapter.utility.AdInfo
import com.ironsource.mediationsdk.logger.IronSourceError
import com.ironsource.mediationsdk.sdk.LevelPlayInterstitialListener
import io.flutter.plugin.common.MethodChannel

class InterstitialAdListener(private val result: MethodChannel.Result) :
        LevelPlayInterstitialListener {

    private var isResultUsed = false

    private val mainHandler = Handler(Looper.getMainLooper())

    private fun runOnMainThread(action: () -> Unit) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            action()
        } else {
            mainHandler.post { action() }
        }
    }

    private fun sendResult(action: () -> Unit) {
        runOnMainThread {
            synchronized(this) {
                if (!isResultUsed) {
                    try {
                        action()
                    } catch (e: IllegalStateException) {
                        e.printStackTrace()
                    } finally {
                        isResultUsed = true
                    }
                }
            }
        }
    }

    /**
    Called after an interstitial has been loaded
    @param adInfo The info of the ad.
     */
    override fun onAdReady(adInfo: AdInfo) {
        sendResult {
            result.success("DONE")
        }
    }

    /**
    Called after an interstitial has attempted to load but failed.
    @param ironSourceError The reason for the error
     */
    override fun onAdLoadFailed(ironSourceError: IronSourceError) {
        sendResult {
            result.error("Not Found Error", "Not Found error", null)
        }
    }

    /**
    Called after an interstitial has been opened.
    This is the indication for impression.
    @param adInfo The info of the ad.
     */
    override fun onAdOpened(adInfo: AdInfo) {
    }

    /**
    Called after an interstitial has been displayed on the screen.
    This callback is not supported by all networks, and we recommend using it
    only if it's supported by all networks you included in your build.
    @param adInfo The info of the ad.
     */
    override fun onAdShowSucceeded(adInfo: AdInfo) {
        sendResult {
            result.success("DONE")
        }
    }

    /**
    Called after an interstitial has attempted to show but failed.
    @param ironSourceError The reason for the error.
    @param adInfo The info of the ad.
     */
    override fun onAdShowFailed(ironSourceError: IronSourceError, adInfo: AdInfo) {
        sendResult {
            result.error("Not Found Error", "Not Found error", null)
        } 
    }

    /**
    Called after an interstitial has been clicked.
    @param adInfo The info of the ad.
     */
    override fun onAdClicked(adInfo: AdInfo) {
       
    }

    /**
    Called after an interstitial has been dismissed.
    @param adInfo The info of the ad.
     */
    override fun onAdClosed(adInfo: AdInfo) {
        sendResult {
            result.success("DONE")
        }
    }
}