package com.chinloyal.wordle

import android.content.Context;
import android.os.Bundle;
import com.google.firebase.analytics.FirebaseAnalytics;
    

class Analytics private constructor(context: Context) {

    private val fireLogger: FirebaseAnalytics = FirebaseAnalytics.getInstance(context)

    fun adRevenueEvent(bundle: Bundle) {
        fireLogger.logEvent("ad_revenue_usd", bundle)
    }

    fun adImpressionEvent(bundle: Bundle) {
        fireLogger.logEvent(FirebaseAnalytics.Event.AD_IMPRESSION, bundle)
    }

    companion object {
        @Volatile
        private var shared: Analytics? = null

        fun init(context: Context) {
            if (shared == null) {
                synchronized(this) {
                    if (shared == null) {
                        shared = Analytics(context)
                    }
                }
            }
        }

        fun getInstance(): Analytics {
            return shared ?: throw IllegalStateException("Analytics not initialized, call init(context) first")
        }
    }
}

    
    
    
    
    
    
    
    
    
    
    