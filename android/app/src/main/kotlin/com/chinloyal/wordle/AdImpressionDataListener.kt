package com.chinloyal.wordle

import com.ironsource.mediationsdk.impressionData.ImpressionData
import com.ironsource.mediationsdk.impressionData.ImpressionDataListener
import android.util.Log
import com.google.firebase.analytics.FirebaseAnalytics
import android.os.Bundle
import com.singular.sdk.*

class AdImpressionDataListener : ImpressionDataListener {

    /**
    Called when the ad was displayed successfully and the impression data was recorded
    @param impressionData The recorded impression data
     */
    override fun onImpressionSuccess(impressionData: ImpressionData) {

        val revenue: Double = impressionData.revenue;
        val adNetwork: String = impressionData.adNetwork;
        val adUnit: String = impressionData.adUnit;
        val precision: String = impressionData.precision;
        val instanceName: String = impressionData.instanceName;
        var attributes =  mapOf("adUnit" to adUnit, "network" to adNetwork,  "precision" to precision);
        val data = SingularAdData("IronSource", "USD", revenue);
        Singular.adRevenue(data);
        Singular.customRevenue("ad_revenue_usd", "USD", revenue, attributes);
        val bundle = Bundle().apply {
            putString(FirebaseAnalytics.Param.AD_PLATFORM, "ironSource")
            putString(FirebaseAnalytics.Param.AD_SOURCE, impressionData.adNetwork)
            putString(FirebaseAnalytics.Param.AD_FORMAT, impressionData.adUnit)
            putString(FirebaseAnalytics.Param.AD_UNIT_NAME, impressionData.instanceName)
            putString(FirebaseAnalytics.Param.CURRENCY, "USD")
            putDouble(FirebaseAnalytics.Param.VALUE, impressionData.revenue)
        }

        Analytics.getInstance().adRevenueEvent(bundle)
        Analytics.getInstance().adImpressionEvent(bundle)     
    }
}