name: Deploy PROD Build To Slack Channel

on:
  repository_dispatch:
    types: [prod-deploy-to-slack-channel]

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      BRANCH_NAME: ${{ github.event.client_payload.branch || github.ref_name }}

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.client_payload.branch }}

      - name: <PERSON><PERSON> Flutter SDK
        uses: actions/cache@v2
        with:
          path: ~/flutter
          key: ${{ runner.os }}-flutter-${{ hashFiles('**/pubspec.yaml') }}
          restore-keys: |
            ${{ runner.os }}-flutter-

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          java-version: "17.x"
          distribution: "temurin"

      - name: Get flutter
        uses: subosito/flutter-action@v1
        with:
          flutter-version: "3.22.2"
          channel: stable

      - name: Set up google-services.json
        run: |
          echo "${{ secrets.PROD_GOOGLE_SERVICES_JSON }}" | jq '.' > android/app/google-services.json
          cat android/app/google-services.json


      - name: Get current date and time
        run: |
          CURRENT_DATE_TIME=$(date '+%Y%m%d_%H%M%S')
          echo "Current Date and Time: $CURRENT_DATE_TIME"
          echo "DATE_TIME=$CURRENT_DATE_TIME" >> $GITHUB_ENV

      - name: Build flutter app
        run: |
          sudo apt-get update -y
          sudo apt-get install -y ninja-build libgtk-3-dev
          dart --version
          flutter --version
          flutter clean
          flutter packages pub get
          cp config/.env.example config/prod.env;
          flutter packages pub run build_runner build --delete-conflicting-outputs;
          flutter build apk --dart-define ENV=prod --flavor prod --no-tree-shake-icons

      - name: Zip creation of build
        uses: vimtor/action-zip@v1
        with:
          files: ./build/app/outputs/flutter-apk/app-prod-release.apk
          dest: prod_${{env.BRANCH_NAME}}_${{env.DATE_TIME}}.zip

      - name: Upload to slack channel
        uses: adrey/slack-file-upload-action@master
        with:
          token: ${{ secrets.SLACK_BOT_TOKEN }}
          path: ./prod_${{env.BRANCH_NAME}}_${{env.DATE_TIME}}.zip
          channel: sl-worde-builds
