PODS:
  - AppAuth (1.6.2):
    - AppAuth/Core (= 1.6.2)
    - AppAuth/ExternalUserAgent (= 1.6.2)
  - AppAuth/Core (1.6.2)
  - AppAuth/ExternalUserAgent (1.6.2):
    - AppAuth/Core
  - cloud_firestore (4.8.4):
    - Firebase/Firestore (= 10.12.0)
    - firebase_core
    - Flutter
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - cloud_functions (4.3.4):
    - Firebase/Functions (= 10.12.0)
    - firebase_core
    - Flutter
  - FBAEMKit (14.1.0):
    - FBSDKCoreKit_Basics (= 14.1.0)
  - FBSDKCoreKit (14.1.0):
    - FBAEMKit (= 14.1.0)
    - FBSDKCoreKit_Basics (= 14.1.0)
  - FBSDKCoreKit_Basics (14.1.0)
  - FBSDKLoginKit (14.1.0):
    - FBSDKCoreKit (= 14.1.0)
  - Firebase/Analytics (10.12.0):
    - Firebase/Core
  - Firebase/Auth (10.12.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.12.0)
  - Firebase/Core (10.12.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.12.0)
  - Firebase/CoreOnly (10.12.0):
    - FirebaseCore (= 10.12.0)
  - Firebase/Crashlytics (10.12.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.12.0)
  - Firebase/Database (10.12.0):
    - Firebase/CoreOnly
    - FirebaseDatabase (~> 10.12.0)
  - Firebase/Firestore (10.12.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 10.12.0)
  - Firebase/Functions (10.12.0):
    - Firebase/CoreOnly
    - FirebaseFunctions (~> 10.12.0)
  - Firebase/InAppMessaging (10.12.0):
    - Firebase/CoreOnly
    - FirebaseInAppMessaging (~> 10.12.0-beta)
  - Firebase/Messaging (10.12.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.12.0)
  - Firebase/RemoteConfig (10.12.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 10.12.0)
  - firebase_analytics (10.4.4):
    - Firebase/Analytics (= 10.12.0)
    - firebase_core
    - Flutter
  - firebase_app_check (0.1.1-5):
    - Firebase/CoreOnly (~> 10.12.0)
    - firebase_core
    - FirebaseAppCheck (~> 10.12.0-beta)
    - Flutter
  - firebase_auth (4.1.3):
    - Firebase/Auth (= 10.12.0)
    - firebase_core
    - Flutter
  - firebase_core (2.15.0):
    - Firebase/CoreOnly (= 10.12.0)
    - Flutter
  - firebase_crashlytics (3.3.4):
    - Firebase/Crashlytics (= 10.12.0)
    - firebase_core
    - Flutter
  - firebase_database (10.0.6):
    - Firebase/Database (= 10.12.0)
    - firebase_core
    - Flutter
  - firebase_in_app_messaging (0.7.3-4):
    - Firebase/InAppMessaging (= 10.12.0)
    - firebase_core
    - Flutter
  - firebase_messaging (14.6.5):
    - Firebase/Messaging (= 10.12.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (3.0.6):
    - Firebase/RemoteConfig (= 10.12.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (10.13.0):
    - FirebaseCore (~> 10.0)
  - FirebaseAnalytics (10.12.0):
    - FirebaseAnalytics/AdIdSupport (= 10.12.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.12.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.12.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAppCheck (10.12.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseAppCheckInterop (10.13.0)
  - FirebaseAuth (10.12.0):
    - FirebaseAppCheckInterop (~> 10.0)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
  - FirebaseAuthInterop (10.13.0)
  - FirebaseCore (10.12.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreExtension (10.13.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.13.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.12.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseDatabase (10.12.0):
    - FirebaseCore (~> 10.0)
    - leveldb-library (~> 1.22)
  - FirebaseFirestore (10.12.0):
    - FirebaseFirestore/AutodetectLeveldb (= 10.12.0)
  - FirebaseFirestore/AutodetectLeveldb (10.12.0):
    - FirebaseFirestore/Base
    - FirebaseFirestore/WithLeveldb
  - FirebaseFirestore/Base (10.12.0)
  - FirebaseFirestore/WithLeveldb (10.12.0):
    - FirebaseFirestore/Base
  - FirebaseFunctions (10.12.0):
    - FirebaseAppCheckInterop (~> 10.10)
    - FirebaseAuthInterop (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseMessagingInterop (~> 10.0)
    - FirebaseSharedSwift (~> 10.0)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
  - FirebaseInAppMessaging (10.12.0-beta):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseInstallations (10.13.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.12.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseMessagingInterop (10.13.0)
  - FirebaseRemoteConfig (10.12.0):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseSessions (10.13.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.10)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (10.13.0)
  - Flutter (1.0.0)
  - flutter_facebook_auth (4.4.1):
    - FBSDKLoginKit (= 14.1.0)
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - Google-Mobile-Ads-SDK (10.4.0):
    - GoogleAppMeasurement (< 11.0, >= 7.0)
    - GoogleUserMessagingPlatform (>= 1.1)
  - google_mobile_ads (1.0.0):
    - Flutter
    - Google-Mobile-Ads-SDK (~> 10.4.0)
    - webview_flutter_wkwebview
  - google_sign_in_ios (0.0.1):
    - Flutter
    - GoogleSignIn (~> 6.2)
  - GoogleAppMeasurement (10.12.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.12.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.12.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.12.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.12.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.2.5):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleSignIn (6.2.4):
    - AppAuth (~> 1.5)
    - GTMAppAuth (~> 1.3)
    - GTMSessionFetcher/Core (< 3.0, >= 1.1)
  - GoogleUserMessagingPlatform (2.1.0)
  - GoogleUtilities/AppDelegateSwizzler (7.11.5):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.11.5):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.11.5):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.11.5):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.11.5):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.11.5)"
  - GoogleUtilities/Reachability (7.11.5):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.11.5):
    - GoogleUtilities/Logger
  - GTMAppAuth (1.3.1):
    - AppAuth/Core (~> 1.6)
    - GTMSessionFetcher/Core (< 3.0, >= 1.5)
  - GTMSessionFetcher/Core (2.3.0)
  - in_app_purchase_storekit (0.0.1):
    - Flutter
  - in_app_review (0.2.0):
    - Flutter
  - leveldb-library (1.22.2)
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - path_provider_ios (0.0.1):
    - Flutter
  - permission_handler_apple (9.0.4):
    - Flutter
  - PromisesObjC (2.3.1)
  - PromisesSwift (2.3.1):
    - PromisesObjC (= 2.3.1)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_ios (0.0.1):
    - Flutter
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite (0.0.2):
    - Flutter
    - FMDB (>= 2.7.5)
  - twitter_login (0.0.1):
    - Flutter
  - uni_links (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - cloud_functions (from `.symlinks/plugins/cloud_functions/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_app_check (from `.symlinks/plugins/firebase_app_check/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_database (from `.symlinks/plugins/firebase_database/ios`)
  - firebase_in_app_messaging (from `.symlinks/plugins/firebase_in_app_messaging/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - FirebaseFirestore (from `https://github.com/invertase/firestore-ios-sdk-frameworks.git`, tag `10.12.0`)
  - Flutter (from `Flutter`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - google_mobile_ads (from `.symlinks/plugins/google_mobile_ads/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/ios`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - path_provider_ios (from `.symlinks/plugins/path_provider_ios/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_ios (from `.symlinks/plugins/shared_preferences_ios/ios`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - twitter_login (from `.symlinks/plugins/twitter_login/ios`)
  - uni_links (from `.symlinks/plugins/uni_links/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - AppAuth
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseAppCheck
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseDatabase
    - FirebaseFunctions
    - FirebaseInAppMessaging
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseMessagingInterop
    - FirebaseRemoteConfig
    - FirebaseSessions
    - FirebaseSharedSwift
    - FMDB
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - leveldb-library
    - nanopb
    - PromisesObjC
    - PromisesSwift

EXTERNAL SOURCES:
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  cloud_functions:
    :path: ".symlinks/plugins/cloud_functions/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_app_check:
    :path: ".symlinks/plugins/firebase_app_check/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_database:
    :path: ".symlinks/plugins/firebase_database/ios"
  firebase_in_app_messaging:
    :path: ".symlinks/plugins/firebase_in_app_messaging/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 10.12.0
  Flutter:
    :path: Flutter
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  google_mobile_ads:
    :path: ".symlinks/plugins/google_mobile_ads/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/ios"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  path_provider_ios:
    :path: ".symlinks/plugins/path_provider_ios/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_ios:
    :path: ".symlinks/plugins/shared_preferences_ios/ios"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  twitter_login:
    :path: ".symlinks/plugins/twitter_login/ios"
  uni_links:
    :path: ".symlinks/plugins/uni_links/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

CHECKOUT OPTIONS:
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 10.12.0

SPEC CHECKSUMS:
  AppAuth: 3bb1d1cd9340bd09f5ed189fb00b1cc28e1e8570
  cloud_firestore: 005e157ad342dbfb2e461cb111a9020aa71bfb22
  cloud_functions: 3c404e6abc226da2c7a5a3f2b67678d1c60f3e3d
  FBAEMKit: a899515e45476027f73aef377b5cffadcd56ca3a
  FBSDKCoreKit: 24f8bc8d3b5b2a8c5c656a1329492a12e8efa792
  FBSDKCoreKit_Basics: 6e578c9bdc7aa1365dbbbde633c9ebb536bcaa98
  FBSDKLoginKit: 787de205d524c3a4b17d527916f1d066e4361660
  Firebase: 07150e75d142fb9399f6777fa56a187b17f833a0
  firebase_analytics: 3ff822ee2e90f95b61f0da300df20b378d380fbb
  firebase_app_check: e39ec486e57ecaabaff3794e42ddc0a614ae3fab
  firebase_auth: a22e852e46f2068c48fd8bcf32f1476d3ac60ffd
  firebase_core: e477125798fc37cd4ab43ca6a8536bf7e0929c00
  firebase_crashlytics: 6043ce85800f96e53f15ee5051f9cfad10cce73d
  firebase_database: 3c85e3d5a3a651540c945d2d9ab18285e85aba73
  firebase_in_app_messaging: ff7b654464edcaf3b1a4adbe4ac4a655814b123c
  firebase_messaging: 334d68c3a36b6d4d5cd91e4f42509e0d4ae49828
  firebase_remote_config: 72364b8d40fa81862bd59166ac37301899f770f1
  FirebaseABTesting: 86ac5a4fc749088bb4d55a1cbfb2c4cb42c6d5de
  FirebaseAnalytics: 0270389efbe3022b54ec4588862dabec3477ee98
  FirebaseAppCheck: c5e49dadf9c9cbabf0066074af938e032a0cce48
  FirebaseAppCheckInterop: 5e12dc623d443dedffcde9c6f3ed41510125d8ef
  FirebaseAuth: a66c1e14ec58f41d154a4b41ce1a23ea00ad4805
  FirebaseAuthInterop: 74875bde5d15636522a8fe98beb561df7a54db58
  FirebaseCore: f86a1394906b97ac445ae49c92552a9425831bed
  FirebaseCoreExtension: ce60f9db46d83944cf444664d6d587474128eeca
  FirebaseCoreInternal: b342e37cd4f5b4454ec34308f073420e7920858e
  FirebaseCrashlytics: c4d111b7430c49744c74bcc6346ea00868661ac8
  FirebaseDatabase: 6d998d7ef2c1723b3e798a85e61a7dc0504b1ea0
  FirebaseFirestore: 8d9dd05bb50e0891ebe4fdd9bd0f9b9d22ea5556
  FirebaseFunctions: d49c7920b289d85029882927e4056ad04a906e19
  FirebaseInAppMessaging: dc24f50aebaf81a377f0b8abf360778f94208931
  FirebaseInstallations: b28af1b9f997f1a799efe818c94695a3728c352f
  FirebaseMessaging: bb2c4f6422a753038fe137d90ae7c1af57251316
  FirebaseMessagingInterop: 593e501af43b6d8df45d7323a0803d496b179ba3
  FirebaseRemoteConfig: bc7f260e6596956fafbb532443c19bd3c30f5258
  FirebaseSessions: 991fb4c20b3505eef125f7cbfa20a5b5b189c2a4
  FirebaseSharedSwift: 5c4906ecf0441ed23efff399454dc791eff8ad54
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_facebook_auth: 361ac7a57263ebf327f26089507ead0d66558ee8
  flutter_local_notifications: 0c0b1ae97e741e1521e4c1629a459d04b9aec743
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  Google-Mobile-Ads-SDK: 32fe7836431a06a29f7734ae092b600137c8108d
  google_mobile_ads: 53b1f0d74445963e5810e34ac38dfb27aabe278e
  google_sign_in_ios: 4f85eb9f937450765c8573bb85fd8cd6a5af675c
  GoogleAppMeasurement: 2d800fab85e7848b1e66a6f8ce5bca06c5aad892
  GoogleDataTransport: 54dee9d48d14580407f8f5fbf2f496e92437a2f2
  GoogleSignIn: 5651ce3a61e56ca864160e79b484cd9ed3f49b7a
  GoogleUserMessagingPlatform: dce302b8f1b84d6e945812ee7a15c3f65a102cbf
  GoogleUtilities: 13e2c67ede716b8741c7989e26893d151b2b2084
  GTMAppAuth: 0ff230db599948a9ad7470ca667337803b3fc4dd
  GTMSessionFetcher: 3a63d75eecd6aa32c2fc79f578064e1214dfdec2
  in_app_purchase_storekit: d7fcf4646136ec258e237872755da8ea6c1b6096
  in_app_review: 4a97249f7a2f539a0f294c2d9196b7fe35e49541
  leveldb-library: f03246171cce0484482ec291f88b6d563699ee06
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  path_provider_ios: 14f3d2fd28c4fdb42f44e0f751d12861c43cee02
  permission_handler_apple: 44366e37eaf29454a1e7b1b7d736c2cceaeb17ce
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4
  PromisesSwift: 28dca69a9c40779916ac2d6985a0192a5cb4a265
  share_plus: 056a1e8ac890df3e33cb503afffaf1e9b4fbae68
  shared_preferences_ios: 548a61f8053b9b8a49ac19c1ffbc8b92c50d68ad
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  sqflite: 6d358c025f5b867b29ed92fc697fd34924e11904
  twitter_login: 2794db69b7640681171b17b3c2c84ad9dfb4a57f
  uni_links: d97da20c7701486ba192624d99bffaaffcfc298a
  url_launcher_ios: 839c58cdb4279282219f5e248c3321761ff3c4de
  webview_flutter_wkwebview: 2e2d318f21a5e036e2c3f26171342e95908bd60a

PODFILE CHECKSUM: 624a6e65ae35338d2fd983169632523860cebd1d

COCOAPODS: 1.12.1
